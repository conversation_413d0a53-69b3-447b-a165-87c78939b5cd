import os
import ezdxf
from ezdxf import recover
from ezdxf.filemanagement import readfile
import pandas as pd
import numpy as np
import json
from shapely.geometry import Polygon, LineString
import rtree.index as index
import sys
import re
import math
import hashlib
from collections import defaultdict

# 导入专业DXF读取器
try:
    from professional_dxf_reader import ProfessionalDXFReader
    PROFESSIONAL_READER_AVAILABLE = True
except ImportError:
    PROFESSIONAL_READER_AVAILABLE = False
    print("⚠️ 专业DXF读取器不可用，将使用传统方法")

# 导入线段合并工具
try:
    from line_merger import DXFLineMerger
    LINE_MERGER_AVAILABLE = True
except ImportError:
    LINE_MERGER_AVAILABLE = False
    print("⚠️ 线段合并工具不可用，将跳过线段合并处理")

# 导入重叠线条合并器
try:
    from overlapping_line_merger import OverlappingLineMerger
    OVERLAPPING_MERGER_AVAILABLE = True
except ImportError:
    OVERLAPPING_MERGER_AVAILABLE = False
    print("⚠️ 重叠线条合并器不可用")


class TransformationMatrix:
    """统一的几何变换矩阵"""
    def __init__(self, base_point, rotation, scale):
        self.base_point = base_point
        self.rotation = rotation
        self.scale = scale

        # 创建3x3变换矩阵
        cos_r = math.cos(math.radians(rotation))
        sin_r = math.sin(math.radians(rotation))

        # 组合变换矩阵：先缩放，再旋转，最后平移
        self.matrix = np.array([
            [cos_r * scale[0], -sin_r * scale[1], base_point.x],
            [sin_r * scale[0], cos_r * scale[1], base_point.y],
            [0, 0, 1]
        ])

    def apply(self, point):
        """应用变换到点"""
        # 创建齐次坐标点
        homogenous_point = np.array([point[0], point[1], 1])

        # 应用变换
        transformed = np.dot(self.matrix, homogenous_point)
        return (transformed[0], transformed[1])

    def is_mirrored(self):
        """检测是否包含镜像变换"""
        # 计算2x2子矩阵的行列式
        det = self.matrix[0][0] * self.matrix[1][1] - self.matrix[0][1] * self.matrix[1][0]
        return det < 0


class GeometricReconstructor:
    """几何重建器 - 基于几何点变换重建圆弧和椭圆"""

    def __init__(self, tolerance=1e-6):
        self.tolerance = tolerance

    def sample_arc_points(self, center, radius, start_angle, end_angle, num_samples=9):
        """在圆弧上采样关键点"""
        points = []

        # 确保角度范围正确
        if end_angle < start_angle:
            end_angle += 360

        # 采样点：起点、终点、中点，以及均匀分布的其他点
        angles = np.linspace(math.radians(start_angle), math.radians(end_angle), num_samples)

        for angle in angles:
            x = center[0] + radius * math.cos(angle)
            y = center[1] + radius * math.sin(angle)
            points.append((x, y, angle))  # 保存原始角度信息

        return points

    def sample_ellipse_points(self, center, major_axis, minor_axis, rotation, start_param, end_param, num_samples=9):
        """在椭圆上采样关键点"""
        points = []

        # 椭圆参数方程
        cos_rot = math.cos(math.radians(rotation))
        sin_rot = math.sin(math.radians(rotation))

        # 确保参数范围正确
        if end_param < start_param:
            end_param += 2 * math.pi

        params = np.linspace(start_param, end_param, num_samples)

        for param in params:
            # 椭圆上的点（未旋转）
            x_local = major_axis * math.cos(param)
            y_local = minor_axis * math.sin(param)

            # 应用旋转
            x = center[0] + x_local * cos_rot - y_local * sin_rot
            y = center[1] + x_local * sin_rot + y_local * cos_rot

            points.append((x, y, param))  # 保存原始参数

        return points

    def reconstruct_arc_from_points(self, transformed_points):
        """从变换后的点重建圆弧参数"""
        if len(transformed_points) < 3:
            return None

        # 提取点坐标
        points = [(p[0], p[1]) for p in transformed_points]

        # 使用最小二乘法拟合圆
        center, radius = self._fit_circle_least_squares(points)

        if center is None or radius <= 0:
            return None

        # 计算起始和结束角度
        start_point = points[0]
        end_point = points[-1]

        start_angle = self._calculate_angle(center, start_point)
        end_angle = self._calculate_angle(center, end_point)

        # 确定角度方向（通过检查中间点）
        if len(points) >= 3:
            mid_point = points[len(points) // 2]
            mid_angle = self._calculate_angle(center, mid_point)

            # 调整角度范围确保正确的弧线方向
            start_angle, end_angle = self._adjust_arc_angles(start_angle, end_angle, mid_angle)

        return {
            'type': 'ARC',
            'center': center,
            'radius': radius,
            'start_angle': start_angle,
            'end_angle': end_angle
        }

    def reconstruct_ellipse_from_points(self, transformed_points):
        """从变换后的点重建椭圆参数"""
        if len(transformed_points) < 5:
            return None

        # 提取点坐标
        points = [(p[0], p[1]) for p in transformed_points]

        # 拟合椭圆
        ellipse_params = self._fit_ellipse_least_squares(points)

        if ellipse_params is None:
            return None

        center, major_axis, minor_axis, rotation_angle = ellipse_params

        # 计算起始和结束参数
        start_point = points[0]
        end_point = points[-1]

        start_param = self._calculate_ellipse_parameter(center, major_axis, minor_axis, rotation_angle, start_point)
        end_param = self._calculate_ellipse_parameter(center, major_axis, minor_axis, rotation_angle, end_point)

        return {
            'type': 'ELLIPSE',
            'center': center,
            'major_axis': major_axis,
            'minor_axis': minor_axis,
            'rotation': rotation_angle,
            'start_param': start_param,
            'end_param': end_param
        }

    def detect_geometry_type(self, transformed_points, original_type='ARC'):
        """检测变换后的几何类型"""
        if len(transformed_points) < 3:
            return original_type

        points = [(p[0], p[1]) for p in transformed_points]

        # 尝试拟合圆
        center, radius = self._fit_circle_least_squares(points)

        if center is not None and radius > 0:
            # 计算拟合误差
            circle_error = self._calculate_circle_fitting_error(points, center, radius)

            # 如果圆拟合误差小，认为是圆弧
            if circle_error < self.tolerance * 10:
                return 'ARC'

        # 尝试拟合椭圆
        ellipse_params = self._fit_ellipse_least_squares(points)

        if ellipse_params is not None:
            ellipse_error = self._calculate_ellipse_fitting_error(points, ellipse_params)

            # 如果椭圆拟合误差小，认为是椭圆
            if ellipse_error < self.tolerance * 10:
                return 'ELLIPSE'

        # 默认返回原始类型
        return original_type

    def _fit_circle_least_squares(self, points):
        """使用最小二乘法拟合圆"""
        if len(points) < 3:
            return None, None

        # 转换为numpy数组
        points_array = np.array(points)
        x = points_array[:, 0]
        y = points_array[:, 1]

        # 构建矩阵方程 Ax = b
        # 圆的方程: (x-a)² + (y-b)² = r²
        # 展开: x² + y² - 2ax - 2by + (a² + b² - r²) = 0
        # 重写为: 2ax + 2by + c = x² + y²

        A = np.column_stack([2*x, 2*y, np.ones(len(x))])
        b = x**2 + y**2

        try:
            # 求解最小二乘
            params, residuals, rank, s = np.linalg.lstsq(A, b, rcond=None)

            if len(params) < 3:
                return None, None

            center_x = params[0]
            center_y = params[1]
            c = params[2]

            # 计算半径
            radius = math.sqrt(center_x**2 + center_y**2 + c)

            if radius <= 0:
                return None, None

            return (center_x, center_y), radius

        except np.linalg.LinAlgError:
            return None, None

    def _fit_ellipse_least_squares(self, points):
        """使用最小二乘法拟合椭圆"""
        if len(points) < 5:
            return None

        # 椭圆的一般方程: Ax² + Bxy + Cy² + Dx + Ey + F = 0
        # 约束条件: B² - 4AC < 0 (椭圆条件)

        points_array = np.array(points)
        x = points_array[:, 0]
        y = points_array[:, 1]

        # 构建设计矩阵
        D = np.column_stack([x**2, x*y, y**2, x, y, np.ones(len(x))])

        try:
            # 使用SVD求解
            U, S, Vt = np.linalg.svd(D)
            params = Vt[-1, :]  # 最小奇异值对应的解

            A, B, C, D_coef, E, F = params

            # 检查椭圆条件
            discriminant = B**2 - 4*A*C
            if discriminant >= 0:
                return None  # 不是椭圆

            # 计算椭圆参数
            center, major_axis, minor_axis, rotation = self._ellipse_params_from_general(A, B, C, D_coef, E, F)

            return center, major_axis, minor_axis, rotation

        except (np.linalg.LinAlgError, ValueError):
            return None

    def _ellipse_params_from_general(self, A, B, C, D, E, F):
        """从椭圆一般方程参数计算几何参数"""
        # 计算中心
        denominator = B**2 - 4*A*C
        center_x = (2*C*D - B*E) / denominator
        center_y = (2*A*E - B*D) / denominator

        # 计算旋转角度
        if abs(B) < 1e-10:
            rotation = 0 if A < C else math.pi/2
        else:
            rotation = 0.5 * math.atan2(B, A - C)

        # 计算长短轴
        cos_rot = math.cos(rotation)
        sin_rot = math.sin(rotation)

        # 变换到主轴坐标系
        A_rot = A * cos_rot**2 + B * cos_rot * sin_rot + C * sin_rot**2
        C_rot = A * sin_rot**2 - B * cos_rot * sin_rot + C * cos_rot**2

        # 计算常数项
        F_rot = F + A * center_x**2 + B * center_x * center_y + C * center_y**2 + D * center_x + E * center_y

        # 计算半轴长度
        if A_rot <= 0 or C_rot <= 0 or F_rot >= 0:
            return (center_x, center_y), 1, 1, math.degrees(rotation)

        major_axis = math.sqrt(-F_rot / A_rot)
        minor_axis = math.sqrt(-F_rot / C_rot)

        # 确保major_axis >= minor_axis
        if major_axis < minor_axis:
            major_axis, minor_axis = minor_axis, major_axis
            rotation += math.pi/2

        return (center_x, center_y), major_axis, minor_axis, math.degrees(rotation)

    def _calculate_angle(self, center, point):
        """计算点相对于中心的角度（度）"""
        dx = point[0] - center[0]
        dy = point[1] - center[1]
        angle = math.degrees(math.atan2(dy, dx))
        return angle % 360

    def _adjust_arc_angles(self, start_angle, end_angle, mid_angle):
        """调整圆弧角度确保正确的方向"""
        # 确保角度在0-360范围内
        start_angle = start_angle % 360
        end_angle = end_angle % 360
        mid_angle = mid_angle % 360

        # 检查中间角度是否在起始和结束角度之间
        def angle_between(angle, start, end):
            if start <= end:
                return start <= angle <= end
            else:  # 跨越0度
                return angle >= start or angle <= end

        # 如果中间角度不在范围内，调整结束角度
        if not angle_between(mid_angle, start_angle, end_angle):
            if end_angle < start_angle:
                end_angle += 360

        # 确保角度差在合理范围内（0-360度）
        angle_diff = (end_angle - start_angle) % 360
        if angle_diff > 180:  # 如果角度差大于180度，可能是方向错误
            # 交换起始和结束角度
            start_angle, end_angle = end_angle % 360, start_angle
            if end_angle < start_angle:
                end_angle += 360

        return start_angle, end_angle

    def _calculate_ellipse_parameter(self, center, major_axis, minor_axis, rotation_angle, point):
        """计算点在椭圆上的参数"""
        # 将点转换到椭圆的局部坐标系
        cos_rot = math.cos(math.radians(rotation_angle))
        sin_rot = math.sin(math.radians(rotation_angle))

        # 平移到椭圆中心
        dx = point[0] - center[0]
        dy = point[1] - center[1]

        # 旋转到椭圆主轴坐标系
        x_local = dx * cos_rot + dy * sin_rot
        y_local = -dx * sin_rot + dy * cos_rot

        # 计算参数
        param = math.atan2(y_local / minor_axis, x_local / major_axis)
        return param

    def _calculate_circle_fitting_error(self, points, center, radius):
        """计算圆拟合误差"""
        total_error = 0
        for point in points:
            distance = math.sqrt((point[0] - center[0])**2 + (point[1] - center[1])**2)
            error = abs(distance - radius)
            total_error += error**2

        return math.sqrt(total_error / len(points))

    def _calculate_ellipse_fitting_error(self, points, ellipse_params):
        """计算椭圆拟合误差"""
        center, major_axis, minor_axis, rotation_angle = ellipse_params

        cos_rot = math.cos(math.radians(rotation_angle))
        sin_rot = math.sin(math.radians(rotation_angle))

        total_error = 0
        for point in points:
            # 转换到椭圆局部坐标系
            dx = point[0] - center[0]
            dy = point[1] - center[1]

            x_local = dx * cos_rot + dy * sin_rot
            y_local = -dx * sin_rot + dy * cos_rot

            # 计算椭圆方程的值
            ellipse_value = (x_local / major_axis)**2 + (y_local / minor_axis)**2
            error = abs(ellipse_value - 1)
            total_error += error**2

        return math.sqrt(total_error / len(points))


class CADDataProcessor:
    """核心数据处理类 - 无状态设计"""

    def __init__(self):
        # 停止控制标志
        self.should_stop = False

        # 初始化专业DXF读取器
        if PROFESSIONAL_READER_AVAILABLE:
            self.professional_reader = ProfessionalDXFReader()
            print("✅ 专业DXF读取器已启用 - 基于坐标系手性检测")
        else:
            self.professional_reader = None
            print("⚠️ 使用传统DXF读取方法")

        # 初始化重叠线条合并器
        if OVERLAPPING_MERGER_AVAILABLE:
            self.overlapping_merger = OverlappingLineMerger()
            print("✅ 重叠线条合并器已启用 - 门窗图层重叠线条合并")
        else:
            self.overlapping_merger = None
            print("⚠️ 重叠线条合并器不可用")

        # 初始化线段合并器（启用迭代合并）
        if LINE_MERGER_AVAILABLE:
            self.line_merger = DXFLineMerger(
                distance_threshold=5,
                angle_threshold=2,
                enable_iterative=True,
                max_iterations=3
            )
            print("✅ 线段合并器已启用 - 墙体图层线段简化处理（迭代模式）")
        else:
            self.line_merger = None
            print("⚠️ 线段合并器不可用")

        # 🔧 步骤1：线型处理系统初始化
        self.linetype_dict = {}  # 线型名称到线型实体的映射
        self.layer_linetype_mapping = {}  # 图层到线型的映射
        self.dxf_version = None  # DXF版本信息
        self.global_ltscale = 1.0  # 全局线型比例
        self.psltscale = 1.0  # 图纸空间线型比例

        # 特征列定义
        self.feature_columns = [
            'length', 'area', 'closed', 'line_count', 'arc_count',
            'circle_count', 'ellipse_count', 'insert_count', 'text_count', 'dimension_count',
            'min_x', 'min_y', 'max_x', 'max_y', 'aspect_ratio',
            'layer_hash', 'text_length', 'text_angle',
            # 新增特征工程增强的特征
            'rectangularity', 'symmetry_score', 'text_keywords', 'color_variance', 'line_angle_variance',
            'feature_hash'
        ]
        
        # 类别映射
        self.category_mapping = {
            'wall': '墙体',
            'door': '门',
            'door_window': '门窗',  # 添加门窗组合类别
            'window': '窗户',
            'bay_window': '飘窗',
            'stair': '楼梯',
            'elevator': '电梯',
            'railing': '栏杆',
            'furniture': '家具',
            'sofa': '沙发',
            'bed': '床',
            'cabinet': '柜子',
            'chair': '椅子',
            'dining_table': '餐桌',
            'appliance': '家电',
            'bathroom_fixture': '洁具',
            'kitchen_fixture': '厨具',
            'dimension': '尺寸标注',
            'title_block': '图框',
            'room_label': '房间标注',
            'column': '柱子',
            'other': '其他'
        }
        
        # 自动标记的图层模式（扩展版）
        self.wall_layer_patterns = [
            # 中文墙体模式
            '墙', '墙体', '结构墙', '承重墙', '隔墙', '内墙', '外墙', '分隔墙', '主墙', '次墙',
            # 英文墙体模式
            'wall', 'walls', 'partition', 'struct', 'structure', 'bearing', 'load',
            'exterior', 'interior', 'divider', 'separator',
            # 常见CAD图层命名
            'A-WALL', 'AWALL', 'A_WALL', 'WALL_', '_WALL', 'STR-WALL', 'ARCH-WALL',
            'PARTITION', 'STRUCT-WALL', 'BEARING-WALL', 'LOAD-WALL',
            # 专业术语
            'masonry', 'concrete', 'brick', '砖墙', '混凝土', '砌体',
            # 图层编号模式
            '01-墙', '02-墙', 'L-墙', 'S-墙', 'A-墙', 'AR-墙'
        ]
        
        self.door_window_layer_patterns = [
            # 中文门窗模式
            '门', '窗', '门窗', '门洞', '窗洞', '开口', '洞口', '门套', '窗套',
            '入口', '出口', '通道口', '门扇', '窗扇',
            # 英文门窗模式
            'door', 'doors', 'window', 'windows', 'opening', 'openings',
            'entrance', 'exit', 'portal', 'aperture',
            # 常见CAD图层命名
            'A-DOOR', 'ADOOR', 'A_DOOR', 'DOOR_', '_DOOR',
            'A-WINDOW', 'AWINDOW', 'A_WINDOW', 'WINDOW_', '_WINDOW',
            'A-OPEN', 'AOPEN', 'OPENING', 'OPENINGS',
            # 专业术语
            'casement', 'sash', 'frame', 'jamb',
            # 图层编号模式
            '01-门', '02-窗', 'L-门', 'L-窗', 'A-门', 'A-窗', 'AR-门', 'AR-窗'
        ]
        
        self.railing_layer_patterns = [
            # 中文栏杆模式
            '栏杆', '护栏', '扶手', '栏板', '护板', '防护栏', '安全栏',
            '阳台栏杆', '楼梯栏杆', '走廊栏杆',
            # 英文栏杆模式
            'railing', 'railings', 'handrail', 'handrails', 'guard', 'guards',
            'fence', 'fencing', 'barrier', 'balustrade', 'parapet',
            # 常见CAD图层命名
            'A-RAIL', 'ARAIL', 'A_RAIL', 'RAIL_', '_RAIL',
            'A-GUARD', 'AGUARD', 'GUARD', 'HANDRAIL',
            'A-FENCE', 'AFENCE', 'FENCE', 'BARRIER',
            # 图层编号模式
            '01-栏杆', 'L-栏杆', 'A-栏杆', 'AR-栏杆'
        ]

    def stop_processing(self):
        """停止处理"""
        self.should_stop = True

    def reset_stop_flag(self):
        """重置停止标志"""
        self.should_stop = False

    def load_dxf_file(self, file_path):
        """加载DXF文件并返回实体列表"""
        try:
            # 尝试正常加载DXF文件
            try:
                doc = readfile(file_path)
                modelspace = doc.modelspace()
            except Exception:
                # 如果文件有结构错误，尝试使用recover模块
                doc, auditor = recover.readfile(file_path)
                if auditor.has_errors:
                    print(f"文件 {file_path} 有无法修复的错误")
                modelspace = doc.modelspace()

            # 🔧 步骤1：建立线型-图层映射关系（在处理实体之前）

            self._build_linetype_layer_mapping(doc)

            # 收集所有实体
            all_entities = []
            for entity in modelspace:
                entity_data = self._extract_entity_data(entity, doc)
                all_entities.append(entity_data)

            # 如果使用了专业DXF读取器，输出校正统计信息
            if self.professional_reader:
                stats = self.professional_reader.validate_handedness_correction(all_entities)
                if stats['corrected_arcs'] > 0 or stats['corrected_ellipses'] > 0:
                    self.professional_reader.print_correction_summary(stats)

            # 🔧 步骤3：线段合并处理（在图层识别和分组之前）
            if self.line_merger:
                print("🔧 开始墙体图层线段合并处理...")
                original_count = len(all_entities)
                all_entities = self.line_merger.process_entities(all_entities)
                final_count = len(all_entities)
                print(f"✅ 线段合并完成: {original_count} -> {final_count} 个实体")

                # 打印合并统计信息
                stats = self.line_merger.get_stats()
                if stats['merged_lines'] > 0:
                    print(f"📊 合并统计: 简化了 {stats['merged_lines']} 条线段")

            # 🔧 步骤4：重叠线条合并处理（在线段合并之后）
            if self.overlapping_merger:
                all_entities = self.overlapping_merger.process_entities(all_entities)

            return all_entities
            
        except Exception as e:
            print(f"无法加载文件 {file_path}: {str(e)}")
            return []
    
    def _extract_entity_data(self, entity, doc):
        """
        提取实体数据 - 使用专业DXF读取器进行坐标系手性校正

        这是核心方法的升级版本，采用基于坐标系手性检测的专业方案
        确保弧形线和椭圆线在分组过程中和显示中正确无误
        """
        # 使用专业DXF读取器（如果可用）
        if self.professional_reader:
            entity_data = self.professional_reader.extract_entity_data_with_handedness_correction(entity, doc)
        else:
            # 回退到传统方法
            entity_data = {
                'type': entity.dxftype(),
                'layer': entity.dxf.layer,
                'entity': entity
            }
            # 使用传统方法提取几何数据
            self._extract_geometry_data_traditional(entity, entity_data)

        # 🔧 步骤2：增强线型信息提取（支持块参照继承）
        linetype_info = self._extract_enhanced_linetype_info(entity)
        entity_data.update(linetype_info)

        # 提取颜色信息
        try:
            color = getattr(entity.dxf, 'color', 256)  # 256表示BYLAYER
            entity_data['color'] = color
        except:
            entity_data['color'] = 256

        return entity_data

    def _extract_geometry_data_traditional(self, entity, entity_data):
        """传统方法提取几何数据（回退方案）"""
        if entity.dxftype() == 'LINE':
            start = entity.dxf.start
            end = entity.dxf.end
            entity_data['points'] = [(start.x, start.y), (end.x, end.y)]
        elif entity.dxftype() in ('LWPOLYLINE', 'POLYLINE'):
            points = self._get_polyline_points(entity)
            entity_data['points'] = points
            entity_data['closed'] = getattr(entity.dxf, 'flags', 0) & 1 == 1
        elif entity.dxftype() == 'CIRCLE':
            center = entity.dxf.center
            entity_data['center'] = (center.x, center.y)
            entity_data['radius'] = entity.dxf.radius
        elif entity.dxftype() == 'ARC':
            # 传统方法：直接提取角度，不进行手性校正
            center = entity.dxf.center
            entity_data['center'] = (center.x, center.y)
            entity_data['radius'] = entity.dxf.radius
            entity_data['start_angle'] = entity.dxf.start_angle
            entity_data['end_angle'] = entity.dxf.end_angle
            print(f"⚠️ 使用传统方法读取圆弧，可能存在镜像显示问题")
        elif entity.dxftype() == 'ELLIPSE':
            # 传统方法：直接提取参数，不进行手性校正
            center = entity.dxf.center
            major_axis = entity.dxf.major_axis
            ratio = entity.dxf.ratio
            start_param = entity.dxf.start_param
            end_param = entity.dxf.end_param

            entity_data['center'] = (center.x, center.y)
            entity_data['major_axis'] = (major_axis.x, major_axis.y)
            entity_data['ratio'] = ratio
            entity_data['start_param'] = start_param
            entity_data['end_param'] = end_param
            print(f"⚠️ 使用传统方法读取椭圆，可能存在镜像显示问题")
        elif entity.dxftype() == 'INSERT':
            entity_data['block'] = entity.dxf.name
            insert = entity.dxf.insert
            entity_data['position'] = (insert.x, insert.y)
            entity_data['rotation'] = getattr(entity.dxf, 'rotation', 0)
            # 注意：传统方法不展开块引用，避免doc参数问题
        elif entity.dxftype() == 'TEXT':
            entity_data['text'] = entity.dxf.text
            insert = entity.dxf.insert
            entity_data['position'] = (insert.x, insert.y)
            entity_data['height'] = entity.dxf.height
            entity_data['rotation'] = getattr(entity.dxf, 'rotation', 0)
        elif entity.dxftype() == 'MTEXT':
            entity_data['text'] = getattr(entity, 'text', getattr(entity.dxf, 'text', ''))
            insert = entity.dxf.insert
            entity_data['position'] = (insert.x, insert.y)
            entity_data['height'] = entity.dxf.char_height
            entity_data['rotation'] = getattr(entity.dxf, 'rotation', 0)
        elif entity.dxftype() == 'DIMENSION':
            entity_data['dim_type'] = entity.dxf.dimtype
            entity_data['text'] = entity.dxf.text
            defpoints = []
            for attr in ['defpoint', 'defpoint2', 'defpoint3', 'defpoint4']:
                if hasattr(entity.dxf, attr):
                    dp = getattr(entity.dxf, attr)
                    defpoints.append((dp.x, dp.y))
            entity_data['def_points'] = defpoints
    
    def _get_polyline_points(self, entity):
        """获取多段线点"""
        points = []
        try:
            if entity.dxftype() == 'LWPOLYLINE':
                get_points_method = getattr(entity, 'get_points', None)
                lwpoints_attr = getattr(entity, 'lwpoints', None)
                if get_points_method and callable(get_points_method):
                    try:
                        point_result = get_points_method()
                        if hasattr(point_result, '__iter__'):
                            points = [(float(p[0]), float(p[1])) for p in point_result]
                    except (TypeError, AttributeError, IndexError):
                        points = []
                elif lwpoints_attr:
                    try:
                        points = [(p[0], p[1]) for p in lwpoints_attr]
                    except (TypeError, AttributeError):
                        points = []
            elif entity.dxftype() == 'POLYLINE':
                vertices_attr = getattr(entity, 'vertices', None)
                if vertices_attr:
                    vertices = list(vertices_attr)
                    points = [(v.dxf.location[0], v.dxf.location[1]) for v in vertices]
        except Exception:
            points = []
        return points
    
    def _expand_block(self, doc, block_entity, depth=0):
        """递归展开块引用"""
        if depth > 10:  # 防止无限递归
            return []
        
        expanded_entities = []
        try:
            block = doc.blocks.get(block_entity.dxf.name)
            if block:
                for entity_in_block in block:
                    if entity_in_block.dxftype() == 'INSERT':
                        # 递归展开嵌套块
                        nested = self._expand_block(doc, entity_in_block, depth+1)
                        expanded_entities.extend(nested)
                    else:
                        # 应用变换并添加实体
                        transformed = self._transform_entity(
                            entity_in_block,
                            block_entity.dxf.insert,
                            block_entity.dxf.rotation,
                            (getattr(block_entity.dxf, 'xscale', 1.0), 
                             getattr(block_entity.dxf, 'yscale', 1.0))
                        )
                        expanded_entities.append(transformed)
        except Exception as e:
            print(f"展开块引用时出错: {e}")
        return expanded_entities

    def _transform_arc(self, entity, matrix):
        """使用几何重建法处理圆弧变换"""
        center = entity.dxf.center
        radius = entity.dxf.radius
        start_angle = entity.dxf.start_angle
        end_angle = entity.dxf.end_angle

        # 创建几何重建器
        reconstructor = GeometricReconstructor()

        # 在原始圆弧上采样关键点
        sample_points = reconstructor.sample_arc_points(
            center=(center.x, center.y),
            radius=radius,
            start_angle=start_angle,
            end_angle=end_angle,
            num_samples=9  # 使用9个点确保精度
        )

        # 对所有采样点应用变换
        transformed_points = []
        for point in sample_points:
            transformed_point = matrix.apply((point[0], point[1]))
            transformed_points.append((transformed_point[0], transformed_point[1], point[2]))  # 保留原始角度信息

        # 检测变换后的几何类型
        geometry_type = reconstructor.detect_geometry_type(transformed_points, 'ARC')

        if geometry_type == 'ARC':
            # 重建圆弧
            result = reconstructor.reconstruct_arc_from_points(transformed_points)
            if result:


                print(f"几何重建圆弧: 中心({result['center'][0]:.2f},{result['center'][1]:.2f}), "
                      f"半径={result['radius']:.2f}, "
                      f"角度={result['start_angle']:.1f}°-{result['end_angle']:.1f}°")

                return result

        elif geometry_type == 'ELLIPSE':
            # 变换后变成椭圆，重建椭圆
            result = reconstructor.reconstruct_ellipse_from_points(transformed_points)
            if result:
                print(f"🔄 圆弧变换为椭圆: 中心({result['center'][0]:.2f},{result['center'][1]:.2f}), "
                      f"长轴={result['major_axis']:.2f}, 短轴={result['minor_axis']:.2f}, "
                      f"旋转={result['rotation']:.1f}°")

                return result

        # 如果重建失败，回退到原始方法
        print("⚠️ 几何重建失败，使用原始变换方法")
        return self._transform_arc_fallback(entity, matrix)

    def _transform_arc_fallback(self, entity, matrix):
        """圆弧变换的回退方法"""
        center = entity.dxf.center
        radius = entity.dxf.radius

        # 应用变换到圆心
        center_trans = matrix.apply((center.x, center.y))

        # 计算新半径（使用缩放因子的几何平均）
        scale_x, scale_y = matrix.scale
        new_radius = abs(radius * math.sqrt(abs(scale_x * scale_y)))

        # 简单的角度变换
        rotation = matrix.rotation
        new_start_angle = (entity.dxf.start_angle + rotation) % 360
        new_end_angle = (entity.dxf.end_angle + rotation) % 360

        # 处理镜像
        if matrix.is_mirrored():
            new_start_angle, new_end_angle = new_end_angle, new_start_angle

        return {
            'type': 'ARC',
            'center': center_trans,
            'radius': new_radius,
            'start_angle': new_start_angle,
            'end_angle': new_end_angle
        }

    def _transform_ellipse(self, entity, matrix):
        """使用几何重建法处理椭圆变换"""
        center = entity.dxf.center
        major_axis_vector = entity.dxf.major_axis
        ratio = entity.dxf.ratio
        start_param = entity.dxf.start_param if hasattr(entity.dxf, 'start_param') else 0
        end_param = entity.dxf.end_param if hasattr(entity.dxf, 'end_param') else 2 * math.pi

        # 计算椭圆参数
        major_axis_length = math.sqrt(major_axis_vector.x**2 + major_axis_vector.y**2)
        minor_axis_length = major_axis_length * ratio
        rotation_angle = math.degrees(math.atan2(major_axis_vector.y, major_axis_vector.x))

        # 创建几何重建器
        reconstructor = GeometricReconstructor()

        # 在原始椭圆上采样关键点
        sample_points = reconstructor.sample_ellipse_points(
            center=(center.x, center.y),
            major_axis=major_axis_length,
            minor_axis=minor_axis_length,
            rotation=rotation_angle,
            start_param=start_param,
            end_param=end_param,
            num_samples=12  # 椭圆需要更多点
        )

        # 对所有采样点应用变换
        transformed_points = []
        for point in sample_points:
            transformed_point = matrix.apply((point[0], point[1]))
            transformed_points.append((transformed_point[0], transformed_point[1], point[2]))

        # 检测变换后的几何类型
        geometry_type = reconstructor.detect_geometry_type(transformed_points, 'ELLIPSE')

        if geometry_type == 'ELLIPSE':
            # 重建椭圆
            result = reconstructor.reconstruct_ellipse_from_points(transformed_points)
            if result:
                print(f"几何重建椭圆: 中心({result['center'][0]:.2f},{result['center'][1]:.2f}), "
                      f"长轴={result['major_axis']:.2f}, 短轴={result['minor_axis']:.2f}, "
                      f"旋转={result['rotation']:.1f}°")

                return result

        elif geometry_type == 'ARC':
            # 椭圆变换为圆弧（均匀缩放情况）
            result = reconstructor.reconstruct_arc_from_points(transformed_points)
            if result:
                print(f"🔄 椭圆变换为圆弧: 中心({result['center'][0]:.2f},{result['center'][1]:.2f}), "
                      f"半径={result['radius']:.2f}")

                return result

        # 如果重建失败，回退到原始方法
        print("⚠️ 椭圆几何重建失败，使用原始变换方法")
        return self._transform_ellipse_fallback(entity, matrix)

    def _transform_ellipse_fallback(self, entity, matrix):
        """椭圆变换的回退方法"""
        center = entity.dxf.center
        major_axis_vector = entity.dxf.major_axis
        ratio = entity.dxf.ratio

        # 应用变换到中心
        center_trans = matrix.apply((center.x, center.y))

        # 变换长轴向量
        major_end_point = (center.x + major_axis_vector.x, center.y + major_axis_vector.y)
        major_end_trans = matrix.apply(major_end_point)

        # 计算新的长轴向量
        new_major_x = major_end_trans[0] - center_trans[0]
        new_major_y = major_end_trans[1] - center_trans[1]

        # 计算新的比例
        scale_x, scale_y = matrix.scale
        new_ratio = ratio * (scale_y / scale_x) if scale_x != 0 else ratio

        return {
            'type': 'ELLIPSE',
            'center': center_trans,
            'major_axis': (new_major_x, new_major_y),
            'ratio': abs(new_ratio),
            'start_param': getattr(entity.dxf, 'start_param', 0),
            'end_param': getattr(entity.dxf, 'end_param', 2 * math.pi)
        }

    def _transform_circle(self, entity, matrix):
        """使用变换矩阵处理圆形"""
        center = entity.dxf.center
        radius = entity.dxf.radius

        # 应用变换到圆心
        center_trans = matrix.apply((center.x, center.y))

        # 计算新半径（处理非均匀缩放）
        scale_x, scale_y = matrix.scale
        if matrix.is_mirrored():
            # 镜像变换时保持半径为正
            pass

        # 使用几何平均处理非均匀缩放
        new_radius = abs(radius * math.sqrt(abs(scale_x * scale_y)))

        return {
            'type': 'CIRCLE',
            'center': center_trans,
            'radius': new_radius
        }

    def _transform_entity(self, entity, base_point, rotation, scale):
        """应用几何变换到实体"""
        # 创建变换后的实体数据
        transformed = {
            'type': entity.dxftype(),
            'layer': entity.dxf.layer,
            'entity': entity
        }

        # 🔧 保留增强的线型信息
        linetype_info = self._extract_enhanced_linetype_info(entity)
        transformed.update(linetype_info)

        try:
            color = getattr(entity.dxf, 'color', 256)
            transformed['color'] = color
        except:
            transformed['color'] = 256
        
        # 创建变换矩阵
        matrix = TransformationMatrix(base_point, rotation, scale)

        # 应用变换
        cos_r = math.cos(math.radians(rotation))
        sin_r = math.sin(math.radians(rotation))
        scale_x, scale_y = scale

        if entity.dxftype() == 'LINE':
            start = entity.dxf.start
            end = entity.dxf.end
            
            # 变换起点和终点
            start_x = (start.x * cos_r - start.y * sin_r) * scale_x + base_point.x
            start_y = (start.x * sin_r + start.y * cos_r) * scale_y + base_point.y
            end_x = (end.x * cos_r - end.y * sin_r) * scale_x + base_point.x
            end_y = (end.x * sin_r + end.y * cos_r) * scale_y + base_point.y
            
            transformed['points'] = [(start_x, start_y), (end_x, end_y)]
            
        elif entity.dxftype() in ('LWPOLYLINE', 'POLYLINE'):
            points = self._get_polyline_points(entity)
            transformed_points = []
            for p in points:
                x = (p[0] * cos_r - p[1] * sin_r) * scale_x + base_point.x
                y = (p[0] * sin_r + p[1] * cos_r) * scale_y + base_point.y
                transformed_points.append((x, y))
            transformed['points'] = transformed_points
            transformed['closed'] = getattr(entity.dxf, 'flags', 0) & 1 == 1
            
        elif entity.dxftype() == 'CIRCLE':
            # 使用新的变换矩阵方法处理圆形
            circle_data = self._transform_circle(entity, matrix)
            transformed.update(circle_data)
            
        elif entity.dxftype() == 'ARC':
            # 使用几何重建法处理圆弧
            arc_data = self._transform_arc(entity, matrix)
            transformed.update(arc_data)
            
        elif entity.dxftype() == 'ELLIPSE':
            # 使用几何重建法处理椭圆
            ellipse_data = self._transform_ellipse(entity, matrix)
            transformed.update(ellipse_data)
            transformed['end_param'] = entity.dxf.end_param
            
        elif entity.dxftype() == 'TEXT':
            insert = entity.dxf.insert
            text_x = (insert.x * cos_r - insert.y * sin_r) * scale_x + base_point.x
            text_y = (insert.x * sin_r + insert.y * cos_r) * scale_y + base_point.y
            
            transformed['text'] = entity.dxf.text
            transformed['position'] = (text_x, text_y)
            transformed['height'] = entity.dxf.height * max(scale_x, scale_y)
            transformed['rotation'] = getattr(entity.dxf, 'rotation', 0) + rotation
        
        return transformed
    
    def _validate_ellipse_parameters(self, entity):
        """验证椭圆参数的有效性"""
        if 'center' not in entity or 'major_axis' not in entity:
            return False
        
        # 检查长轴向量是否为零向量
        major_x, major_y = entity['major_axis']
        if math.isclose(major_x, 0, abs_tol=1e-6) and math.isclose(major_y, 0, abs_tol=1e-6):
            print("警告: 椭圆长轴向量为零，使用默认值")
            entity['major_axis'] = (1.0, 0.0)  # 设置默认长轴
        
        # 检查比例是否有效
        ratio = entity.get('ratio', 1.0)
        if ratio <= 0 or ratio > 1.0:
            print(f"警告: 椭圆比例无效 ({ratio})，使用默认值1.0")
            entity['ratio'] = 1.0
        
        return True
    
    def group_entities(self, entities, distance_threshold=20, debug=False):
        """调整后的分组算法：特殊图层优先处理且严格隔离"""
        # 检查停止标志
        if self.should_stop:
            return []



        # 检查停止标志
        if self.should_stop:
            return []

        # 步骤1: 识别特殊图层（墙体、门窗、栏杆）
        wall_layers = self._detect_special_layers(entities, self.wall_layer_patterns, debug=False, layer_type="墙体")

        if self.should_stop:
            return []

        door_window_layers = self._detect_special_layers(entities, self.door_window_layer_patterns, debug=False, layer_type="门窗")

        if self.should_stop:
            return []

        railing_layers = self._detect_special_layers(entities, self.railing_layer_patterns, debug=False, layer_type="栏杆")

        # 合并所有特殊图层
        special_layers = wall_layers | door_window_layers | railing_layers
        
        all_groups = []
        processed_entities = set()
        
        # 步骤2: 优先处理墙体实体（精确端点连接）
        wall_entities = [e for e in entities if e['layer'] in wall_layers]
        if wall_entities:
            wall_groups = self._group_special_entities_by_layer(
                wall_entities, connection_threshold=5, entity_type="wall"
            )
            all_groups.extend(wall_groups)
            processed_entities.update(id(e) for e in wall_entities)

        
        # 步骤3: 处理门窗实体（端点连接 + 200单位容差）
        door_window_entities = [e for e in entities if e['layer'] in door_window_layers]
        if door_window_entities:
            door_window_groups = self._group_special_entities_by_layer(
                door_window_entities, connection_threshold=50, entity_type="door_window"
            )
            all_groups.extend(door_window_groups)
            processed_entities.update(id(e) for e in door_window_entities)

        
        # 步骤4: 处理栏杆实体（端点连接 + 200单位容差）
        railing_entities = [e for e in entities if e['layer'] in railing_layers]
        if railing_entities:
            railing_groups = self._group_special_entities_by_layer(
                railing_entities, connection_threshold=20, entity_type="railing"
            )
            all_groups.extend(railing_groups)
            processed_entities.update(id(e) for e in railing_entities)

        
        # 步骤5: 处理其他图层实体（优先同图层分组）
        other_entities = [e for e in entities if id(e) not in processed_entities]
        if other_entities:
            other_groups = self._group_other_entities_by_layer(other_entities, distance_threshold)
            all_groups.extend(other_groups)
            processed_entities.update(id(e) for e in other_entities)

        
        # 步骤6: 处理孤立的小实体（仅处理真正未分组的小实体）
        isolated_small_entities = self._find_isolated_small_entities(entities, all_groups)
        if isolated_small_entities:
            small_groups = self._group_isolated_small_entities(isolated_small_entities)
            all_groups.extend(small_groups)

        

        
        
        
        # 步骤7: 处理所有未分组的实体（通用分组）
        if len(all_groups) == 0 and len(entities) > 0:
            print("  没有特殊图层分组，执行通用分组...")
            
            # 按图层进行基本分组
            layer_groups = defaultdict(list)
            for entity in entities:
                layer = entity.get('layer', 'UNKNOWN')
                layer_groups[layer].append(entity)
            
            # 为每个图层创建一个组
            for layer, layer_entities in layer_groups.items():
                if len(layer_entities) > 0:
                    group = {
                        'entities': layer_entities,
                        'label': f'layer_{layer}',
                        'group_type': 'layer_based',
                        'layer': layer,
                        'status': 'pending',
                        'confidence': 0.3
                    }
                    all_groups.append(group)
            
            print(f"  通用分组完成: {len(all_groups)}个组")
        
        # 统一化所有分组的数据结构为字典格式
        unified_groups = []
        for i, group in enumerate(all_groups):
            if isinstance(group, list):
                # 如果是列表，转换为字典格式
                dict_group = {
                    'entities': group,
                    'label': f'group_{i}',
                    'group_type': 'general',
                    'status': 'pending',
                    'confidence': 0.5
                }
                unified_groups.append(dict_group)
            elif isinstance(group, dict):
                # 如果已经是字典，确保有必要的字段
                if 'entities' not in group:
                    group['entities'] = []
                if 'label' not in group:
                    group['label'] = f'group_{i}'
                if 'group_type' not in group:
                    group['group_type'] = 'general'
                if 'status' not in group:
                    group['status'] = 'pending'
                if 'confidence' not in group:
                    group['confidence'] = 0.5
                unified_groups.append(group)
            else:
                print(f"  警告：跳过无效的组类型: {type(group)}")
        
        all_groups = unified_groups
        print(f"  数据结构统一化完成: {len(all_groups)}个字典格式的组")

        return all_groups
    
    def _detect_special_layers(self, entities, layer_patterns, debug=False, layer_type="特殊"):
        """检测特殊图层（墙体、门窗、栏杆）"""
        special_layers = set()
        matched_patterns = {}  # 记录匹配的模式
        
        for entity in entities:
            layer = entity['layer']
            layer_lower = layer.lower()
            
            # 检查是否匹配任何模式
            for pattern in layer_patterns:
                if pattern.lower() in layer_lower:
                    special_layers.add(layer)
                    if layer not in matched_patterns:
                        matched_patterns[layer] = []
                    if pattern not in matched_patterns[layer]:
                        matched_patterns[layer].append(pattern)
                    break
        

        
        return special_layers
    
    def _group_special_entities_by_layer(self, entities, connection_threshold=10, entity_type="wall"):
        """对特殊图层实体进行严格的按图层分组"""
        if not entities:
            return []
        
        # 按图层分组
        layer_groups = defaultdict(list)
        for entity in entities:
            layer_groups[entity['layer']].append(entity)
        
        all_groups = []
        
        # 对每个图层单独进行连接性分组
        for layer, layer_entities in layer_groups.items():
            if entity_type == "wall":
                # 墙体：精确的端点连接检测
                layer_connection_groups = self._group_entities_by_precise_connection(
                    layer_entities, connection_threshold
                )
            elif entity_type in ["door_window", "railing"]:
                # 门窗和栏杆：端点连接 + 200单位容差
                layer_connection_groups = self._group_entities_by_flexible_connection(
                    layer_entities, connection_threshold, proximity_threshold=80
                )
            else:
                # 其他：标准连接检测
                layer_connection_groups = self._group_entities_by_connection(
                    layer_entities, connection_threshold
                )
            
            # 确保返回的是字典格式的组
            for i, group in enumerate(layer_connection_groups):
                if isinstance(group, list):
                    # 如果是列表，转换为字典格式
                    dict_group = {
                        'entities': group,
                        'label': f'{entity_type}_{layer}_{i}',
                        'group_type': entity_type,
                        'layer': layer,
                        'status': 'pending',
                        'confidence': 0.8
                    }
                    all_groups.append(dict_group)
                elif isinstance(group, dict):
                    # 如果已经是字典，确保有必要的字段
                    if 'entities' not in group:
                        group['entities'] = []
                    if 'label' not in group:
                        group['label'] = f'{entity_type}_{layer}_{i}'
                    if 'group_type' not in group:
                        group['group_type'] = entity_type
                    if 'layer' not in group:
                        group['layer'] = layer
                    if 'status' not in group:
                        group['status'] = 'pending'
                    if 'confidence' not in group:
                        group['confidence'] = 0.8
                    all_groups.append(group)
        
        return all_groups

    
    def _group_other_entities_by_layer(self, entities, distance_threshold=20):
        """对其他图层实体进行分组：优先同图层连接，使用精确端点检测（改进版）"""
        if not entities:
            return []
        
        # 第一步：移除重复实体
        unique_entities = self._remove_duplicate_entities(entities)

        # 分离SPLINE和非SPLINE实体
        spline_entities = [e for e in unique_entities if e.get('type') == 'SPLINE']
        non_spline_entities = [e for e in unique_entities if e.get('type') != 'SPLINE']
        
        # 过滤掉长度为0的SPLINE实体
        valid_spline_entities = []
        for spline in spline_entities:
            bbox = self._get_entity_bbox(spline)
            if bbox:
                # 检查SPLINE是否有有效长度
                if bbox[2] - bbox[0] > 0.1 and bbox[3] - bbox[1] > 0.1:
                    valid_spline_entities.append(spline)
        
        # 只对非SPLINE实体进行正常分组
        unique_entities = non_spline_entities

        if not unique_entities:
            return []
        
        # 固定同图层分组阈值为40
        same_layer_threshold = 40
        print(f"同图层分组阈值: {same_layer_threshold}")
        
        # 按图层分组
        layer_groups = defaultdict(list)
        for entity in unique_entities:
            layer_groups[entity['layer']].append(entity)
        
        initial_groups = []
        for layer, layer_entities in layer_groups.items():
            if len(layer_entities) > 1:
                layer_groups_result = self._group_entities_by_precise_connection(
                    layer_entities, threshold=same_layer_threshold
                )
                initial_groups.extend(layer_groups_result)
            else:
                initial_groups.append(layer_entities)
        
        # 简化输出：只显示组数和实体总数
        
        # 第三步：对每个组进行内部优化，使用更小的阈值
        optimized_groups = []
        for group in initial_groups:
            if len(group) == 1:
                optimized_groups.append(group)
                continue
            
            # 对组内实体进行更精确的连接性分组，使用更小的阈值
            internal_threshold = 40  # 保持一致
            internal_groups = self._group_entities_by_precise_connection(
                group, threshold=internal_threshold
            )
            
            if len(internal_groups) == 1:
                # 如果内部优化没有分裂，保持原组
                optimized_groups.append(group)
            else:
                # 如果内部优化产生了分裂，使用分裂后的组
                optimized_groups.extend(internal_groups)
        
        print(f"内部优化后组数: {len(optimized_groups)}")
        
        # 第四步：处理孤立的实体，使用固定的合并阈值100
        final_groups = []
        isolated_entities = []
        
        for group in optimized_groups:
            if len(group) == 1:
                isolated_entities.append(group[0])
            else:
                final_groups.append(group)
        
        # 将孤立的实体合并到最近的组，使用固定阈值100
        merge_threshold = 100
        if isolated_entities and final_groups:
            merged_count = 0
            new_groups_count = 0

            for isolated_entity in isolated_entities:
                nearest_group = None
                min_distance = float('inf')

                for group in final_groups:
                    distance = self._calculate_geometric_distance(isolated_entity, group[0])
                    if distance < min_distance and distance < merge_threshold:
                        min_distance = distance
                        nearest_group = group

                if nearest_group:
                    nearest_group.append(isolated_entity)
                    merged_count += 1
                else:
                    # 如果找不到合适的组，创建新组
                    final_groups.append([isolated_entity])
                    new_groups_count += 1
        
        # 新增：如果一个组的所有元素都在另一个组的边界框内，则合并
        merged = [False] * len(final_groups)
        for i, group_i in enumerate(final_groups):
            if merged[i] or not group_i:
                continue
            bbox_i = self._get_group_bbox(group_i)
            if not bbox_i:
                continue
            for j, group_j in enumerate(final_groups):
                if i == j or merged[j] or not group_j:
                    continue
                bbox_j = self._get_group_bbox(group_j)
                if not bbox_j:
                    continue
                # 判断group_j是否完全在group_i内
                all_in = True
                for entity in group_j:
                    ebbox = self._get_entity_bbox(entity)
                    if not ebbox:
                        all_in = False
                        break
                    # 只要有一个点在外面就不合并
                    if ebbox[0] < bbox_i[0] or ebbox[1] < bbox_i[1] or ebbox[2] > bbox_i[2] or ebbox[3] > bbox_i[3]:
                        all_in = False
                        break
                if all_in:
                    group_i.extend(group_j)
                    merged[j] = True
        # 移除被合并的空组
        final_groups = [g for idx, g in enumerate(final_groups) if not merged[idx]]
        print(f"包围合并后组数: {len(final_groups)}")
        return final_groups
    
    def _remove_duplicate_entities(self, entities):
        """移除重复实体（增强版 - 带调试信息）"""
        if not entities:
            return []

        unique_entities = []
        seen_hashes = set()
        duplicate_count = 0
        hash_stats = {}

        for i, entity in enumerate(entities):
            # 创建实体的哈希值用于去重
            entity_hash = self._get_entity_hash(entity)

            # 统计哈希分布
            hash_stats[entity_hash] = hash_stats.get(entity_hash, 0) + 1

            if entity_hash not in seen_hashes:
                unique_entities.append(entity)
                seen_hashes.add(entity_hash)
            else:
                duplicate_count += 1
                # 只在调试模式下显示重复实体的详细信息
                if duplicate_count <= 3:  # 只显示前3个重复实体
                    entity_type = entity.get('type', 'unknown')
                    layer = entity.get('layer', 'unknown')
                    print(f"    🔄 跳过重复实体[{i}]: {entity_type} - {layer}")

        # 输出去重结果
        if len(entities) != len(unique_entities):
            print(f"去重: {len(entities)} → {len(unique_entities)} 个实体")

            # 如果去重比例过高，显示警告和详细信息
            removal_ratio = (len(entities) - len(unique_entities)) / len(entities)
            if removal_ratio > 0.5:  # 如果超过50%的实体被去重
                print(f"    ⚠️ 去重比例过高: {removal_ratio:.1%}")
                print(f"    📊 哈希分布统计:")
                for hash_val, count in sorted(hash_stats.items(), key=lambda x: x[1], reverse=True)[:5]:
                    if count > 1:
                        print(f"      {hash_val}: {count} 个实体")

        return unique_entities
    
    def _get_entity_hash(self, entity):
        """获取实体的哈希值用于去重（修复版 - 支持多种坐标格式）"""
        if not isinstance(entity, dict) or 'type' not in entity:
            return str(entity)

        entity_type = entity.get('type', '')
        layer = entity.get('layer', '')

        # 根据实体类型创建不同的哈希
        if entity_type == 'LINE':
            # 🔑 修复：支持多种坐标格式
            start_point = None
            end_point = None

            # 格式1：points数组
            points = entity.get('points', [])
            if len(points) >= 2:
                start_point = (float(points[0][0]), float(points[0][1]))
                end_point = (float(points[1][0]), float(points[1][1]))

            # 格式2：start_x, start_y, end_x, end_y字段
            elif ('start_x' in entity and 'start_y' in entity and
                  'end_x' in entity and 'end_y' in entity):
                start_point = (float(entity['start_x']), float(entity['start_y']))
                end_point = (float(entity['end_x']), float(entity['end_y']))

            # 格式3：start, end数组
            elif 'start' in entity and 'end' in entity:
                start = entity.get('start', (0, 0))
                end = entity.get('end', (0, 0))
                start_point = (float(start[0]), float(start[1]))
                end_point = (float(end[0]), float(end[1]))

            # 如果成功提取坐标，创建哈希
            if start_point is not None and end_point is not None:
                # 对点进行排序以确保一致性（相同线段不同方向应该有相同哈希）
                sorted_points = sorted([start_point, end_point])
                return f"LINE_{layer}_{sorted_points}"
            else:
                # 如果无法提取坐标，使用实体的字符串表示
                return f"LINE_{layer}_NO_COORDS_{str(entity)}"
        
        elif entity_type == 'ELLIPSE':
            center = entity.get('center', (0, 0))
            major_axis = entity.get('major_axis', (0, 0))
            ratio = entity.get('ratio', 1.0)
            start_param = entity.get('start_param', 0)
            end_param = entity.get('end_param', 2 * math.pi)
            return f"ELLIPSE_{layer}_{(float(center[0]), float(center[1]))}_{(float(major_axis[0]), float(major_axis[1]))}_{ratio}_{start_param}_{end_param}"
        
        elif entity_type == 'CIRCLE':
            center = entity.get('center', (0, 0))
            radius = entity.get('radius', 0)
            return f"CIRCLE_{layer}_{(float(center[0]), float(center[1]))}_{radius}"
        
        elif entity_type == 'ARC':
            center = entity.get('center', (0, 0))
            radius = entity.get('radius', 0)
            start_angle = entity.get('start_angle', 0)
            end_angle = entity.get('end_angle', 0)
            return f"ARC_{layer}_{(float(center[0]), float(center[1]))}_{radius}_{start_angle}_{end_angle}"
        
        elif entity_type == 'LWPOLYLINE':
            points = entity.get('points', [])
            if points:
                sorted_points = sorted([(float(p[0]), float(p[1])) for p in points])
                return f"LWPOLYLINE_{layer}_{sorted_points}"
        
        elif entity_type == 'TEXT':
            text = entity.get('text', '')
            position = entity.get('position', (0, 0))
            return f"TEXT_{layer}_{text}_{(float(position[0]), float(position[1]))}"
        
        elif entity_type == 'INSERT':
            name = entity.get('name', '')
            position = entity.get('position', (0, 0))
            return f"INSERT_{layer}_{name}_{(float(position[0]), float(position[1]))}"
        
        else:
            # 对于其他类型，使用通用哈希
            return f"{entity_type}_{layer}_{str(entity)}"
    
    def _group_entities_by_connection(self, entities, threshold):
        """基于连接性对实体进行分组的通用方法（改进版）"""
        if not entities:
            return []
        
        # 第一步：移除重复实体
        unique_entities = self._remove_duplicate_entities(entities)
        
        if not unique_entities:
            return []
        
        groups = []
        grouped = set()

        # 第二步：改进的分组算法
        for i, entity in enumerate(unique_entities):
            if i in grouped:
                continue

            # 创建新组
            group = [entity]
            grouped.add(i)
            queue = [i]

            # 广度优先搜索连接实体
            while queue:
                current_idx = queue.pop(0)
                current_entity = unique_entities[current_idx]

                # 检查与所有其他未分组实体的连接性
                for j, other_entity in enumerate(unique_entities):
                    if j == current_idx or j in grouped:
                        continue

                    # 使用改进的连接检测
                    is_connected = self._are_entities_connected(current_entity, other_entity, threshold)

                    if is_connected:
                        group.append(other_entity)
                        grouped.add(j)
                        queue.append(j)

            groups.append(group)
        
        return groups
    
    def _group_entities_by_precise_connection(self, entities, threshold):
        """基于精确端点连接的分组方法（用于墙体）"""
        if not entities:
            return []
        
        # 第一步：移除重复实体
        unique_entities = self._remove_duplicate_entities(entities)
        
        if not unique_entities:
            return []
        
        # 简化输出
        
        groups = []
        grouped = set()
        
        # 第二步：精确连接分组
        for i, entity in enumerate(unique_entities):
            if i in grouped:
                continue
            
            group = [entity]
            grouped.add(i)
            queue = [i]
            
            while queue:
                current_idx = queue.pop(0)
                current_entity = unique_entities[current_idx]
                
                # 检查与所有其他未分组实体的连接性
                for j, other_entity in enumerate(unique_entities):
                    if j == current_idx or j in grouped:
                        continue
                    
                    # 使用精确的端点连接检测
                    distance = self._calculate_geometric_distance(current_entity, other_entity)
                    if distance <= threshold:
                        group.append(other_entity)
                        grouped.add(j)
                        queue.append(j)
                        # 简化相交实体输出
            
            groups.append(group)
        
        return groups
    
    def _group_entities_by_flexible_connection(self, entities, connection_threshold, proximity_threshold=80):
        """基于灵活连接的分组方法（用于门窗栏杆）"""
        if not entities:
            return []
        
        # 第一步：移除重复实体
        unique_entities = self._remove_duplicate_entities(entities)
        
        if not unique_entities:
            return []
        
        # 简化输出
        
        groups = []
        grouped = set()
        
        # 第二步：灵活连接分组
        for i, entity in enumerate(unique_entities):
            if i in grouped:
                continue
            
            group = [entity]
            grouped.add(i)
            queue = [i]
            
            while queue:
                current_idx = queue.pop(0)
                current_entity = unique_entities[current_idx]
                
                # 检查与所有其他未分组实体的连接性
                for j, other_entity in enumerate(unique_entities):
                    if j == current_idx or j in grouped:
                        continue
                    
                    # 首先尝试精确的端点连接
                    is_connected = self._are_entities_connected_by_endpoints(
                        current_entity, other_entity, connection_threshold
                    )
                    
                    # 如果端点不连接，检查是否在容差距离内
                    if not is_connected:
                        is_connected = self._are_entities_in_proximity(
                            current_entity, other_entity, proximity_threshold
                        )
                    
                    if is_connected:
                        group.append(other_entity)
                        grouped.add(j)
                        queue.append(j)
            
            groups.append(group)
        
        return groups
    
    def _are_entities_in_proximity(self, entity1, entity2, threshold):
        """检查两个实体是否在指定距离内（用于门窗栏杆的容差检测）"""
        # 使用新的几何距离计算方法
        geometric_distance = self._calculate_geometric_distance(entity1, entity2)
        return geometric_distance <= threshold
        
        # 检查任意端点之间的最小距离
        min_distance = float('inf')
        for p1 in endpoints1:
            for p2 in endpoints2:
                distance = math.sqrt((p1[0] - p2[0])**2 + (p1[1] - p2[1])**2)
                min_distance = min(min_distance, distance)
        
        return min_distance <= threshold
    
    def _find_isolated_small_entities(self, all_entities, existing_groups):
        """找出孤立的小实体（不与任何其他实体连接的小实体）"""
        # 收集所有已分组的实体
        grouped_entity_ids = set()
        for group in existing_groups:
            for entity in group:
                grouped_entity_ids.add(id(entity))
        
        # 找出未分组的实体
        ungrouped_entities = [e for e in all_entities if id(e) not in grouped_entity_ids]
        
        # 筛选出小实体
        small_entities = [e for e in ungrouped_entities if self._is_small_entity(e, 50)]
        
        # 检查这些小实体是否真正孤立（不与任何已分组实体连接）
        isolated_small_entities = []
        for small_entity in small_entities:
            is_isolated = True
            small_center = self._get_entity_center(small_entity)
            
            if small_center is not None:
                # 检查是否与任何已分组实体连接
                for group in existing_groups:
                    for grouped_entity in group:
                        if self._are_entities_connected(small_entity, grouped_entity, 50):
                            is_isolated = False
                            break
                    if not is_isolated:
                        break
            
            if is_isolated:
                isolated_small_entities.append(small_entity)
        
        return isolated_small_entities
    
    def _group_isolated_small_entities(self, isolated_entities):
        """对孤立的小实体进行分组（改进版）"""
        if not isolated_entities:
            return []
        
        # 第一步：移除重复实体
        unique_entities = self._remove_duplicate_entities(isolated_entities)

        if not unique_entities:
            return []

        # 第二步：首先尝试中等阈值分组（20单位阈值）
        medium_groups = self._group_entities_by_precise_connection(unique_entities, threshold=20)

        # 统计中等阈值分组结果
        medium_isolated = sum(1 for g in medium_groups if len(g) == 1)

        # 第三步：如果还有孤立实体，尝试更宽松的阈值（200单位）
        if medium_isolated > 0:
            remaining_isolated = [e for g in medium_groups if len(g) == 1 for e in g]
            if remaining_isolated:
                loose_groups = self._group_entities_by_precise_connection(remaining_isolated, threshold=50)

                # 第四步：如果还有孤立实体，尝试最宽松的阈值（500单位）
                loose_isolated = sum(1 for g in loose_groups if len(g) == 1)
                if loose_isolated > 0:
                    final_isolated = [e for g in loose_groups if len(g) == 1 for e in g]
                    if final_isolated:
                        final_groups = self._group_entities_by_precise_connection(final_isolated, threshold=100)

                        # 合并所有结果
                        all_final_groups = [g for g in medium_groups if len(g) > 1] + [g for g in loose_groups if len(g) > 1] + final_groups

                        return all_final_groups
                    else:
                        return [g for g in medium_groups if len(g) > 1] + [g for g in loose_groups if len(g) > 1]
                else:
                    # 合并结果
                    final_groups = [g for g in medium_groups if len(g) > 1] + loose_groups

                    return final_groups
        
        return medium_groups
    
    def merge_small_elements_to_nearest_groups(self, groups, wall_layers, door_window_layers, railing_layers, size_threshold=30):
        """将小元素合并到就近组（非墙体门窗栏杆）"""
        if not groups:
            return groups
        
        special_layers = wall_layers | door_window_layers | railing_layers
        small_elements = []
        remaining_groups = []
        
        # 分离小元素和普通组
        for group in groups:
            if not group:
                continue
                
            # 检查是否为特殊图层组
            is_special_group = any(entity['layer'] in special_layers for entity in group)
            if is_special_group:
                remaining_groups.append(group)
                continue
            
            # 检查组中的小元素
            group_small_elements = []
            group_large_elements = []
            
            for entity in group:
                if self._is_small_entity(entity, size_threshold):
                    group_small_elements.append(entity)
                else:
                    group_large_elements.append(entity)
            
            # 如果整个组都是小元素，则加入小元素列表
            if len(group_small_elements) == len(group) and len(group) <= 2:
                small_elements.extend(group_small_elements)
            else:
                # 保留有大元素的组，小元素单独处理
                if group_large_elements:
                    remaining_groups.append(group_large_elements)
                if group_small_elements:
                    small_elements.extend(group_small_elements)
        
        # 将小元素分配到最近的非特殊图层组
        non_special_groups = [g for g in remaining_groups 
                              if not any(entity['layer'] in special_layers for entity in g)]
        
        for element in small_elements:
            nearest_group = self._find_nearest_non_special_group(element, non_special_groups, special_layers, threshold=20)
            if nearest_group:
                nearest_group.append(element)
        
        return remaining_groups
    
    def merge_small_groups_to_nearest(self, groups, wall_layers, door_window_layers, railing_layers, size_threshold=60, connection_threshold=20):
        """将小组合并到就近组（非墙体门窗栏杆）"""
        if not groups:
            return groups
        
        special_layers = wall_layers | door_window_layers | railing_layers
        small_groups = []
        remaining_groups = []
        
        # 分离小组和普通组
        for group in groups:
            if not group:
                continue
                
            # 检查是否为特殊图层组
            is_special_group = any(entity['layer'] in special_layers for entity in group)
            if is_special_group:
                remaining_groups.append(group)
                continue
            
            # 计算组的边界框尺寸
            bbox = self._get_group_bbox(group)
            if bbox:
                width = bbox[2] - bbox[0]  # max_x - min_x
                height = bbox[3] - bbox[1]  # max_y - min_y
                
                # 如果长宽都小于阈值，则为小组
                if width < size_threshold and height < size_threshold:
                    small_groups.append(group)
                else:
                    remaining_groups.append(group)
            else:
                remaining_groups.append(group)
        
        # 将小组合并到最近的非特殊图层组
        non_special_groups = [g for g in remaining_groups 
                              if not any(entity['layer'] in special_layers for entity in g)]
        
        for small_group in small_groups:
            connected = False
            # 首先检查是否与任何组有几何连接
            for other_group in non_special_groups:
                if self._groups_connected(small_group, other_group, connection_threshold):
                    other_group.extend(small_group)
                    connected = True
                    break
                    
            if not connected:
                # 没有连接关系，保留为独立组
                remaining_groups.append(small_group)
        
        return remaining_groups
    
    def _groups_connected(self, group1, group2, threshold):
        """检查两组实体间是否有几何连接"""
        for entity1 in group1:
            for entity2 in group2:
                if self._are_entities_connected(entity1, entity2, threshold):
                    return True
        return False
    
    def _get_group_bbox(self, group):
        """获取组的边界框"""
        if not group:
            return None

        # 修复：处理不同格式的组
        entities_to_process = []
        if isinstance(group, dict) and 'entities' in group:
            # 如果是字典格式的组，获取实体列表
            entities_to_process = group['entities']
        elif isinstance(group, list):
            # 如果是实体列表
            entities_to_process = group
        else:
            print(f"⚠️ _get_group_bbox: 未知的组格式: {type(group)}")
            return None

        min_x = float('inf')
        min_y = float('inf')
        max_x = float('-inf')
        max_y = float('-inf')

        for entity in entities_to_process:
            bbox = self._get_entity_bbox(entity)
            if bbox:
                # 确保使用普通浮点数
                min_x = min(min_x, float(bbox[0]))
                min_y = min(min_y, float(bbox[1]))
                max_x = max(max_x, float(bbox[2]))
                max_y = max(max_y, float(bbox[3]))

        if min_x != float('inf'):
            return (min_x, min_y, max_x, max_y)
        return None
    
    def _find_nearest_non_special_group(self, element, groups, special_layers, threshold=20):
        """严格基于几何连接性"""
        # 优先检查几何连接性
        for group in groups:
            if any(entity['layer'] in special_layers for entity in group):
                continue
                
            for entity2 in group:
                # 1. 端点距离检查
                if self._calculate_endpoint_distance(element, entity2) < threshold:
                    return group
                    
                # 2. 线段相交检查
                if element.get('type') == 'LINE' and entity2.get('type') == 'LINE':
                    if self._lines_intersect(element, entity2):
                        return group
                        
                # 3. 精确几何距离
                distance = self._calculate_geometric_distance(element, entity2)
                if distance <= threshold:
                    return group
        
        # 没有任何连接关系，不合并
        return None
    
    def _find_nearest_non_special_group_for_group(self, small_group, groups, special_layers):
        """找到小组最近的非特殊图层组，优先判断端点重合、线段相交或端点距离小于阈值"""
        if not groups:
            return None
        threshold = 20  # 端点距离阈值，可根据实际情况调整
        for entity1 in small_group:
            for group in groups:
                if any(entity['layer'] in special_layers for entity in group):
                    continue
                for entity2 in group:
                    # 1. 端点距离
                    if self._calculate_endpoint_distance(entity1, entity2) < threshold:
                        return group
                    # 2. 线段相交
                    if entity1.get('type') == 'LINE' and entity2.get('type') == 'LINE':
                        if self._lines_intersect(entity1, entity2):
                            return group
                    # 3. 连接性综合判据
                    if self._are_entities_connected(entity1, entity2, tolerance=threshold):
                        return group
        # 若无严格连接，兜底用中心点距离
        small_group_center = self._get_group_center(small_group)
        min_distance = float('inf')
        nearest_group = None
        for group in groups:
            if any(entity['layer'] in special_layers for entity in group):
                continue
            group_center = self._get_group_center(group)
            if group_center:
                distance = math.sqrt(
                    (small_group_center[0] - group_center[0])**2 + 
                    (small_group_center[1] - group_center[1])**2
                )
                if distance < min_distance:
                    min_distance = distance
                    nearest_group = group
        return None  # 不再用中心点距离合并
    

    

    

    
    def extract_features(self, entity_group):
        """从实体组中提取增强特征"""
        features = {
            'length': 0.0,
            'area': 0.0,
            'closed': 0,
            'line_count': 0,
            'arc_count': 0,
            'circle_count': 0,
            'ellipse_count': 0,
            'insert_count': 0,
            'text_count': 0,
            'dimension_count': 0,
            'min_x': float('inf'),
            'min_y': float('inf'),
            'max_x': float('-inf'),
            'max_y': float('-inf'),
            'aspect_ratio': 0.0,
            'layer_hash': 0,
            'text_length': 0,
            'text_angle': 0
        }
        
        layers = set()
        text_entities = []
        
        for entity in entity_group:
            # 🔑 关键修复：确保图层名为字符串类型，避免排序时的类型错误
            layer_name = str(entity['layer']) if entity['layer'] is not None else '0'
            layers.add(layer_name)
            
            # 更新边界框
            if 'points' in entity and entity['points']:
                points = np.array(entity['points'])
                min_x, min_y = np.min(points, axis=0)
                max_x, max_y = np.max(points, axis=0)
                features['min_x'] = min(features['min_x'], min_x)
                features['min_y'] = min(features['min_y'], min_y)
                features['max_x'] = max(features['max_x'], max_x)
                features['max_y'] = max(features['max_y'], max_y)
            
            # 统计实体类型
            entity_type = entity['type']
            if entity_type == 'LINE' and 'points' in entity and len(entity['points']) >= 2:
                features['line_count'] += 1
                p1, p2 = entity['points'][:2]
                features['length'] += np.sqrt((p2[0]-p1[0])**2 + (p2[1]-p1[1])**2)
            elif entity_type == 'ARC':
                features['arc_count'] += 1
            elif entity_type == 'CIRCLE' and 'radius' in entity:
                features['circle_count'] += 1
                features['area'] += np.pi * entity['radius'] ** 2
            elif entity_type == 'ELLIPSE':
                features['ellipse_count'] += 1
            elif entity_type == 'INSERT':
                features['insert_count'] += 1
            elif entity_type in ['TEXT', 'MTEXT']:
                features['text_count'] += 1
                text = entity.get('text', '')
                features['text_length'] += len(text)
                features['text_angle'] = entity.get('rotation', 0)
                text_entities.append(entity)
            elif entity_type == 'DIMENSION':
                features['dimension_count'] += 1
            elif entity_type in ('LWPOLYLINE', 'POLYLINE') and 'points' in entity and entity['points']:
                points = entity['points']
                if entity.get('closed', False) and len(points) >= 3:
                    features['closed'] = 1
                    try:
                        poly = Polygon(points)
                        features['area'] += poly.area
                        features['length'] += poly.length
                    except:
                        pass
                else:
                    for i in range(len(points)-1):
                        try:
                            segment = LineString([points[i], points[i+1]])
                            features['length'] += segment.length
                        except:
                            pass
        
        # 计算宽高比
        width = features['max_x'] - features['min_x']
        height = features['max_y'] - features['min_y']
        if height > 0:
            features['aspect_ratio'] = width / height
        elif width > 0:
            features['aspect_ratio'] = 1000
        
        # 计算图层哈希
        try:
            # 🔑 关键修复：确保所有图层名都是字符串类型后再排序
            layer_list = [str(layer) for layer in layers]
            features['layer_hash'] = hash(tuple(sorted(layer_list))) % 1000
        except Exception as e:
            print(f"⚠️ 图层哈希计算失败: {e}, 图层数据: {layers}")
            features['layer_hash'] = 0  # 默认值
        
        # 添加文本特定特征
        if features['text_count'] > 0:
            features['is_room_label'] = 1 if self._is_room_label(text_entities) else 0
            features['is_dimension'] = 1 if self._is_dimension_text(text_entities) else 0
            features['is_title_block'] = 1 if self._is_title_block(text_entities) else 0
        else:
            features['is_room_label'] = 0
            features['is_dimension'] = 0
            features['is_title_block'] = 0
        
        # 特征工程增强
        features.update({
            'rectangularity': self._calc_rectangularity(entity_group),
            'symmetry_score': self._calc_symmetry(entity_group),
            'text_keywords': self._detect_keywords(entity_group),
            'color_variance': self._calc_color_variance(entity_group),
            'line_angle_variance': self._calc_angle_variance(entity_group),
            'feature_hash': self._calc_feature_hash(features)
        })
        
        # 确保所有特征都存在
        for key in self.feature_columns:
            if key not in features:
                features[key] = 0.0
        
        return features
    
    def suggest_category(self, features, entity_group=None):
        """类别推荐算法"""
        # 基于特征和历史标注推荐类别
        suggestions = []
        
        # 1. 基于文本特征
        if features.get('is_room_label', 0) == 1:
            suggestions.append('room_label')
        if features.get('is_dimension', 0) == 1:
            suggestions.append('dimension')
        if features.get('is_title_block', 0) == 1:
            suggestions.append('title_block')
        
        # 2. 基于几何特征
        if features.get('closed', 0) == 1 and features.get('area', 0) > 1000:
            suggestions.append('wall')
        
        if features.get('arc_count', 0) > 0 and features.get('line_count', 0) > 0:
            suggestions.append('door')
        
        if features.get('line_count', 0) >= 2 and features.get('aspect_ratio', 0) > 2:
            suggestions.append('window')
        
        # 3. 基于图层信息
        if entity_group:
            inferred = self._infer_category_from_layer(entity_group)
            if inferred:
                suggestions.append(inferred)
        
        # 返回最可能的类别
        if suggestions:
            return suggestions[0]
        return 'other'
    
    # 辅助方法
    def _get_entity_bbox(self, entity):
        """获取实体的边界框"""
        if not isinstance(entity, dict) or 'type' not in entity:
            return None
        
        entity_type = entity.get('type', '')
        
        if entity_type == 'LINE':
            # 🔑 修复：支持多种坐标格式
            if 'points' in entity and entity['points'] and len(entity['points']) >= 2:
                points = np.array(entity['points'])
                min_x, min_y = np.min(points, axis=0)
                max_x, max_y = np.max(points, axis=0)
                return (min_x, min_y, max_x, max_y)
            elif 'start' in entity and 'end' in entity:
                start = entity['start']
                end = entity['end']
                return (min(start[0], end[0]), min(start[1], end[1]),
                       max(start[0], end[0]), max(start[1], end[1]))
            elif ('start_x' in entity and 'start_y' in entity and
                  'end_x' in entity and 'end_y' in entity):
                # 格式：start_x, start_y, end_x, end_y字段
                start_x = float(entity['start_x'])
                start_y = float(entity['start_y'])
                end_x = float(entity['end_x'])
                end_y = float(entity['end_y'])
                return (min(start_x, end_x), min(start_y, end_y),
                       max(start_x, end_x), max(start_y, end_y))
        
        elif entity_type == 'CIRCLE':
            # 对于圆形，使用采样点计算包围盒而不是简单的中心+半径
            if 'center' in entity and 'radius' in entity:
                circle_points = self._sample_circle_points(entity, num_points=16)
                if circle_points:
                    x_coords = [p[0] for p in circle_points]
                    y_coords = [p[1] for p in circle_points]
                    return (min(x_coords), min(y_coords), max(x_coords), max(y_coords))
                else:
                    # 回退到中心+半径方法
                    center = entity['center']
                    radius = entity['radius']
                    return (center[0]-radius, center[1]-radius, center[0]+radius, center[1]+radius)

        elif entity_type == 'ARC':
            # 对于圆弧，使用采样点计算包围盒，不将其视为完整圆
            if 'center' in entity and 'radius' in entity:
                arc_points = self._sample_arc_points(entity, num_points=16)
                if arc_points:
                    x_coords = [p[0] for p in arc_points]
                    y_coords = [p[1] for p in arc_points]
                    return (min(x_coords), min(y_coords), max(x_coords), max(y_coords))
                else:
                    # 回退到端点方法
                    endpoints = self._get_entity_endpoints(entity)
                    if endpoints:
                        x_coords = [p[0] for p in endpoints]
                        y_coords = [p[1] for p in endpoints]
                        return (min(x_coords), min(y_coords), max(x_coords), max(y_coords))
        
        elif entity_type == 'ELLIPSE':
            if 'center' in entity and 'major_axis' in entity:
                # 验证椭圆参数
                if not self._validate_ellipse_parameters(entity):
                    return None
                
                center = entity['center']
                major_axis = entity['major_axis']
                ratio = entity.get('ratio', 1.0)
                
                # 计算椭圆参数
                a = math.sqrt(major_axis[0]**2 + major_axis[1]**2)  # 长轴半径
                b = a * ratio  # 短轴半径
                angle = math.atan2(major_axis[1], major_axis[0])  # 长轴角度
                
                # 计算旋转后的边界
                cos_angle = math.cos(angle)
                sin_angle = math.sin(angle)
                
                # 计算未旋转时的最大范围
                dx = math.sqrt((a * cos_angle)**2 + (b * sin_angle)**2)
                dy = math.sqrt((a * sin_angle)**2 + (b * cos_angle)**2)
                
                return (
                    center[0] - dx, center[1] - dy,
                    center[0] + dx, center[1] + dy
                )
        
        elif entity_type in ['LWPOLYLINE', 'POLYLINE']:
            if 'points' in entity and entity['points']:
                points = np.array(entity['points'])
                min_x, min_y = np.min(points, axis=0)
                max_x, max_y = np.max(points, axis=0)
                return (min_x, min_y, max_x, max_y)
        
        elif entity_type == 'SPLINE':
            # 尝试从控制点计算边界框
            if 'control_points' in entity and entity['control_points']:
                points = entity['control_points']
                x_coords = [p[0] for p in points]
                y_coords = [p[1] for p in points]
                
                # 检查是否长度为0
                x_range = max(x_coords) - min(x_coords)
                y_range = max(y_coords) - min(y_coords)
                
                if x_range < 0.001 and y_range < 0.001:
                    return None
                
                return (min(x_coords), min(y_coords), max(x_coords), max(y_coords))
            
            # 尝试从拟合点计算边界框
            elif 'fit_points' in entity and entity['fit_points']:
                points = entity['fit_points']
                x_coords = [p[0] for p in points]
                y_coords = [p[1] for p in points]
                
                # 检查是否长度为0
                x_range = max(x_coords) - min(x_coords)
                y_range = max(y_coords) - min(y_coords)
                
                if x_range < 0.001 and y_range < 0.001:
                    return None
                
                return (min(x_coords), min(y_coords), max(x_coords), max(y_coords))
            
            # 如果既没有控制点也没有拟合点，返回None
            else:
                return None
        
        elif entity_type in ['INSERT', 'TEXT', 'MTEXT']:
            if 'position' in entity:
                pos = entity['position']
                size = 100
                return (pos[0]-size, pos[1]-size, pos[0]+size, pos[1]+size)
        
        # 通用处理：尝试从points字段
        elif 'points' in entity and entity['points']:
            points = np.array(entity['points'])
            min_x, min_y = np.min(points, axis=0)
            max_x, max_y = np.max(points, axis=0)
            return (min_x, min_y, max_x, max_y)
        
        return None
    
    def _get_entity_center(self, entity):
        """获取实体的中心点（增强容错性）"""
        # 检查entity是否有type键
        if not isinstance(entity, dict) or 'type' not in entity:
            return None
            
        try:
            if entity['type'] == 'LINE':
                # 检查points字段
                if 'points' not in entity or not entity['points'] or len(entity['points']) < 2:
                    return None
                p1, p2 = entity['points'][0], entity['points'][1]
                return ((p1[0] + p2[0]) / 2, (p1[1] + p2[1]) / 2)
                
            elif entity['type'] in ('LWPOLYLINE', 'POLYLINE'):
                # 检查points字段
                if 'points' not in entity or not entity['points']:
                    return None
                points = np.array(entity['points'])
                center = np.mean(points, axis=0)
                return tuple(center)
                
            elif entity['type'] in ['ARC', 'CIRCLE', 'ELLIPSE']:
                # 对于圆弧、圆形、椭圆，不返回中心点，而是返回None
                # 这样可以强制使用采样点方法进行距离计算
                return None
                
            elif entity['type'] in ['INSERT', 'TEXT', 'MTEXT']:
                # 检查position字段
                if 'position' not in entity or entity['position'] is None:
                    return None
                return tuple(entity['position'])
                
            elif entity['type'] == 'SPLINE':
                # 处理SPLINE实体
                if 'control_points' in entity and entity['control_points']:
                    # 使用控制点的平均值作为中心点
                    control_points = np.array(entity['control_points'])
                    center = np.mean(control_points, axis=0)
                    return tuple(center)
                elif 'fit_points' in entity and entity['fit_points']:
                    # 使用拟合点的平均值作为中心点
                    fit_points = np.array(entity['fit_points'])
                    center = np.mean(fit_points, axis=0)
                    return tuple(center)
                else:
                    # 回退到bbox计算
                    bbox = self._get_entity_bbox(entity)
                    if bbox:
                        min_x, min_y, max_x, max_y = bbox
                        return ((min_x + max_x) / 2, (min_y + max_y) / 2)
                    return None
            else:
                # 未知类型，尝试使用bbox计算中心点
                bbox = self._get_entity_bbox(entity)
                if bbox:
                    min_x, min_y, max_x, max_y = bbox
                    return ((min_x + max_x) / 2, (min_y + max_y) / 2)
                return None
                
        except (KeyError, TypeError, ValueError, IndexError) as e:
            # 记录错误但不中断程序
            return None
    

    
    def _is_small_entity(self, entity, size_threshold=50):
        """检查实体是否过小"""
        # 检查entity是否有type键
        if not isinstance(entity, dict) or 'type' not in entity:
            return False
            
        if entity['type'] == 'LINE' and 'points' in entity and len(entity['points']) >= 2:
            p1, p2 = entity['points'][:2]
            length = math.sqrt((p2[0]-p1[0])**2 + (p2[1]-p1[1])**2)
            return length < size_threshold
        elif entity['type'] in ('LWPOLYLINE', 'POLYLINE') and 'points' in entity:
            total_length = 0
            points = entity['points']
            for i in range(len(points)-1):
                p1 = points[i]
                p2 = points[i+1]
                segment_length = math.sqrt((p2[0]-p1[0])**2 + (p2[1]-p1[1])**2)
                total_length += segment_length
            return total_length < size_threshold
        elif entity['type'] == 'CIRCLE' and 'radius' in entity:
            return entity['radius'] * 2 < size_threshold
        elif entity['type'] in ['INSERT', 'TEXT', 'MTEXT']:
            return True
        return False
    

    
    def _get_group_center(self, group):
        """获取实体组的中心点（增强容错性）"""
        if not group:
            return None
            
        centers = []
        invalid_entities = []
        
        for i, entity in enumerate(group):
            if entity is None:
                invalid_entities.append(f"实体{i+1}: None")
                continue
                
            center = self._get_entity_center(entity)
            if center is not None:
                centers.append(center)
            else:
                # 记录无法计算中心点的实体
                entity_type = entity.get('type', 'unknown') if isinstance(entity, dict) else 'invalid'
                invalid_entities.append(f"实体{i+1}: {entity_type}")
        
        if centers:
            try:
                centers_arr = np.array(centers)
                group_center = np.mean(centers_arr, axis=0)
                # 转换为普通浮点数以避免numpy类型问题
                return (float(group_center[0]), float(group_center[1]))
            except Exception as e:
                print(f"警告: 计算组中心点时出错: {e}")
                return None
        else:
            # 记录无法计算中心点的原因
            return None
    

    
    def _are_entities_connected(self, entity1, entity2, tolerance=50.0, use_endpoint_detection=True):
        """检查实体是否连接"""
        if use_endpoint_detection:
            # 使用精确的端点连接检测
            return self._are_entities_connected_by_endpoints(entity1, entity2, tolerance)
        else:
            # 对于圆弧、圆形、椭圆，使用几何距离而不是中心点距离
            if entity1['type'] in ['ARC', 'CIRCLE', 'ELLIPSE'] or entity2['type'] in ['ARC', 'CIRCLE', 'ELLIPSE']:
                distance = self._calculate_entity_distance(entity1, entity2)
                return distance <= tolerance
            else:
                # 对于其他类型，使用中心点距离检测（向后兼容）
                center1 = self._get_entity_center(entity1)
                center2 = self._get_entity_center(entity2)

                if center1 is not None and center2 is not None:
                    distance = math.sqrt((center1[0]-center2[0])**2 + (center1[1]-center2[1])**2)
                    return distance <= tolerance
                return False
    
    def _are_entities_connected_by_endpoints(self, entity1, entity2, tolerance=50.0):
        """基于真实几何距离的精确连接检测"""
        # 使用新的几何距离计算方法
        geometric_distance = self._calculate_geometric_distance(entity1, entity2)
        return geometric_distance <= tolerance
    
    def _calculate_geometric_distance(self, entity1, entity2):
        """计算两个实体之间的真实几何距离"""
        # 获取实体的几何信息
        geom1 = self._get_entity_geometry(entity1)
        geom2 = self._get_entity_geometry(entity2)
        

        
        if not geom1 or not geom2:
            # 回退到端点距离计算
            distance = self._calculate_endpoint_distance(entity1, entity2)
            return distance
        
        # 性能优化：对于圆弧/圆形，先进行快速预检查
        has_arc_or_circle = (geom1['type'] in ['circle', 'arc'] or geom2['type'] in ['circle', 'arc'])
        
        if has_arc_or_circle:
            # 对于圆弧/圆形，先进行粗略的端点距离检查（不使用中心点）
            quick_distance = self._quick_distance_check(entity1, entity2)
            if quick_distance > 1000:
                # 距离超过1000单位，直接返回粗略距离，不进行复杂计算
                return quick_distance
            elif quick_distance > 50:
                # 距离在50-1000之间，先尝试简单的端点距离
                simple_distance = self._calculate_endpoint_distance(entity1, entity2)
                if simple_distance > 50:
                    # 简单距离也较远，可能不需要复杂计算
                    return simple_distance
                                 # 否则继续进行精确计算（使用采样点）
        else:
            # 对于非圆弧/圆形的情况，也进行快速预检查
            quick_distance = self._quick_distance_check(entity1, entity2)
            if quick_distance > 500:
                # 距离超过500单位，直接返回端点距离
                simple_distance = self._calculate_endpoint_distance(entity1, entity2)
                return simple_distance
        
        # 计算几何距离
        distance = None
        if geom1['type'] == 'line' and geom2['type'] == 'line':
            distance = self._line_to_line_distance(geom1, geom2)
        elif geom1['type'] == 'polyline' and geom2['type'] == 'polyline':
            distance = self._polyline_to_polyline_distance(geom1, geom2)
        elif geom1['type'] == 'polyline' and geom2['type'] == 'line':
            distance = self._polyline_to_line_distance(geom1, geom2)
        elif geom1['type'] == 'line' and geom2['type'] == 'polyline':
            distance = self._polyline_to_line_distance(geom2, geom1)
        # 圆形与直线/多段线的距离计算
        elif geom1['type'] == 'circle' and geom2['type'] == 'line':
            distance = self._circle_to_line_distance(geom1, geom2)
        elif geom1['type'] == 'line' and geom2['type'] == 'circle':
            distance = self._circle_to_line_distance(geom2, geom1)
        elif geom1['type'] == 'circle' and geom2['type'] == 'polyline':
            distance = self._circle_to_polyline_distance(geom1, geom2)
        elif geom1['type'] == 'polyline' and geom2['type'] == 'circle':
            distance = self._circle_to_polyline_distance(geom2, geom1)
        # 圆弧与直线/多段线的距离计算
        elif geom1['type'] == 'arc' and geom2['type'] == 'line':
            distance = self._arc_to_line_distance(geom1, geom2)
        elif geom1['type'] == 'line' and geom2['type'] == 'arc':
            distance = self._arc_to_line_distance(geom2, geom1)
        elif geom1['type'] == 'arc' and geom2['type'] == 'polyline':
            distance = self._arc_to_polyline_distance(geom1, geom2)
        elif geom1['type'] == 'polyline' and geom2['type'] == 'arc':
            distance = self._arc_to_polyline_distance(geom2, geom1)
        # 圆形与圆形、圆弧的距离计算
        elif geom1['type'] == 'circle' and geom2['type'] == 'circle':
            distance = self._circle_to_circle_distance(geom1, geom2)
        elif geom1['type'] == 'circle' and geom2['type'] == 'arc':
            distance = self._circle_to_arc_distance(geom1, geom2)
        elif geom1['type'] == 'arc' and geom2['type'] == 'circle':
            distance = self._circle_to_arc_distance(geom2, geom1)
        elif geom1['type'] == 'arc' and geom2['type'] == 'arc':
            distance = self._arc_to_arc_distance(geom1, geom2)
        # 点与各种几何体的距离计算
        elif geom1['type'] == 'point' and geom2['type'] == 'line':
            distance = self._point_to_line_distance(geom1['point'], geom2)
        elif geom1['type'] == 'line' and geom2['type'] == 'point':
            distance = self._point_to_line_distance(geom2['point'], geom1)
        elif geom1['type'] == 'point' and geom2['type'] == 'polyline':
            distance = self._point_to_polyline_distance(geom1['point'], geom2)
        elif geom1['type'] == 'polyline' and geom2['type'] == 'point':
            distance = self._point_to_polyline_distance(geom2['point'], geom1)
        elif geom1['type'] == 'point' and geom2['type'] == 'circle':
            distance = self._point_to_circle_distance(geom1['point'], geom2)
        elif geom1['type'] == 'circle' and geom2['type'] == 'point':
            distance = self._point_to_circle_distance(geom2['point'], geom1)
        elif geom1['type'] == 'point' and geom2['type'] == 'arc':
            distance = self._point_to_arc_distance(geom1['point'], geom2)
        elif geom1['type'] == 'arc' and geom2['type'] == 'point':
            distance = self._point_to_arc_distance(geom2['point'], geom1)
        elif geom1['type'] == 'point' and geom2['type'] == 'point':
            p1, p2 = geom1['point'], geom2['point']
            distance = math.sqrt((p1[0] - p2[0])**2 + (p1[1] - p2[1])**2)
        # 椭圆与其他几何体的距离计算
        elif geom1['type'] == 'ellipse' and geom2['type'] == 'line':
            distance = self._ellipse_to_line_distance(geom1, geom2)
        elif geom1['type'] == 'line' and geom2['type'] == 'ellipse':
            distance = self._ellipse_to_line_distance(geom2, geom1)
        elif geom1['type'] == 'ellipse' and geom2['type'] == 'polyline':
            distance = self._ellipse_to_polyline_distance(geom1, geom2)
        elif geom1['type'] == 'polyline' and geom2['type'] == 'ellipse':
            distance = self._ellipse_to_polyline_distance(geom2, geom1)
        elif geom1['type'] == 'ellipse' and geom2['type'] == 'circle':
            distance = self._ellipse_to_circle_distance(geom1, geom2)
        elif geom1['type'] == 'circle' and geom2['type'] == 'ellipse':
            distance = self._ellipse_to_circle_distance(geom2, geom1)
        elif geom1['type'] == 'ellipse' and geom2['type'] == 'arc':
            distance = self._ellipse_to_arc_distance(geom1, geom2)
        elif geom1['type'] == 'arc' and geom2['type'] == 'ellipse':
            distance = self._ellipse_to_arc_distance(geom2, geom1)
        elif geom1['type'] == 'ellipse' and geom2['type'] == 'ellipse':
            distance = self._ellipse_to_ellipse_distance(geom1, geom2)
        elif geom1['type'] == 'point' and geom2['type'] == 'ellipse':
            distance = self._point_to_ellipse_distance(geom1['point'], geom2)
        elif geom1['type'] == 'ellipse' and geom2['type'] == 'point':
            distance = self._point_to_ellipse_distance(geom2['point'], geom1)
        elif geom1['type'] == 'spline' or geom2['type'] == 'spline':
            # 处理包含SPLINE的距离计算
            distance = self._spline_distance_calculation(geom1, geom2)
        else:
            # 其他未实现的复杂几何体回退到端点距离
            distance = self._calculate_endpoint_distance(entity1, entity2)
            
            # 如果端点距离也失败，使用快速距离检查
            if distance == float('inf'):
                distance = self._quick_distance_check(entity1, entity2)
                if distance == float('inf'):
                    # 最后的回退：对于圆弧椭圆不使用中心点，直接返回大距离
                    if entity1['type'] in ['ARC', 'CIRCLE', 'ELLIPSE'] or entity2['type'] in ['ARC', 'CIRCLE', 'ELLIPSE']:
                        distance = 1000  # 对于圆弧椭圆，不使用中心点回退
                    else:
                        # 对于其他类型，仍可使用中心点距离
                        center1 = self._get_entity_center(entity1)
                        center2 = self._get_entity_center(entity2)
                        if center1 and center2:
                            distance = math.sqrt((center1[0] - center2[0])**2 + (center1[1] - center2[1])**2)
                        else:
                            distance = 1000  # 默认大距离
        
        # 简化输出：只在需要时输出关键信息
        
        return distance
    
    def _quick_distance_check(self, entity1, entity2, fallback_threshold=500):
        """快速粗略距离检查，用于性能优化（圆弧椭圆特殊处理）"""
        # 对于圆弧和椭圆，不使用中心点，而是使用端点或采样点
        if entity1['type'] in ['ARC', 'CIRCLE', 'ELLIPSE'] or entity2['type'] in ['ARC', 'CIRCLE', 'ELLIPSE']:
            # 对于圆弧、圆形、椭圆，使用端点距离作为快速检查
            return self._calculate_endpoint_distance(entity1, entity2)

        # 对于其他实体，使用中心点距离
        center1 = self._get_entity_center(entity1)
        center2 = self._get_entity_center(entity2)

        if center1 is None or center2 is None:
            return fallback_threshold

        distance = math.sqrt((center1[0]-center2[0])**2 + (center1[1]-center2[1])**2)

        # 仅当距离很大时作为优化
        if distance > fallback_threshold:
            return distance
        else:
            # 否则返回中心点距离（避免递归）
            return distance
    
    def _get_entity_geometry(self, entity):
        """获取实体的几何表示"""
        if entity['type'] == 'LINE' and 'points' in entity and len(entity['points']) >= 2:
            return {
                'type': 'line',
                'start': entity['points'][0],
                'end': entity['points'][-1]
            }
        elif entity['type'] == 'LINE' and 'start' in entity and 'end' in entity:
            return {
                'type': 'line',
                'start': entity['start'],
                'end': entity['end']
            }
        elif entity['type'] in ('LWPOLYLINE', 'POLYLINE') and 'points' in entity and len(entity['points']) >= 2:
            # 对于多段线，返回所有线段
            return {
                'type': 'polyline',
                'segments': self._get_polyline_segments(entity['points'])
            }
        elif entity['type'] == 'CIRCLE' and 'center' in entity and 'radius' in entity:
            return {
                'type': 'circle',
                'center': entity['center'],
                'radius': entity['radius']
            }
        elif entity['type'] == 'ARC' and 'center' in entity and 'radius' in entity:
            return {
                'type': 'arc',
                'center': entity['center'],
                'radius': entity['radius'],
                'start_angle': math.radians(entity.get('start_angle', 0)),
                'end_angle': math.radians(entity.get('end_angle', 0))
            }
        elif entity['type'] == 'ELLIPSE' and 'center' in entity:
            return {
                'type': 'ellipse',
                'center': entity['center'],
                'major_axis': entity.get('major_axis', (1, 0)),
                'ratio': entity.get('ratio', 1.0),
                'start_param': entity.get('start_param', 0),
                'end_param': entity.get('end_param', math.pi * 2)
            }
        elif entity['type'] in ['TEXT', 'MTEXT', 'INSERT'] and 'position' in entity:
            return {
                'type': 'point',
                'point': entity['position']
            }
        elif entity['type'] == 'SPLINE':
            # 处理SPLINE实体
            if 'control_points' in entity and entity['control_points']:
                return {
                    'type': 'spline',
                    'control_points': entity['control_points'],
                    'fit_points': entity.get('fit_points', [])
                }
            elif 'fit_points' in entity and entity['fit_points']:
                return {
                    'type': 'spline',
                    'fit_points': entity['fit_points'],
                    'control_points': entity.get('control_points', [])
                }
            else:
                return None
        else:
            return None
    
    def _get_polyline_segments(self, points):
        """将多段线点列表转换为线段列表"""
        segments = []
        for i in range(len(points) - 1):
            segments.append({
                'start': points[i],
                'end': points[i + 1]
            })
        return segments
    
    def _polyline_to_polyline_distance(self, polyline1, polyline2):
        """计算两条多段线之间的最短距离"""
        min_distance = float('inf')
        
        for segment1 in polyline1['segments']:
            for segment2 in polyline2['segments']:
                # 检查线段是否相交
                if self._lines_intersect(segment1, segment2):
                    return 0.0  # 相交距离为0
                
                # 计算线段间距离
                distance = self._line_to_line_distance(segment1, segment2)
                min_distance = min(min_distance, distance)
        
        return min_distance
    
    def _polyline_to_line_distance(self, polyline, line):
        """计算多段线到直线的最短距离"""
        min_distance = float('inf')
        
        for segment in polyline['segments']:
            # 检查线段是否相交
            if self._lines_intersect(segment, line):
                return 0.0  # 相交距离为0
            
            # 计算线段间距离
            distance = self._line_to_line_distance(segment, line)
            min_distance = min(min_distance, distance)
        
        return min_distance
    
    def _point_to_polyline_distance(self, point, polyline):
        """计算点到多段线的最短距离"""
        min_distance = float('inf')
        
        for segment in polyline['segments']:
            distance = self._point_to_line_distance(point, segment)
            min_distance = min(min_distance, distance)
        
        return min_distance
    
    def _circle_to_line_distance(self, circle, line):
        """计算圆形到直线的最短距离 - 优化版：只检测圆周上的点"""
        center = circle['center']
        radius = circle['radius']

        # 在圆周上采样多个点来检测最短距离
        # 不再使用圆心到直线的距离判断
        sample_count = 16  # 采样点数量
        min_distance = float('inf')

        for i in range(sample_count):
            angle = 2 * math.pi * i / sample_count
            sample_point = (
                center[0] + radius * math.cos(angle),
                center[1] + radius * math.sin(angle)
            )
            sample_dist = self._point_to_line_distance(sample_point, line)
            min_distance = min(min_distance, sample_dist)

            # 如果发现非常接近的点，可能相交
            if sample_dist < 0.1:
                return 0.0

        return min_distance
    
    def _circle_to_polyline_distance(self, circle, polyline):
        """计算圆形到多段线的最短距离"""
        min_distance = float('inf')
        
        for segment in polyline['segments']:
            distance = self._circle_to_line_distance(circle, segment)
            if distance == 0:
                return 0.0  # 相交距离为0
            min_distance = min(min_distance, distance)
        
        return min_distance
    
    def _arc_to_line_distance(self, arc, line):
        """计算圆弧到直线的最短距离 - 优化版：只检测端点和圆弧上的点"""
        center = arc['center']
        radius = arc['radius']
        start_angle = arc['start_angle']
        end_angle = arc['end_angle']

        # 计算圆弧端点
        start_point = (
            center[0] + radius * math.cos(math.radians(start_angle)),
            center[1] + radius * math.sin(math.radians(start_angle))
        )
        end_point = (
            center[0] + radius * math.cos(math.radians(end_angle)),
            center[1] + radius * math.sin(math.radians(end_angle))
        )

        # 计算端点到直线的距离
        dist1 = self._point_to_line_distance(start_point, line)
        dist2 = self._point_to_line_distance(end_point, line)
        min_distance = min(dist1, dist2)

        # 在圆弧上采样多个点来检测最短距离
        # 不再使用圆心到直线的距离判断
        sample_count = 8  # 采样点数量
        angle_range = end_angle - start_angle

        # 处理跨越360度的情况
        if angle_range < 0:
            angle_range += 360

        if angle_range > 0:
            for i in range(1, sample_count):
                sample_angle = start_angle + (angle_range * i / sample_count)
                sample_point = (
                    center[0] + radius * math.cos(math.radians(sample_angle)),
                    center[1] + radius * math.sin(math.radians(sample_angle))
                )
                sample_dist = self._point_to_line_distance(sample_point, line)
                min_distance = min(min_distance, sample_dist)

                # 如果发现非常接近的点，可能相交
                if sample_dist < 0.1:
                    return 0.0

        return min_distance
    
    def _arc_intersects_line(self, arc, line):
        """检查圆弧是否与直线相交（改进版：使用采样点而非圆心）"""
        # 使用采样点方法检测相交，而不是圆心到直线距离
        arc_points = self._sample_arc_points(arc, num_points=16)

        # 检查圆弧采样点是否有任何一点接近直线
        for point in arc_points:
            distance = self._point_to_line_distance(point, line)
            if distance < 0.1:  # 如果任何采样点接近直线，认为相交
                return True

        return False
    
    def _point_on_line_segment(self, point, line):
        """检查点是否在线段上"""
        x, y = point
        x1, y1 = line['start']
        x2, y2 = line['end']
        
        # 检查点是否在线段的边界框内
        if not (min(x1, x2) <= x <= max(x1, x2) and min(y1, y2) <= y <= max(y1, y2)):
            return False
        
        # 检查点是否在直线上（考虑浮点数精度）
        cross_product = (y - y1) * (x2 - x1) - (x - x1) * (y2 - y1)
        return abs(cross_product) < 1e-6
    
    def _angle_in_arc_range(self, angle, start_angle, end_angle):
        """检查角度是否在圆弧范围内"""
        # 标准化角度到 [0, 2π] 范围
        def normalize_angle(a):
            while a < 0:
                a += 2 * math.pi
            while a >= 2 * math.pi:
                a -= 2 * math.pi
            return a
        
        angle = normalize_angle(angle)
        start = normalize_angle(start_angle)
        end = normalize_angle(end_angle)
        
        if start <= end:
            return start <= angle <= end
        else:
            # 跨越0度的情况
            return angle >= start or angle <= end
    
    def _arc_to_polyline_distance(self, arc, polyline):
        """计算圆弧到多段线的最短距离"""
        min_distance = float('inf')
        
        for segment in polyline['segments']:
            distance = self._arc_to_line_distance(arc, segment)
            if distance == 0:
                return 0.0  # 相交距离为0
            min_distance = min(min_distance, distance)
        
        return min_distance
    
    def _circle_to_circle_distance(self, circle1, circle2):
        """计算两个圆形之间的最短距离（改进版：使用采样点而非圆心）"""
        # 使用采样点方法而不是圆心距离
        circle1_points = self._sample_circle_points(circle1, num_points=16)
        circle2_points = self._sample_circle_points(circle2, num_points=16)

        min_distance = float('inf')

        # 计算两个圆形采样点之间的最短距离
        for p1 in circle1_points:
            for p2 in circle2_points:
                distance = math.sqrt((p1[0] - p2[0])**2 + (p1[1] - p2[1])**2)
                min_distance = min(min_distance, distance)
                if distance < 0.1:  # 如果发现相交，直接返回
                    return 0.0

        return min_distance

    def _sample_circle_points(self, circle, num_points=16):
        """生成圆形上的采样点"""
        center = circle['center']
        radius = circle['radius']

        points = []
        for i in range(num_points):
            angle = 2 * math.pi * i / num_points
            x = center[0] + radius * math.cos(angle)
            y = center[1] + radius * math.sin(angle)
            points.append((x, y))

        return points

    def _sample_arc_points(self, arc, num_points=16):
        """生成圆弧上的采样点"""
        center = arc['center']
        radius = arc['radius']
        start_angle = arc['start_angle']
        end_angle = arc['end_angle']

        # 将角度转换为弧度（如果输入是度数）
        start_angle_rad = math.radians(start_angle)
        end_angle_rad = math.radians(end_angle)

        # 处理角度范围
        angle_range = end_angle_rad - start_angle_rad
        if angle_range < 0:
            angle_range += 2 * math.pi

        points = []
        for i in range(num_points + 1):  # 包括端点
            if num_points == 0:
                angle = start_angle_rad
            else:
                angle = start_angle_rad + (angle_range * i / num_points)

            x = center[0] + radius * math.cos(angle)
            y = center[1] + radius * math.sin(angle)
            points.append((x, y))

        return points

    def _circle_to_arc_distance(self, circle, arc):
        """计算圆形到圆弧的最短距离（改进版：不将圆弧视为完整圆）"""
        # 使用采样点方法，不将圆弧扩展为完整圆
        circle_points = self._sample_circle_points(circle, num_points=16)
        arc_points = self._sample_arc_points(arc, num_points=16)

        min_distance = float('inf')

        # 计算圆形采样点到圆弧采样点的最短距离
        for circle_point in circle_points:
            for arc_point in arc_points:
                distance = math.sqrt((circle_point[0] - arc_point[0])**2 + (circle_point[1] - arc_point[1])**2)
                min_distance = min(min_distance, distance)
                if distance < 0.1:  # 如果发现相交，直接返回
                    return 0.0

        return min_distance
    
    def _arc_to_arc_distance(self, arc1, arc2):
        """计算两个圆弧之间的最短距离（改进版：不将圆弧视为完整圆）"""
        # 使用采样点方法，不将圆弧扩展为完整圆
        arc1_points = self._sample_arc_points(arc1, num_points=16)
        arc2_points = self._sample_arc_points(arc2, num_points=16)

        min_distance = float('inf')

        # 计算两个圆弧采样点之间的最短距离
        for p1 in arc1_points:
            for p2 in arc2_points:
                distance = math.sqrt((p1[0] - p2[0])**2 + (p1[1] - p2[1])**2)
                min_distance = min(min_distance, distance)
                if distance < 0.1:  # 如果发现相交，直接返回
                    return 0.0

        return min_distance
    
    def _point_to_circle_distance(self, point, circle):
        """计算点到圆形的最短距离（改进版：使用采样点而非圆心）"""
        # 使用采样点方法而不是圆心距离
        circle_points = self._sample_circle_points(circle, num_points=16)

        min_distance = float('inf')
        for circle_point in circle_points:
            distance = math.sqrt((point[0] - circle_point[0])**2 + (point[1] - circle_point[1])**2)
            min_distance = min(min_distance, distance)

        return min_distance
    
    def _point_to_arc_distance(self, point, arc):
        """计算点到圆弧的最短距离（改进版：使用采样点而非圆心）"""
        # 使用采样点方法而不是圆心距离
        arc_points = self._sample_arc_points(arc, num_points=16)

        min_distance = float('inf')
        for arc_point in arc_points:
            distance = math.sqrt((point[0] - arc_point[0])**2 + (point[1] - arc_point[1])**2)
            min_distance = min(min_distance, distance)

        return min_distance
    
    def _line_to_line_distance(self, line1, line2):
        """计算两条线段之间的最短距离"""
        # 首先检查线段是否相交
        if self._lines_intersect(line1, line2):
            return 0.0  # 相交距离为0
        
        # 如果不相交，计算各端点到另一条线段的距离，取最小值
        distances = [
            self._point_to_line_distance(line1['start'], line2),
            self._point_to_line_distance(line1['end'], line2),
            self._point_to_line_distance(line2['start'], line1),
            self._point_to_line_distance(line2['end'], line1)
        ]
        
        return min(distances)
    
    def _lines_intersect(self, line1, line2):
        """检查两条线段或多段线是否相交，兼容LINE/LWPOLYLINE/POLYLINE"""
        # 辅助函数：获取所有线段
        def get_segments(entity):
            if entity.get('type') == 'LINE':
                if 'points' in entity and len(entity['points']) >= 2:
                    return [ (entity['points'][0], entity['points'][1]) ]
                elif 'start' in entity and 'end' in entity:
                    return [ (entity['start'], entity['end']) ]
            elif entity.get('type') in ('LWPOLYLINE', 'POLYLINE'):
                pts = entity.get('points', [])
                if len(pts) >= 2:
                    return [ (pts[i], pts[i+1]) for i in range(len(pts)-1) ]
            return []
        
        segs1 = get_segments(line1)
        segs2 = get_segments(line2)
        eps = 1e-6
        def ccw(A, B, C):
            return (C[1]-A[1]) * (B[0]-A[0]) > (B[1]-A[1]) * (C[0]-A[0])
        for A, B in segs1:
            for C, D in segs2:
                # 检查线段是否有效
                if A == B or C == D:
                    continue
                # 标准相交检测
                intersects = ccw(A,C,D) != ccw(B,C,D) and ccw(A,B,C) != ccw(A,B,D)
                # 检查端点重合
                if not intersects:
                    if (abs(A[0] - C[0]) < eps and abs(A[1] - C[1]) < eps) or \
                       (abs(A[0] - D[0]) < eps and abs(A[1] - D[1]) < eps) or \
                       (abs(B[0] - C[0]) < eps and abs(B[1] - C[1]) < eps) or \
                       (abs(B[0] - D[0]) < eps and abs(B[1] - D[1]) < eps):
                        intersects = True
                if intersects:

                    return True
        return False
    
    def _point_to_line_distance(self, point, line):
        """计算点到线段的最短距离"""
        x0, y0 = point

        # 处理不同的直线格式
        if 'start' in line and 'end' in line:
            x1, y1 = line['start']
            x2, y2 = line['end']
        elif 'points' in line and len(line['points']) >= 2:
            x1, y1 = line['points'][0]
            x2, y2 = line['points'][1]
        else:
            raise ValueError(f"无法识别的直线格式: {line.keys()}")
        
        # 计算线段长度的平方
        line_length_sq = (x2 - x1)**2 + (y2 - y1)**2
        
        if line_length_sq == 0:
            # 线段退化为点
            return math.sqrt((x0 - x1)**2 + (y0 - y1)**2)
        
        # 计算点在线段上的投影参数t
        t = max(0, min(1, ((x0 - x1) * (x2 - x1) + (y0 - y1) * (y2 - y1)) / line_length_sq))
        
        # 计算投影点
        proj_x = x1 + t * (x2 - x1)
        proj_y = y1 + t * (y2 - y1)
        
        # 返回点到投影点的距离
        return math.sqrt((x0 - proj_x)**2 + (y0 - proj_y)**2)
    
    def _calculate_endpoint_distance(self, entity1, entity2):
        """计算端点距离（备用方法）"""
        endpoints1 = self._get_entity_endpoints(entity1)
        endpoints2 = self._get_entity_endpoints(entity2)
        
        if not endpoints1 or not endpoints2:
            return float('inf')
        
        min_distance = float('inf')
        for p1 in endpoints1:
            for p2 in endpoints2:
                distance = math.sqrt((p1[0] - p2[0])**2 + (p1[1] - p2[1])**2)
                min_distance = min(min_distance, distance)
                

        
        return min_distance
    
    def _get_entity_endpoints(self, entity):
        """获取实体的端点"""
        endpoints = []
        
        # 检查entity是否有type键
        if not isinstance(entity, dict) or 'type' not in entity:
            return endpoints
        
        if entity['type'] == 'LINE' and 'points' in entity and len(entity['points']) >= 2:
            # 线段的两个端点
            endpoints = [entity['points'][0], entity['points'][-1]]
            
        elif entity['type'] in ('LWPOLYLINE', 'POLYLINE') and 'points' in entity and entity['points']:
            points = entity['points']
            if len(points) >= 2:
                # 对于多段线，所有顶点都可能是连接点
                # 这样可以更好地检测L形、T形等复杂连接
                endpoints = points  # 所有顶点都作为潜在连接点
        
        elif entity['type'] == 'ARC' and 'center' in entity and 'radius' in entity:
            # 圆弧的起点和终点
            center = entity['center']
            radius = entity['radius']
            start_angle = math.radians(entity.get('start_angle', 0))
            end_angle = math.radians(entity.get('end_angle', 0))
            
            start_point = (
                center[0] + radius * math.cos(start_angle),
                center[1] + radius * math.sin(start_angle)
            )
            end_point = (
                center[0] + radius * math.cos(end_angle),
                center[1] + radius * math.sin(end_angle)
            )
            endpoints = [start_point, end_point]
            
        elif entity['type'] == 'ELLIPSE' and 'center' in entity and 'major_axis' in entity:
            # 验证椭圆参数
            if not self._validate_ellipse_parameters(entity):
                return []
            
            # 椭圆的端点处理
            center = entity['center']
            major_axis = entity['major_axis']
            ratio = entity.get('ratio', 1.0)
            start_param = entity.get('start_param', 0)
            end_param = entity.get('end_param', 2 * math.pi)
            
            # 计算椭圆参数
            a = math.sqrt(major_axis[0]**2 + major_axis[1]**2)  # 长轴半径
            b = a * ratio  # 短轴半径
            angle = math.atan2(major_axis[1], major_axis[0])  # 长轴角度
            
            # 检查是否为完整椭圆（增加精度容差）
            param_diff = abs(end_param - start_param)
            if param_diff >= 2 * math.pi - 0.01:  # 增加容差到0.01
                # 完整椭圆没有端点
                endpoints = []
            else:
                # 椭圆弧有起点和终点
                # 使用椭圆参数化方程计算端点
                cos_angle = math.cos(angle)
                sin_angle = math.sin(angle)
                
                # 起点
                cos_start = math.cos(start_param)
                sin_start = math.sin(start_param)
                start_x = center[0] + a * cos_start * cos_angle - b * sin_start * sin_angle
                start_y = center[1] + a * cos_start * sin_angle + b * sin_start * cos_angle
                start_point = (round(start_x, 6), round(start_y, 6))  # 保留6位小数精度
                
                # 终点
                cos_end = math.cos(end_param)
                sin_end = math.sin(end_param)
                end_x = center[0] + a * cos_end * cos_angle - b * sin_end * sin_angle
                end_y = center[1] + a * cos_end * sin_angle + b * sin_end * cos_angle
                end_point = (round(end_x, 6), round(end_y, 6))  # 保留6位小数精度
                
                endpoints = [start_point, end_point]
            
        elif entity['type'] == 'CIRCLE' and 'center' in entity:
            # 圆形没有端点，返回空列表
            endpoints = []
            
        elif entity['type'] in ['TEXT', 'MTEXT', 'INSERT'] and 'position' in entity:
            # 文本和插入点只有一个位置点
            endpoints = [entity['position']]
        
        return endpoints
    
    def _is_room_label(self, text_entities):
        """检测是否为房间标注"""
        room_keywords = ['卧室', '客厅', '厨房', '卫生间', '书房', '餐厅', '阳台', '房间', '起居室', '主卧']
        for entity in text_entities:
            text = entity.get('text', '').strip()
            for keyword in room_keywords:
                if keyword in text:
                    return True
        return False
    
    def _is_dimension_text(self, text_entities):
        """检测是否为尺寸标注文本"""
        dimension_pattern = r'\d+\.?\d*\s*(mm|cm|m|米|厘米|毫米)'
        for entity in text_entities:
            text = entity.get('text', '')
            if re.search(dimension_pattern, text):
                return True
        return False
    
    def _is_title_block(self, text_entities):
        """检测是否为建筑图框"""
        title_keywords = ['图框', '标题栏', '项目名称', '设计单位', '图号', '比例', '日期', '签名']
        for entity in text_entities:
            text = entity.get('text', '').lower()
            for keyword in title_keywords:
                if keyword.lower() in text:
                    return True
        return False
    
    def _calc_rectangularity(self, group):
        """计算组内元素形成的矩形程度"""
        if not group:
            return 0.0
        
        all_points = []
        for entity in group:
            if 'points' in entity and entity['points']:
                all_points.extend(entity['points'])
            elif 'center' in entity and 'radius' in entity:
                center = entity['center']
                radius = entity['radius']
                all_points.extend([
                    (center[0] - radius, center[1] - radius),
                    (center[0] + radius, center[1] + radius)
                ])
        
        if not all_points:
            return 0.0
        
        points = np.array(all_points)
        min_x, min_y = np.min(points, axis=0)
        max_x, max_y = np.max(points, axis=0)
        
        actual_area = 0
        for entity in group:
            if entity['type'] in ('LWPOLYLINE', 'POLYLINE') and entity.get('closed', False):
                try:
                    poly = Polygon(entity['points'])
                    actual_area += poly.area
                except:
                    pass
            elif entity['type'] == 'CIRCLE' and 'radius' in entity:
                actual_area += np.pi * entity['radius'] ** 2
        
        rect_area = (max_x - min_x) * (max_y - min_y)
        if rect_area > 0:
            return actual_area / rect_area
        return 0.0
    
    def _calc_symmetry(self, group):
        """计算对称性分数"""
        if not group:
            return 0.0
        
        center = self._get_group_center(group)
        if center is None:
            return 0.0
        
        symmetry_score = 0.0
        entity_count = 0
        
        for entity in group:
            if 'points' in entity and entity['points']:
                points = np.array(entity['points'])
                for point in points:
                    for other_entity in group:
                        if other_entity != entity and 'points' in other_entity and other_entity['points']:
                            other_points = np.array(other_entity['points'])
                            for other_point in other_points:
                                if isinstance(center, np.ndarray):
                                    center_x, center_y = center[0], center[1]
                                else:
                                    center_x, center_y = center[0], center[1]
                                if isinstance(point, np.ndarray):
                                    point_x, point_y = point[0], point[1]
                                else:
                                    point_x, point_y = point[0], point[1]
                                sym_x = 2 * center_x - point_x
                                sym_y = 2 * center_y - point_y
                                for check_point in other_points:
                                    if isinstance(check_point, np.ndarray):
                                        check_x, check_y = check_point[0], check_point[1]
                                    else:
                                        check_x, check_y = check_point[0], check_point[1]
                                    if np.sqrt((sym_x - check_x)**2 + (sym_y - check_y)**2) < 10:
                                        symmetry_score += 1
                                entity_count += 1
        
        if entity_count > 0:
            return symmetry_score / entity_count
        return 0.0
    
    def _detect_keywords(self, group):
        """检测关键词"""
        keywords = []
        for entity in group:
            if entity['type'] in ['TEXT', 'MTEXT']:
                text = entity.get('text', '').lower()
                if '门' in text or 'door' in text:
                    keywords.append('door')
                elif '窗' in text or 'window' in text:
                    keywords.append('window')
                elif '墙' in text or 'wall' in text:
                    keywords.append('wall')
        return len(keywords)
    
    def _calc_color_variance(self, group):
        """计算颜色方差（基于图层）"""
        layers = [entity['layer'] for entity in group]
        if len(set(layers)) > 1:
            return len(set(layers))
        return 1
    
    def _calc_angle_variance(self, group):
        """计算角度方差"""
        angles = []
        for entity in group:
            if entity['type'] == 'LINE' and 'points' in entity and len(entity['points']) >= 2:
                p1, p2 = entity['points'][:2]
                angle = math.atan2(p2[1] - p1[1], p2[0] - p1[0])
                angles.append(angle)
        
        if angles:
            return np.var(angles)
        return 0.0
    
    def _calc_feature_hash(self, features):
        """计算特征哈希"""
        feature_str = str(sorted(features.items()))
        return hash(feature_str) % 10000
    
    def _infer_category_from_layer(self, entity_group):
        """根据图层名称推断实体组类别"""
        layer_counts = defaultdict(int)
        for entity in entity_group:
            layer = entity['layer'].lower()
            layer_counts[layer] += 1
        
        for layer, count in layer_counts.items():
            if '墙' in layer or 'wall' in layer:
                return 'wall'
            elif '门' in layer or 'door' in layer:
                return 'door'
            elif '窗' in layer or 'window' in layer:
                return 'window'
            elif '栏杆' in layer or '护栏' in layer or 'railing' in layer or 'handrail' in layer or '扶手' in layer:
                return 'railing'
            elif '家具' in layer or 'furniture' in layer:
                return 'furniture'
            elif '楼梯' in layer or 'stair' in layer:
                return 'stair'
            elif '电梯' in layer or 'elevator' in layer:
                return 'elevator'
            elif '标注' in layer or 'dim' in layer:
                return 'dimension'
            elif '文字' in layer or 'text' in layer:
                return 'room_label'
        
        return None

    def _is_dashed_linetype(self, linetype):
        """增强版虚线类型识别"""
        if not linetype:
            return False

        linetype_upper = linetype.upper()

        # 标准虚线类型
        standard_dash_types = [
            'DASHED', 'DASH', 'DASHDOT', 'DASHDOTDOT', 'DOT', 'DOTTED',
            'HIDDEN', 'CENTER', 'PHANTOM', 'DIVIDE'
        ]

        # 检查标准类型
        if linetype_upper in standard_dash_types:
            return True

        # 检查包含虚线关键词的类型
        dash_keywords = ['DASH', 'DOT', 'HIDDEN', 'CENTER', 'PHANTOM']
        for keyword in dash_keywords:
            if keyword in linetype_upper:
                return True

        # 检查AutoCAD常见虚线类型
        autocad_dash_types = [
            'ACAD_ISO02W100', 'ACAD_ISO03W100', 'ACAD_ISO04W100', 'ACAD_ISO05W100',
            'ACAD_ISO06W100', 'ACAD_ISO07W100', 'ACAD_ISO08W100', 'ACAD_ISO09W100',
            'ACAD_ISO10W100', 'ACAD_ISO11W100', 'ACAD_ISO12W100', 'ACAD_ISO13W100',
            'ACAD_ISO14W100', 'ACAD_ISO15W100'
        ]

        if linetype_upper in autocad_dash_types:
            return True

        # 检查中文虚线类型
        chinese_dash_types = ['虚线', '点线', '中心线', '隐藏线', '幻影线']
        for dash_type in chinese_dash_types:
            if dash_type in linetype:
                return True

        return False

    def _build_linetype_layer_mapping(self, doc):
        """🔧 步骤1：建立线型-图层映射关系"""
        try:


            # 获取DXF版本信息
            self.dxf_version = doc.dxfversion
            print(f"📋 DXF版本: {self.dxf_version}")

            # 获取全局线型比例
            try:
                self.global_ltscale = doc.header.get('$LTSCALE', 1.0)
                self.psltscale = doc.header.get('$PSLTSCALE', 1.0)
                print(f"📏 全局线型比例: {self.global_ltscale}, 图纸空间比例: {self.psltscale}")
            except:
                self.global_ltscale = 1.0
                self.psltscale = 1.0

            # 步骤1.1：建立线型名称到线型实体的映射
            self.linetype_dict = {}
            linetype_count = 0

            # 添加默认的连续线型定义（支持不同大小写）
            default_continuous_types = ['CONTINUOUS', 'Continuous', 'continuous', 'BYLAYER', 'BYBLOCK']
            for linetype_name in default_continuous_types:
                self.linetype_dict[linetype_name] = None  # 连续线型不需要实际定义

            for entity in doc.entities:
                if entity.dxftype() == "LTYPE":
                    self.linetype_dict[entity.dxf.name] = entity
                    linetype_count += 1

            print(f"📊 发现线型定义: {linetype_count} 个")
            if linetype_count > 0:
                print(f"   线型列表: {list(self.linetype_dict.keys())}")

            # 步骤1.2：建立图层到线型的映射
            self.layer_linetype_mapping = {}
            layer_count = 0
            missing_linetype_count = 0

            for layer in doc.layers:
                layer_name = layer.dxf.name
                linetype_name = getattr(layer.dxf, 'linetype', 'CONTINUOUS')

                if linetype_name in self.linetype_dict:
                    self.layer_linetype_mapping[layer_name] = {
                        'linetype_name': linetype_name,
                        'linetype_entity': self.linetype_dict[linetype_name],
                        'is_dashed': self._is_dashed_linetype(linetype_name)
                    }
                else:
                    self.layer_linetype_mapping[layer_name] = {
                        'linetype_name': linetype_name,
                        'linetype_entity': None,
                        'is_dashed': self._is_dashed_linetype(linetype_name)
                    }
                    # 检查是否为连续线型（不区分大小写）
                    if linetype_name.upper() not in ['CONTINUOUS', 'BYLAYER', 'BYBLOCK']:
                        missing_linetype_count += 1
                        print(f"⚠️ 图层 '{layer_name}' 引用的线型 '{linetype_name}' 未找到定义")

                layer_count += 1

            print(f"📊 处理图层: {layer_count} 个, 缺失线型定义: {missing_linetype_count} 个")

            # 步骤1.3：版本兼容性处理
            if self.dxf_version and self.dxf_version <= "AC1009":  # R12/R13
                print("🔧 检测到旧版DXF，启用兼容性处理...")
                self._convert_iso_linetypes_to_legacy()

            return True

        except Exception as e:
            print(f"❌ 建立线型-图层映射失败: {e}")
            return False

    def _convert_iso_linetypes_to_legacy(self):
        """🔧 步骤4：版本兼容性处理 - 将ACAD_ISO线型转为传统线型"""
        try:
            conversion_map = {
                'ACAD_ISO02W100': 'DASHED',
                'ACAD_ISO03W100': 'DASHDOT',
                'ACAD_ISO04W100': 'DASHDOTDOT',
                'ACAD_ISO05W100': 'CENTER',
                'ACAD_ISO06W100': 'PHANTOM',
                'ACAD_ISO07W100': 'HIDDEN',
                'ACAD_ISO08W100': 'DOTTED',
                'ACAD_ISO09W100': 'DASHED',
                'ACAD_ISO10W100': 'DASHDOT',
                'ACAD_ISO11W100': 'DASHDOTDOT',
                'ACAD_ISO12W100': 'CENTER',
                'ACAD_ISO13W100': 'PHANTOM',
                'ACAD_ISO14W100': 'HIDDEN',
                'ACAD_ISO15W100': 'DOTTED'
            }

            converted_count = 0
            for layer_name, layer_info in self.layer_linetype_mapping.items():
                linetype_name = layer_info['linetype_name']
                if linetype_name in conversion_map:
                    new_linetype = conversion_map[linetype_name]
                    layer_info['linetype_name'] = new_linetype
                    layer_info['is_dashed'] = self._is_dashed_linetype(new_linetype)
                    converted_count += 1
                    print(f"🔄 转换线型: {linetype_name} → {new_linetype} (图层: {layer_name})")

            if converted_count > 0:
                print(f"✅ 完成 {converted_count} 个ISO线型的兼容性转换")

        except Exception as e:
            print(f"❌ ISO线型转换失败: {e}")

    def get_effective_linetype_scale(self, entity):
        """🔧 步骤3：动态计算线型比例"""
        try:
            # 获取实体级比例 (默认1.0)
            celtscale = getattr(entity.dxf, 'ltscale', 1.0)
            if celtscale == 0:
                celtscale = 1.0

            # 全局比例 × 图纸空间比例 × 实体比例
            effective_scale = celtscale * self.global_ltscale * self.psltscale
            return effective_scale

        except:
            return 1.0

    def _extract_enhanced_linetype_info(self, entity):
        """🔧 步骤2：增强线型信息提取（支持块参照继承）"""
        try:
            # 获取实体的基本线型信息
            entity_linetype = getattr(entity.dxf, 'linetype', 'BYLAYER')
            entity_layer = getattr(entity.dxf, 'layer', '0')

            # 步骤2.1：处理线型继承逻辑
            effective_linetype = self._resolve_effective_linetype(entity_linetype, entity_layer, entity)

            # 步骤2.2：计算有效线型比例
            effective_scale = self.get_effective_linetype_scale(entity)

            # 步骤2.3：判断是否为虚线
            is_dashed = self._is_dashed_linetype(effective_linetype)

            # 步骤2.4：获取详细线型信息
            linetype_details = self._get_linetype_details(effective_linetype)

            return {
                'linetype': effective_linetype,
                'linetype_original': entity_linetype,
                'linetype_scale': effective_scale,
                'is_dashed': is_dashed,
                'linetype_details': linetype_details
            }

        except Exception as e:
            print(f"⚠️ 线型信息提取失败: {e}")
            return {
                'linetype': 'CONTINUOUS',
                'linetype_original': 'CONTINUOUS',
                'linetype_scale': 1.0,
                'is_dashed': False,
                'linetype_details': None
            }

    def _resolve_effective_linetype(self, entity_linetype, entity_layer, entity):
        """🔧 步骤2：解析有效线型（处理BYLAYER、BYBLOCK等）"""
        try:
            # 情况1：具体线型名称 - 直接使用（不区分大小写检查连续线型）
            if entity_linetype and entity_linetype.upper() not in ['BYLAYER', 'BYBLOCK', 'CONTINUOUS']:
                return entity_linetype

            # 情况2：BYLAYER - 使用图层线型
            if entity_linetype.upper() == 'BYLAYER' or not entity_linetype:
                if entity_layer in self.layer_linetype_mapping:
                    layer_linetype = self.layer_linetype_mapping[entity_layer]['linetype_name']
                    return layer_linetype if layer_linetype else 'CONTINUOUS'
                else:
                    return 'CONTINUOUS'

            # 情况3：BYBLOCK - 使用块定义中的实体线型
            if entity_linetype.upper() == 'BYBLOCK':
                # 对于块参照，需要查找块定义中的线型
                if entity.dxftype() == 'INSERT':
                    # 这里可以进一步处理块内实体的线型
                    # 暂时返回图层线型作为fallback
                    if entity_layer in self.layer_linetype_mapping:
                        layer_linetype = self.layer_linetype_mapping[entity_layer]['linetype_name']
                        return layer_linetype if layer_linetype else 'CONTINUOUS'
                return 'CONTINUOUS'

            # 情况4：CONTINUOUS或其他 - 返回连续线
            return 'CONTINUOUS'

        except Exception as e:
            print(f"⚠️ 线型解析失败: {e}")
            return 'CONTINUOUS'

    def _get_linetype_details(self, linetype_name):
        """获取线型详细信息"""
        try:
            if linetype_name in self.linetype_dict:
                linetype_entity = self.linetype_dict[linetype_name]
                # 可以从线型实体中提取更多信息，如虚实模式等
                return {
                    'name': linetype_name,
                    'description': getattr(linetype_entity.dxf, 'description', ''),
                    'pattern_length': getattr(linetype_entity.dxf, 'pattern_length', 0),
                    'has_pattern': hasattr(linetype_entity, 'pattern_tags')
                }
            else:
                return {
                    'name': linetype_name,
                    'description': '',
                    'pattern_length': 0,
                    'has_pattern': False
                }
        except:
            return None

    # 椭圆距离计算方法
    def _ellipse_to_line_distance(self, ellipse, line):
        """计算椭圆到直线的最短距离"""
        # 方法1: 使用采样点方法计算椭圆上点到直线的距离
        ellipse_points = self._sample_ellipse_points(ellipse, num_points=64)  # 增加采样点数量
        
        min_distance = float('inf')
        for point in ellipse_points:
            distance = self._point_to_line_distance(point, line)
            min_distance = min(min_distance, distance)
            if distance < 0.1:  # 如果发现非常接近的点，认为是相交
                return 0.0
        
        # 方法2: 检查椭圆的端点到直线的距离（如果椭圆有端点）
        ellipse_endpoints = self._get_entity_endpoints(ellipse)
        if ellipse_endpoints:
            for endpoint in ellipse_endpoints:
                distance = self._point_to_line_distance(endpoint, line)
                min_distance = min(min_distance, distance)
                if distance < 0.1:  # 如果发现相交，直接返回
                    return 0.0
        
        # 方法3: 检查直线的端点到椭圆的距离
        line_endpoints = self._get_entity_endpoints(line)
        if line_endpoints:
            for endpoint in line_endpoints:
                distance = self._point_to_ellipse_distance(endpoint, ellipse)
                min_distance = min(min_distance, distance)
                if distance < 0.1:  # 如果发现相交，直接返回
                    return 0.0
        
        # 方法4: 检查椭圆采样点到直线的距离，寻找相交
        for point in ellipse_points:
            distance = self._point_to_line_distance(point, line)
            if distance < 0.1:  # 如果发现相交，直接返回
                return 0.0
        
        return min_distance
    
    def _ellipse_to_polyline_distance(self, ellipse, polyline):
        """计算椭圆到多段线的最短距离"""
        min_distance = float('inf')
        
        # 方法1: 检查椭圆到每个线段的距离
        for segment in polyline['segments']:
            distance = self._ellipse_to_line_distance(ellipse, segment)
            if distance < 0.1:
                return 0.0  # 相交距离为0
            min_distance = min(min_distance, distance)
        
        # 方法2: 检查椭圆的端点到多段线的距离
        ellipse_endpoints = self._get_entity_endpoints(ellipse)
        if ellipse_endpoints:
            for endpoint in ellipse_endpoints:
                distance = self._point_to_polyline_distance(endpoint, polyline)
                min_distance = min(min_distance, distance)
                if distance < 0.1:  # 如果发现相交，直接返回
                    return 0.0
        
        # 方法3: 检查多段线的端点到椭圆的距离
        polyline_endpoints = self._get_entity_endpoints(polyline)
        if polyline_endpoints:
            for endpoint in polyline_endpoints:
                distance = self._point_to_ellipse_distance(endpoint, ellipse)
                min_distance = min(min_distance, distance)
                if distance < 0.1:  # 如果发现相交，直接返回
                    return 0.0
        
        return min_distance
    
    def _ellipse_to_circle_distance(self, ellipse, circle):
        """计算椭圆到圆形的最短距离"""
        # 方法1: 使用采样点方法计算椭圆上点到圆形的距离
        ellipse_points = self._sample_ellipse_points(ellipse, num_points=64)
        
        min_distance = float('inf')
        for point in ellipse_points:
            distance = self._point_to_circle_distance(point, circle)
            min_distance = min(min_distance, distance)
            if distance < 0.1:  # 如果发现相交，直接返回
                return 0.0
        
        # 方法2: 检查椭圆的端点到圆形的距离
        ellipse_endpoints = self._get_entity_endpoints(ellipse)
        if ellipse_endpoints:
            for endpoint in ellipse_endpoints:
                distance = self._point_to_circle_distance(endpoint, circle)
                min_distance = min(min_distance, distance)
                if distance < 0.1:  # 如果发现相交，直接返回
                    return 0.0
        
        # 方法3: 检查圆形采样点到椭圆的距离（不使用圆心）
        circle_points = self._sample_circle_points(circle, num_points=16)
        for point in circle_points:
            distance = self._point_to_ellipse_distance(point, ellipse)
            min_distance = min(min_distance, distance)
            if distance < 0.1:  # 如果发现相交，直接返回
                return 0.0
        
        return min_distance
    
    def _ellipse_to_arc_distance(self, ellipse, arc):
        """计算椭圆到圆弧的最短距离"""
        # 方法1: 使用采样点方法计算椭圆上点到圆弧的距离
        ellipse_points = self._sample_ellipse_points(ellipse, num_points=64)
        
        min_distance = float('inf')
        for point in ellipse_points:
            distance = self._point_to_arc_distance(point, arc)
            min_distance = min(min_distance, distance)
            if distance < 0.1:  # 如果发现相交，直接返回
                return 0.0
        
        # 方法2: 检查椭圆的端点到圆弧的距离
        ellipse_endpoints = self._get_entity_endpoints(ellipse)
        if ellipse_endpoints:
            for endpoint in ellipse_endpoints:
                distance = self._point_to_arc_distance(endpoint, arc)
                min_distance = min(min_distance, distance)
                if distance < 0.1:  # 如果发现相交，直接返回
                    return 0.0
        
        # 方法3: 检查圆弧的端点到椭圆的距离
        arc_endpoints = self._get_entity_endpoints(arc)
        if arc_endpoints:
            for endpoint in arc_endpoints:
                distance = self._point_to_ellipse_distance(endpoint, ellipse)
                min_distance = min(min_distance, distance)
                if distance < 0.1:  # 如果发现相交，直接返回
                    return 0.0
        
        return min_distance
    
    def _ellipse_to_ellipse_distance(self, ellipse1, ellipse2):
        """计算两个椭圆之间的最短距离"""
        # 方法1: 使用采样点方法计算椭圆上点之间的距离
        ellipse1_points = self._sample_ellipse_points(ellipse1, num_points=64)  # 增加采样点数量
        ellipse2_points = self._sample_ellipse_points(ellipse2, num_points=64)
        
        min_distance = float('inf')
        for p1 in ellipse1_points:
            for p2 in ellipse2_points:
                distance = math.sqrt((p1[0] - p2[0])**2 + (p1[1] - p2[1])**2)
                min_distance = min(min_distance, distance)
                if distance < 0.1:  # 如果发现非常接近的点，认为是相交
                    return 0.0
        
        # 方法2: 检查椭圆1的端点到椭圆2的距离
        ellipse1_endpoints = self._get_entity_endpoints(ellipse1)
        if ellipse1_endpoints:
            for endpoint in ellipse1_endpoints:
                distance = self._point_to_ellipse_distance(endpoint, ellipse2)
                min_distance = min(min_distance, distance)
                if distance < 0.1:  # 如果发现相交，直接返回
                    return 0.0
        
        # 方法3: 检查椭圆2的端点到椭圆1的距离
        ellipse2_endpoints = self._get_entity_endpoints(ellipse2)
        if ellipse2_endpoints:
            for endpoint in ellipse2_endpoints:
                distance = self._point_to_ellipse_distance(endpoint, ellipse1)
                min_distance = min(min_distance, distance)
                if distance < 0.1:  # 如果发现相交，直接返回
                    return 0.0
        
        # 方法4: 检查椭圆1的采样点到椭圆2的采样点，寻找相交
        for p1 in ellipse1_points:
            for p2 in ellipse2_points:
                # 检查两点是否非常接近（可能是相交点）
                dist = math.sqrt((p1[0] - p2[0])**2 + (p1[1] - p2[1])**2)
                if dist < 0.1:  # 如果发现相交，直接返回
                    return 0.0
        
        # 方法5: 如果上述方法都失败，返回一个大的默认值（不使用中心点）
        if min_distance == float('inf'):
            # 不再使用中心点距离作为回退，而是返回一个表示"很远"的值
            return 1000.0
        
        return min_distance
    
    def _point_to_ellipse_distance(self, point, ellipse):
        """计算点到椭圆的最短距离"""
        # 使用采样点方法近似计算
        ellipse_points = self._sample_ellipse_points(ellipse, num_points=32)
        
        min_distance = float('inf')
        for ellipse_point in ellipse_points:
            distance = math.sqrt((point[0] - ellipse_point[0])**2 + (point[1] - ellipse_point[1])**2)
            min_distance = min(min_distance, distance)
        
        return min_distance
    
    def _sample_ellipse_points(self, ellipse, num_points=16):
        """生成椭圆上的采样点"""
        center = ellipse['center']
        major_axis = ellipse['major_axis']
        ratio = ellipse.get('ratio', 1.0)
        start_param = ellipse.get('start_param', 0)
        end_param = ellipse.get('end_param', 2 * math.pi)
        
        # 计算椭圆参数
        a = math.sqrt(major_axis[0]**2 + major_axis[1]**2)  # 长轴半径
        b = a * ratio  # 短轴半径
        angle = math.atan2(major_axis[1], major_axis[0])  # 长轴角度
        
        # 检查是否为完整椭圆
        param_diff = abs(end_param - start_param)
        if param_diff >= 2 * math.pi - 0.01:
            # 完整椭圆，生成均匀分布的采样点
            points = []
            for i in range(num_points):
                t = 2 * math.pi * i / num_points
                cos_t = math.cos(t)
                sin_t = math.sin(t)
                
                x = center[0] + a * cos_t * math.cos(angle) - b * sin_t * math.sin(angle)
                y = center[1] + a * cos_t * math.sin(angle) + b * sin_t * math.cos(angle)
                points.append((round(x, 6), round(y, 6)))
        else:
            # 椭圆弧，在参数范围内生成采样点
            points = []
            for i in range(num_points):
                t = start_param + (end_param - start_param) * i / (num_points - 1)
                cos_t = math.cos(t)
                sin_t = math.sin(t)
                
                x = center[0] + a * cos_t * math.cos(angle) - b * sin_t * math.sin(angle)
                y = center[1] + a * cos_t * math.sin(angle) + b * sin_t * math.cos(angle)
                points.append((round(x, 6), round(y, 6)))
        
        return points
    
    def _spline_distance_calculation(self, geom1, geom2):
        """计算包含SPLINE的距离"""
        # 获取SPLINE的采样点
        spline1_points = self._get_spline_points(geom1)
        spline2_points = self._get_spline_points(geom2)
        
        if not spline1_points or not spline2_points:
            # 如果无法获取采样点，使用中心点距离
            center1 = self._get_spline_center(geom1)
            center2 = self._get_spline_center(geom2)
            if center1 and center2:
                return math.sqrt((center1[0] - center2[0])**2 + (center1[1] - center2[1])**2)
            return 1000  # 默认大距离
        
        # 计算两组采样点之间的最小距离
        min_distance = float('inf')
        for p1 in spline1_points:
            for p2 in spline2_points:
                dist = math.sqrt((p1[0] - p2[0])**2 + (p1[1] - p2[1])**2)
                min_distance = min(min_distance, dist)
        
        return min_distance if min_distance != float('inf') else 1000
    
    def _get_spline_points(self, geom):
        """获取SPLINE的采样点"""
        if geom['type'] != 'spline':
            return []
        
        points = []
        
        # 优先使用拟合点
        if 'fit_points' in geom and geom['fit_points']:
            points = geom['fit_points']
        # 其次使用控制点
        elif 'control_points' in geom and geom['control_points']:
            points = geom['control_points']
        
        return points
    
    def _get_spline_center(self, geom):
        """获取SPLINE的中心点"""
        points = self._get_spline_points(geom)
        if points:
            points_arr = np.array(points)
            center = np.mean(points_arr, axis=0)
            return tuple(center)
        return None
    
    def _find_nearest_group_for_entity(self, entity, groups):
        """为实体找到最近的组（优化版：对圆弧使用真实几何距离）"""
        if not groups:
            return None

        nearest_group = None
        min_distance = float('inf')

        for group in groups:
            # 计算实体到组中所有实体的最小距离
            group_min_distance = float('inf')

            for group_entity in group:
                if entity['type'] in ['ARC', 'CIRCLE', 'ELLIPSE'] or group_entity['type'] in ['ARC', 'CIRCLE', 'ELLIPSE']:
                    # 对于圆弧、圆形、椭圆，使用几何距离
                    distance = self._calculate_entity_distance(entity, group_entity)
                else:
                    # 对于其他类型，使用中心点距离
                    entity_center = self._get_entity_center(entity)
                    group_entity_center = self._get_entity_center(group_entity)

                    if entity_center and group_entity_center:
                        distance = math.sqrt((entity_center[0] - group_entity_center[0])**2 +
                                           (entity_center[1] - group_entity_center[1])**2)
                    else:
                        distance = float('inf')

                group_min_distance = min(group_min_distance, distance)

            if group_min_distance < min_distance:
                min_distance = group_min_distance
                nearest_group = group

        return nearest_group
    
    def _force_merge_spline_entities(self, groups):
        """强制合并SPLINE实体到最近的组中"""
        if not groups:
            return groups

        # 收集所有SPLINE实体
        spline_entities = []
        non_spline_groups = []

        for i, group in enumerate(groups):
            if not group:
                continue

            # 处理不同格式的组（字典格式或列表格式）
            if isinstance(group, dict):
                entities = group.get('entities', [])
                group_format = 'dict'
            elif isinstance(group, list):
                entities = group
                group_format = 'list'
            else:
                continue

            # 分离SPLINE和非SPLINE实体
            spline_in_group = [e for e in entities if isinstance(e, dict) and e.get('type') == 'SPLINE']
            non_spline_in_group = [e for e in entities if isinstance(e, dict) and e.get('type') != 'SPLINE']

            if spline_in_group:
                # 过滤掉无效的SPLINE实体
                valid_spline_in_group = []
                for spline in spline_in_group:
                    bbox = self._get_entity_bbox(spline)
                    if bbox and bbox[2] - bbox[0] > 0.1 and bbox[3] - bbox[1] > 0.1:
                        valid_spline_in_group.append(spline)

                if valid_spline_in_group:
                    spline_entities.extend(valid_spline_in_group)

            if non_spline_in_group:
                # 保持原有的组格式
                if group_format == 'dict':
                    # 创建新的字典格式组，但只包含非SPLINE实体
                    new_group = group.copy()
                    new_group['entities'] = non_spline_in_group
                    non_spline_groups.append(new_group)
                else:
                    # 列表格式直接添加
                    non_spline_groups.append(non_spline_in_group)
        
        if not spline_entities:
            return groups

        # 将SPLINE实体分配到最近的组
        for spline_entity in spline_entities:
            nearest_group = None
            min_distance = float('inf')

            # 计算SPLINE实体的中心点
            spline_center = self._get_entity_center(spline_entity)
            if not spline_center:
                continue

            # 找到最近的组
            for i, group in enumerate(non_spline_groups):
                # 处理不同格式的组
                if isinstance(group, dict):
                    group_entities = group.get('entities', [])
                else:
                    group_entities = group

                group_center = self._get_group_center(group_entities)
                if not group_center:
                    continue

                distance = math.sqrt((spline_center[0] - group_center[0])**2 +
                                   (spline_center[1] - group_center[1])**2)

                if distance < min_distance:
                    min_distance = distance
                    nearest_group = group

            if nearest_group:
                # 根据组格式添加SPLINE实体
                if isinstance(nearest_group, dict):
                    nearest_group['entities'].append(spline_entity)
                else:
                    nearest_group.append(spline_entity)
            else:
                # 如果没有合适的组，创建新组
                non_spline_groups.append([spline_entity])
        return non_spline_groups
    
    def merge_contained_wall_groups(self, groups):
        """合并完全包含在另一个墙体组范围内的墙体组"""
        if not groups:
            return groups

        # 筛选出墙体组
        wall_groups = []
        for group in groups:
            # 修复：处理不同的组格式
            entities_to_check = []
            if isinstance(group, dict) and 'entities' in group:
                # 如果是字典格式的组，获取实体列表
                entities_to_check = group['entities']
            elif isinstance(group, list):
                # 如果是实体列表
                entities_to_check = group
            else:
                print(f"⚠️ 未知的组格式: {type(group)}")
                continue

            # 检查是否是墙体组（通过标签或图层判断）
            is_wall_group = False
            for entity in entities_to_check:
                # 修复：检查entity是否为字典类型
                if isinstance(entity, str):
                    # 如果entity是字符串，跳过这个实体
                    continue
                elif isinstance(entity, dict):
                    if entity.get('label') == 'wall' or entity.get('auto_labeled', False):
                        is_wall_group = True
                        break
                    # 也可以通过图层判断
                    if any(pattern in entity.get('layer', '').lower() for pattern in ['wall', '墙']):
                        is_wall_group = True
                        break
                else:
                    # 如果entity不是字典也不是字符串，尝试获取属性
                    try:
                        if hasattr(entity, 'get'):
                            if entity.get('label') == 'wall' or entity.get('auto_labeled', False):
                                is_wall_group = True
                                break
                            if any(pattern in entity.get('layer', '').lower() for pattern in ['wall', '墙']):
                                is_wall_group = True
                                break
                    except Exception as e:
                        print(f"⚠️ 处理实体时出错: {type(entity)}, {e}")
                        continue
            
            if is_wall_group:
                wall_groups.append(group)
        
        if len(wall_groups) < 2:
            return groups
        
        print(f"开始合并包含的墙体组，共 {len(wall_groups)} 个墙体组")
        
        # 计算每个墙体组的边界框
        wall_group_bboxes = []
        for i, group in enumerate(wall_groups):
            bbox = self._get_group_bbox(group)
            if bbox:
                wall_group_bboxes.append((i, group, bbox))
        
        # 检查包含关系并合并
        merged_groups = []
        used_indices = set()
        
        for i, (idx1, group1, bbox1) in enumerate(wall_group_bboxes):
            if idx1 in used_indices:
                continue
            
            contained_groups = []
            
            # 检查group1是否包含其他组
            for j, (idx2, group2, bbox2) in enumerate(wall_group_bboxes):
                if i == j or idx2 in used_indices:
                    continue
                
                # 检查bbox2是否完全在bbox1内
                if self._is_bbox_contained(bbox2, bbox1):
                    # 🔑 增强调试信息：显示边界框和面积信息
                    area1 = (bbox1[2] - bbox1[0]) * (bbox1[3] - bbox1[1])
                    area2 = (bbox2[2] - bbox2[0]) * (bbox2[3] - bbox2[1])
                    area_ratio = area2 / area1 if area1 > 0 else 0
                    print(f"发现包含关系: 组{idx1} 包含 组{idx2} (面积比: {area_ratio:.2f})")
                    contained_groups.append(group2)
                    used_indices.add(idx2)
            
            # 如果有包含的组，合并它们
            if contained_groups:
                # 修复：处理不同格式的组合并
                if isinstance(group1, dict) and 'entities' in group1:
                    # 字典格式的组
                    merged_group = group1.copy()
                    merged_entities = merged_group['entities'].copy()

                    for contained_group in contained_groups:
                        if isinstance(contained_group, dict) and 'entities' in contained_group:
                            merged_entities.extend(contained_group['entities'])
                        elif isinstance(contained_group, list):
                            merged_entities.extend(contained_group)

                    merged_group['entities'] = merged_entities
                elif isinstance(group1, list):
                    # 列表格式的组
                    merged_group = group1.copy()
                    for contained_group in contained_groups:
                        if isinstance(contained_group, dict) and 'entities' in contained_group:
                            merged_group.extend(contained_group['entities'])
                        elif isinstance(contained_group, list):
                            merged_group.extend(contained_group)
                else:
                    # 未知格式，保持原样
                    merged_group = group1

                # 简化输出
                merged_groups.append(merged_group)
                used_indices.add(idx1)
            else:
                # 没有被包含，也没有包含其他组，保持原样
                merged_groups.append(group1)
                used_indices.add(idx1)
        
        # 构建最终结果
        result_groups = []

        # 添加合并后的墙体组
        result_groups.extend(merged_groups)

        # 添加非墙体组
        for group in groups:
            if group not in wall_groups:
                result_groups.append(group)

        # 🔑 增强统计信息
        merge_ratio = (len(wall_groups) - len(merged_groups)) / len(wall_groups) * 100 if len(wall_groups) > 0 else 0
        print(f"墙体组合并完成: {len(wall_groups)} -> {len(merged_groups)} (合并率: {merge_ratio:.1f}%)")

        # 如果合并率过高，发出警告
        if merge_ratio > 20:
            print(f"  ⚠️ 合并率过高 ({merge_ratio:.1f}%)，可能存在过度合并")
        elif merge_ratio > 0:
            print(f"  ✅ 合并率正常 ({merge_ratio:.1f}%)")

        return result_groups
    
    def _is_bbox_contained(self, inner_bbox, outer_bbox):
        """检查内边界框是否完全包含在外边界框内（修复版 - 减少过度合并）"""
        if not inner_bbox or not outer_bbox:
            return False

        # 🔑 修复：大幅减少容差，避免过度合并
        tolerance = 0.1  # 从1.0减少到0.1

        # 检查基本包含关系
        is_contained = (inner_bbox[0] >= outer_bbox[0] - tolerance and
                       inner_bbox[1] >= outer_bbox[1] - tolerance and
                       inner_bbox[2] <= outer_bbox[2] + tolerance and
                       inner_bbox[3] <= outer_bbox[3] + tolerance)

        if not is_contained:
            return False

        # 🔑 新增：面积比例检查，避免大小相似的组被合并
        inner_area = (inner_bbox[2] - inner_bbox[0]) * (inner_bbox[3] - inner_bbox[1])
        outer_area = (outer_bbox[2] - outer_bbox[0]) * (outer_bbox[3] - outer_bbox[1])

        # 如果内边界框面积超过外边界框面积的80%，认为它们大小相似，不应该合并
        if outer_area > 0 and inner_area / outer_area > 0.8:
            return False

        return True
    
    def merge_isolated_groups_to_single(self, groups, wall_layers, door_window_layers, railing_layers):
        """将所有非特殊图层的独立组（实体数为1）合并为一个组"""
        if not groups:
            return groups
        
        special_layers = wall_layers | door_window_layers | railing_layers
        isolated_entities = []
        remaining_groups = []
        
        # 分离独立实体和普通组
        for group in groups:
            if not group:
                continue
                
            # 检查是否为特殊图层组
            is_special_group = any(entity['layer'] in special_layers for entity in group)
            if is_special_group:
                remaining_groups.append(group)
                continue
            
            # 检查是否为独立组（只有一个实体）
            if len(group) == 1:
                isolated_entities.extend(group)
            else:
                remaining_groups.append(group)
        
        # 如果有独立实体，创建一个新的合并组
        if isolated_entities:
            remaining_groups.append(isolated_entities)
        
        return remaining_groups

    def merge_adjacent_groups(self, groups, distance_threshold=50, debug=False):
        """合并相邻的组（基于空间距离）"""
        if not groups or len(groups) <= 1:
            return groups

        if debug:
            print(f"🔧 开始相邻组合并，输入组数: {len(groups)}")

        merged_groups = []
        used_indices = set()
        merge_count = 0

        for i, group1 in enumerate(groups):
            if i in used_indices:
                continue

            # 创建合并组，初始包含当前组
            merged_group = list(group1)
            used_indices.add(i)

            # 查找可以合并的相邻组
            for j, group2 in enumerate(groups):
                if j <= i or j in used_indices:
                    continue

                # 计算两个组之间的最小距离
                min_distance = self._calculate_group_distance(group1, group2)

                if min_distance <= distance_threshold:
                    # 合并组
                    merged_group.extend(group2)
                    used_indices.add(j)
                    merge_count += 1

                    if debug:
                        print(f"  合并组{i}和组{j}，距离: {min_distance:.1f}")

            merged_groups.append(merged_group)

        if debug:
            print(f"  相邻组合并完成: {len(groups)} -> {len(merged_groups)} (合并了{merge_count}个组)")

        return merged_groups

    def _calculate_group_distance(self, group1, group2):
        """计算两个组之间的最小距离"""
        min_distance = float('inf')

        for entity1 in group1:
            for entity2 in group2:
                distance = self._calculate_geometric_distance(entity1, entity2)
                min_distance = min(min_distance, distance)

        return min_distance

    def merge_lines(self, entities):
        """合并线条（三阶段处理专用方法）"""
        if not entities:
            print("⚠️ 没有实体需要合并")
            return entities

        print(f"🔧 开始线条合并处理，输入实体数: {len(entities)}")

        try:
            # 标记实体为处理中状态
            processing_entities = []
            for entity in entities:
                if isinstance(entity, dict):
                    entity_copy = entity.copy()
                    entity_copy['processing'] = True
                    processing_entities.append(entity_copy)
                else:
                    processing_entities.append(entity)

            merged_entities = processing_entities.copy()

            # 如果有线段合并器，使用它进行合并
            if hasattr(self, 'line_merger') and self.line_merger:
                print("🔧 使用线段合并器进行处理...")
                original_count = len(merged_entities)
                merged_entities = self.line_merger.process_entities(merged_entities)
                final_count = len(merged_entities)
                print(f"✅ 线段合并完成: {original_count} -> {final_count} 个实体")

                # 打印合并统计信息
                if hasattr(self.line_merger, 'get_stats'):
                    stats = self.line_merger.get_stats()
                    if stats.get('merged_lines', 0) > 0:
                        print(f"📊 合并统计: 简化了 {stats['merged_lines']} 条线段")

            # 如果有重叠线条合并器，使用它进行处理
            if hasattr(self, 'overlapping_merger') and self.overlapping_merger:
                print("🔧 使用重叠线条合并器进行处理...")
                original_count = len(merged_entities)
                merged_entities = self.overlapping_merger.process_entities(merged_entities)
                final_count = len(merged_entities)
                print(f"✅ 重叠线条合并完成: {original_count} -> {final_count} 个实体")

            # 移除处理标记
            final_entities = []
            for entity in merged_entities:
                if isinstance(entity, dict) and 'processing' in entity:
                    entity_copy = entity.copy()
                    del entity_copy['processing']
                    final_entities.append(entity_copy)
                else:
                    final_entities.append(entity)

            print(f"✅ 线条合并处理完成，输出实体数: {len(final_entities)}")
            return final_entities

        except Exception as e:
            print(f"❌ 线条合并处理失败: {e}")
            # 返回原始实体（移除处理标记）
            clean_entities = []
            for entity in entities:
                if isinstance(entity, dict) and 'processing' in entity:
                    entity_copy = entity.copy()
                    del entity_copy['processing']
                    clean_entities.append(entity_copy)
                else:
                    clean_entities.append(entity)
            return clean_entities

    def group_entities(self, entities):
        """对实体进行分组（三阶段处理专用方法）"""
        if not entities:
            print("⚠️ 没有实体需要分组")
            return []

        print(f"🔧 开始实体分组处理，输入实体数: {len(entities)}")

        try:
            # 使用现有的分组方法
            groups = self.group_entities_by_connectivity_and_layer(entities)

            if not groups:
                print("⚠️ 分组结果为空，创建默认分组")
                # 如果分组失败，将所有实体放入一个组
                groups = [entities]

            print(f"✅ 实体分组完成，共生成 {len(groups)} 个组")

            # 打印分组统计
            for i, group in enumerate(groups):
                if group:
                    print(f"  组{i+1}: {len(group)} 个实体")

            return groups

        except Exception as e:
            print(f"❌ 实体分组处理失败: {e}")
            # 返回默认分组
            return [entities] if entities else []