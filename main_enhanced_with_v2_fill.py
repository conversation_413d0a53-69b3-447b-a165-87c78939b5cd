#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
CAD分类标注工具 - 增强版（集成V2墙体填充）
集成改进版墙体填充处理器V2，实现：
1. 使用create_fill_polygons_enhanced方法识别外轮廓
2. 使用完整线段打断算法
3. 处理重叠线条、间隙和缺失端头
4. 仅识别墙体组的外轮廓进行填充，不识别空腔
"""

import os
import sys
import threading
import json
import time
from datetime import datetime

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入tkinter组件
import tkinter as tk
from tkinter import messagebox, ttk, filedialog
from tkinter import Label, Frame, Button, Radiobutton

# 导入matplotlib组件
try:
    import matplotlib
    matplotlib.use('TkAgg')
    from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
except ImportError:
    FigureCanvasTkAgg = None


# 导入主程序
from main_enhanced import EnhancedCADApp
try:
    from main_enhanced import main
except ImportError:
    main = None

# 导入增强版墙体填充处理器V2
try:
    from wall_fill_processor_enhanced_v2 import EnhancedWallFillProcessorV2
except ImportError as e:
    EnhancedWallFillProcessorV2 = None

# 导入日志输出模块
try:
    from log_exporter import LogExporter
except ImportError as e:
    LogExporter = None
    print(f"⚠️ 日志输出模块导入失败: {e}")

# 导入房间识别模块
try:
    from room_recognition_processor import RoomRecognitionProcessor
    from room_recognition_ui import RoomRecognitionUI
except ImportError as e:
    RoomRecognitionProcessor = None
    RoomRecognitionUI = None
    print(f"⚠️ 房间识别模块导入失败: {e}")

# 导入显示控制模块
try:
    from display_controller import DisplayController, DataChangeType
    from display_integration import DisplayControllerMixin, DisplayIntegrationHelper
except ImportError as e:
    DisplayController = None
    DataChangeType = None
    DisplayControllerMixin = object  # 提供一个基类
    DisplayIntegrationHelper = None
    print(f"⚠️ 显示控制模块导入失败: {e}")

# 导入数据缓存系统
try:
    from overview_data_cache import overview_cache, DataType
except ImportError as e:
    overview_cache = None
    DataType = None
    print(f"⚠️ 数据缓存系统导入失败: {e}")

# 导入阴影生成系统
try:
    from shadow_generator import (
        RasterShadowGenerator, VectorShadowGenerator,
        DirectionalShadowGenerator, AdaptiveShadowGenerator,
        ContactShadowGenerator, batch_create_shadows
    )
    SHADOW_AVAILABLE = True
except ImportError as e:
    # 创建占位符类
    class DummyShadowGenerator:
        pass

    RasterShadowGenerator = DummyShadowGenerator
    VectorShadowGenerator = DummyShadowGenerator
    DirectionalShadowGenerator = DummyShadowGenerator
    AdaptiveShadowGenerator = DummyShadowGenerator
    ContactShadowGenerator = DummyShadowGenerator
    batch_create_shadows = lambda *args, **kwargs: []
    SHADOW_AVAILABLE = False
    print(f"⚠️ 阴影生成系统导入失败: {e}")

class EnhancedCADAppV2(EnhancedCADApp):
    """增强版CAD应用（集成V2墙体填充和显示控制器）"""

    def __init__(self, root):
        # 文件管理相关（在父类初始化之前设置）
        self.current_folder = ""
        self.current_file = ""  # 当前处理的文件
        self.all_files = []  # 文件夹中的所有CAD文件
        self.file_status = {}  # 文件处理状态
        self.file_data = {}  # 文件数据缓存
        self.background_processors = {}  # 后台处理器
        self.should_stop_background = False  # 后台处理停止标志

        # 🗄️ 初始化数据缓存系统
        self._init_overview_cache()

        # 🌟 初始化阴影系统
        self._init_shadow_system()

        # 🎨 预初始化显示控制器（在UI创建之前）
        self._pre_init_display_controller()

        # 🏠 预初始化房间识别模块（在UI创建之前）
        self._pre_init_room_recognition()

        
        # 🔍 简化处理器重置追踪
        self._processor_reset_count = 0
        print("🔍 处理器重置追踪已启动")
        # 调用父类初始化（这会创建UI，包括file_combo）
        super().__init__(root)

        # 使用V2墙体填充处理器
        if EnhancedWallFillProcessorV2:
            self.wall_fill_processor_v2 = EnhancedWallFillProcessorV2()
        else:
            self.wall_fill_processor_v2 = None

        # 初始化填充状态管理
        if not hasattr(self, 'group_fill_status'):
            self.group_fill_status = {}

        # 初始化组显示控制
        self.hidden_groups = set()  # 存储隐藏的组索引

        # 初始化文件状态
        self._init_file_management()

        # 初始化日志导出器
        self._init_log_exporter()

        # 🔧 应用全图概览颜色显示修复
        self._apply_color_display_fix()

        # 🎨 初始化配色系统（确保配色方案可用）
        self._init_color_system_v2()

        # 🏠 完成房间识别模块初始化
        self._complete_room_recognition_init()

        # 🎨 完成显示控制器初始化
        self._complete_display_controller_init()

        # 设置程序关闭时的回调
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # 🔧 最终修复：确保处理器在初始化时就存在
        self._ensure_processor_initialized()

    def _init_color_system_v2(self):
        """初始化V2配色系统"""
        try:
            # 确保父类的配色系统已初始化
            if not hasattr(self, 'current_color_scheme'):
                # 调用父类的配色系统初始化
                super().init_color_system()

            # 初始化配色方案应用标记
            self._color_scheme_applied = False

            # 5. 配色方案中增加高亮显示的颜色调整
            if 'highlight' not in self.current_color_scheme:
                self.current_color_scheme['highlight'] = self.current_color_scheme.get('current_group', '#FF0000')

            # 确保默认配色方案也包含高亮颜色
            if hasattr(self, 'default_color_scheme') and 'highlight' not in self.default_color_scheme:
                self.default_color_scheme['highlight'] = self.default_color_scheme.get('current_group', '#FF0000')

            # 初始化索引图的类别颜色映射
            self.legend_category_colors = {
                'wall': self.current_color_scheme.get('wall', '#8B4513'),
                'door': self.current_color_scheme.get('door_window', '#FFD700'),
                'window': self.current_color_scheme.get('door_window', '#87CEEB'),
                'column': self.current_color_scheme.get('column', '#696969'),
                'beam': self.current_color_scheme.get('other', '#2F4F4F'),
                'stair': self.current_color_scheme.get('stair', '#9370DB'),
                'elevator': self.current_color_scheme.get('elevator', '#FF6347'),
                'other': self.current_color_scheme.get('other', '#808080'),
                'door_window': self.current_color_scheme.get('door_window', '#FFD700'),
                'furniture': self.current_color_scheme.get('furniture', '#8B4513')
            }



        except Exception as e:
            print(f"❌ V2配色系统初始化失败: {e}")
            # 设置默认配色方案
            self.current_color_scheme = {
                'background': '#FFFFFF',
                'wall': '#000000',
                'door_window': '#FF0000',
                'furniture': '#0000FF',
                'other': '#808080',
                'other_lines': '#808080',  # 添加其他线条颜色
                'processing_lines': '#FF8C00',  # 添加处理过程中的线条颜色
                'current_group': '#FF0000',
                'labeled_group': '#00FF00',
                'text': '#000000',
                'highlight': '#FF0000'  # 5. 增加高亮颜色
            }

    def _init_overview_cache(self):
        """初始化全图概览数据缓存系统"""
        try:
            if overview_cache:
                self.overview_cache = overview_cache
                # 注册数据变化监听器
                if DataType:
                    self.overview_cache.register_change_listener(
                        DataType.DXF_ENTITIES, self._on_entities_changed
                    )
                    self.overview_cache.register_change_listener(
                        DataType.WALL_FILL, self._on_wall_fill_changed
                    )
                    self.overview_cache.register_change_listener(
                        DataType.ROOM_FILL, self._on_room_fill_changed
                    )
            else:
                self.overview_cache = None
        except Exception as e:
            print(f"❌ 数据缓存系统初始化失败: {e}")
            self.overview_cache = None

    def _init_shadow_system(self):
        """初始化阴影系统"""
        try:
            if SHADOW_AVAILABLE:
                # 初始化图层阴影状态
                self.layer_shadows = {
                    'cad_lines': {
                        'enabled': False,
                        'generator': None,
                        'direction': 315,  # 默认光源角度
                        'intensity': 0.3,  # 阴影强度
                        'length': 10,      # 阴影长度
                        'type': 'vector'   # 阴影类型：vector/raster/contact
                    },
                    'wall_fill': {
                        'enabled': False,
                        'generator': None,
                        'direction': 315,
                        'intensity': 0.3,
                        'length': 10,
                        'type': 'vector'
                    },
                    'furniture_fill': {
                        'enabled': False,
                        'generator': None,
                        'direction': 315,
                        'intensity': 0.3,
                        'length': 10,
                        'type': 'vector'
                    },
                    'room_fill': {
                        'enabled': False,
                        'generator': None,
                        'direction': 315,
                        'intensity': 0.3,
                        'length': 10,
                        'type': 'vector'
                    }
                }
                print("✅ 阴影系统初始化完成")
            else:
                self.layer_shadows = {}
                print("⚠️ 阴影系统不可用")
        except Exception as e:
            print(f"❌ 阴影系统初始化失败: {e}")
            self.layer_shadows = {}

    def _init_file_management(self):
        """初始化文件管理系统"""
        # 文件状态定义
        # processing_status: 'unprocessed', 'processing', 'completed'
        # annotation_status: 'unannotated', 'incomplete', 'completed'
        pass

    def _init_log_exporter(self):
        """初始化日志导出器"""
        if LogExporter:
            self.log_exporter = LogExporter(self)
        else:
            self.log_exporter = None

    def _on_entities_changed(self, file_path: str, data_type=None, data=None):
        """实体数据变化回调"""
        if self.current_file == file_path:
            print(f"🔄 实体数据已变化，更新显示: {file_path}")
            self._refresh_overview_display()

    def _on_wall_fill_changed(self, file_path: str, data_type=None, data=None):
        """墙体填充数据变化回调"""
        if self.current_file == file_path:
            print(f"🏗️ 墙体填充数据已变化，更新显示: {file_path}")
            self._refresh_overview_display()

    def _on_room_fill_changed(self, file_path: str, data_type=None, data=None):
        """房间填充数据变化回调"""
        if self.current_file == file_path:
            print(f"🏠 房间填充数据已变化，更新显示: {file_path}")
            self._refresh_overview_display()

    def _refresh_overview_display(self):
        """刷新全图概览显示"""
        try:
            if self.visualizer and hasattr(self, 'current_file_entities'):
                # 从缓存获取最新数据
                cached_entities = None
                cached_wall_fills = None
                cached_room_fills = None

                if self.overview_cache and DataType:
                    cached_entities = self.overview_cache.get_data(DataType.DXF_ENTITIES)
                    cached_wall_fills = self.overview_cache.get_data(DataType.WALL_FILL)
                    cached_room_fills = self.overview_cache.get_data(DataType.ROOM_FILL)

                # 使用缓存数据或当前数据
                entities = cached_entities or self.current_file_entities
                # 使用缓存的填充数据
                wall_fills = cached_wall_fills or getattr(self, 'wall_fills', None)
                room_fills = cached_room_fills or getattr(self, 'room_fills', None)

                # 更新全图概览
                self.visualizer.visualize_overview(
                    entities,
                    getattr(self, 'current_group', None),
                    self.auto_labeled_entities + self.labeled_entities,
                    processor=self,
                    wall_fills=wall_fills,
                    wall_fill_processor=getattr(self, 'wall_fill_processor_v2', None)
                )

                if self.canvas:
                    self.visualizer.update_canvas(self.canvas)

        except Exception as e:
            print(f"❌ 刷新全图概览显示失败: {e}")

    def _pre_init_room_recognition(self):
        """预初始化房间识别模块（在UI创建之前）"""
        try:
            # 初始化房间识别处理器
            if RoomRecognitionProcessor:
                self.room_processor = RoomRecognitionProcessor()
            else:
                self.room_processor = None

            # 房间识别UI将在UI创建后初始化
            self.room_ui = None

        except Exception as e:
            print(f"❌ 预初始化房间识别模块失败: {e}")
            self.room_processor = None
            self.room_ui = None

    def _pre_init_display_controller(self):
        """预初始化显示控制器（在UI创建之前）"""
        try:
            if DisplayController:
                # 创建显示控制器实例
                self.display_controller = DisplayController()
            else:
                self.display_controller = None

        except Exception as e:
            print(f"❌ 预初始化显示控制器失败: {e}")
            self.display_controller = None

    def _complete_display_controller_init(self):
        """完成显示控制器初始化（在UI创建之后）"""
        try:
            if self.display_controller:
                # 设置可视化器和画布
                if hasattr(self, 'visualizer') and self.visualizer:
                    self.display_controller.set_visualizer(self.visualizer)

                if hasattr(self, 'canvas') and self.canvas:
                    self.display_controller.set_canvas(self.canvas)

                # 设置配色系统
                if hasattr(self, 'current_color_scheme'):
                    from display_integration import SimpleColorSystemWrapper
                    color_system = SimpleColorSystemWrapper(self)
                    self.display_controller.set_color_system(color_system)

                # 设置数据源
                if hasattr(self, 'processor') and self.processor:
                    self.display_controller.set_data_source(self.processor)

                # 集成到处理器
                if DisplayIntegrationHelper and hasattr(self, 'processor') and self.processor:
                    DisplayIntegrationHelper.integrate_with_processor(
                        self.processor, self.display_controller
                    )

                pass
            else:
                pass

        except Exception as e:
            print(f"❌ 完成显示控制器初始化失败: {e}")

    def _complete_room_recognition_init(self):
        """完成房间识别模块初始化（在UI创建之后）"""
        try:
            # 这里可以添加需要在UI创建后执行的初始化逻辑
            if self.room_processor:
                pass

        except Exception as e:
            print(f"❌ 完成房间识别模块初始化失败: {e}")

    def _notify_display_controller_change(self, status_type, data):
        """通知显示控制器数据变化"""
        try:
            if not hasattr(self, 'display_controller') or not self.display_controller:
                return

            # 根据状态类型映射到显示控制器的数据变化类型
            change_mapping = {
                'manual_group': DataChangeType.GROUP_DATA,
                'group_labeled': DataChangeType.GROUP_TYPE,
                'auto_labeled': DataChangeType.GROUP_TYPE,
                'completed': DataChangeType.DXF_FILE_DATA,
                'wall_fill_completed': DataChangeType.WALL_FILL,
                'room_recognition_completed': DataChangeType.ROOM_FILL,
                'group_fill_update': DataChangeType.GROUP_FILL,
                'color_scheme_changed': DataChangeType.COLOR_SCHEME,
                'visibility_changed': DataChangeType.VISIBILITY
            }

            change_type = change_mapping.get(status_type)
            if change_type:
                # 构建元数据
                metadata = {
                    'status_type': status_type,
                    'current_file': getattr(self, 'current_file', ''),
                    'processor': getattr(self, 'processor', None)
                }

                # 添加特定的元数据
                if status_type == 'wall_fill_completed' and hasattr(self, 'wall_fill_processor_v2'):
                    metadata['wall_fill_processor'] = self.wall_fill_processor_v2
                elif status_type == 'room_recognition_completed' and hasattr(self, 'room_processor'):
                    metadata['room_processor'] = self.room_processor

                # 通知显示控制器
                self.display_controller.notify_data_change(
                    change_type,
                    data,
                    f"EnhancedCADAppV2.{status_type}",
                    metadata
                )

        except Exception as e:
            print(f"❌ 通知显示控制器失败: {e}")

    def _init_room_recognition(self):
        """初始化房间识别模块"""
        try:
            # 初始化房间识别处理器
            if RoomRecognitionProcessor:
                self.room_processor = RoomRecognitionProcessor()
            else:
                self.room_processor = None

            # 房间识别UI将在创建右侧面板时初始化
            self.room_ui = None

        except Exception as e:
            print(f"❌ 初始化房间识别模块失败: {e}")
            self.room_processor = None
            self.room_ui = None

    def _apply_color_display_fix(self):
        """应用全图概览颜色显示修复"""
        try:
            pass

        except Exception as e:
            print(f"⚠️ 应用颜色显示修复失败: {e}")



    def export_log_manual(self):
        """手动导出日志（按钮触发）"""
        if not self.log_exporter:
            messagebox.showwarning("警告", "日志导出功能不可用")
            return

        try:
            log_file = self.log_exporter.export_log("手动触发")
            if log_file:
                messagebox.showinfo("成功", f"日志已导出到:\n{log_file}")
            else:
                messagebox.showerror("错误", "日志导出失败")
        except Exception as e:
            messagebox.showerror("错误", f"日志导出失败: {e}")

    def export_log_auto(self, trigger_reason):
        """自动导出日志"""
        if not self.log_exporter:
            print("⚠️ 日志导出器不可用，跳过自动导出")
            return

        try:
            log_file = self.log_exporter.export_log(trigger_reason)
            if log_file:
                print(f"✅ 自动日志导出成功: {log_file}")
            else:
                print("❌ 自动日志导出失败")
        except Exception as e:
            print(f"❌ 自动日志导出异常: {e}")

    def _check_all_files_completed(self):
        """检查是否所有文件都已处理完成，如果是则自动导出日志"""
        if not self.file_status:
            return

        # 统计处理状态
        total_files = len(self.file_status)
        completed_files = 0
        failed_files = 0

        for file_name, status in self.file_status.items():
            proc_status = status.get('processing_status', 'unprocessed')
            if proc_status == 'completed':
                completed_files += 1
            elif proc_status == 'failed':
                failed_files += 1

        # 检查是否所有文件都已处理（完成或失败）
        processed_files = completed_files + failed_files
        if processed_files == total_files:
            print(f"🎉 所有文件处理完成: 总计{total_files}个文件，成功{completed_files}个，失败{failed_files}个")

            # 自动导出日志
            self.export_log_auto("所有选择的文件处理完成后")
        else:
            remaining = total_files - processed_files
            print(f"📊 处理进度: {processed_files}/{total_files}，剩余{remaining}个文件")

    def on_closing(self):
        """程序关闭时的处理"""
        try:
            # 导出关闭前日志
            self.export_log_auto("关闭程序前")

            # 停止后台处理
            self.should_stop_background = True

            # 关闭程序
            self.root.destroy()
        except Exception as e:
            print(f"程序关闭处理失败: {e}")
            self.root.destroy()

    def _create_status_display(self, parent):
        """创建状态显示区域（重写以添加文件选择功能）"""
        try:
            frame = tk.Frame(parent)
            frame.pack(fill='x', pady=(0, 15))

            tk.Label(frame, text="处理状态:", font=('Arial', 10, 'bold')).pack(anchor='w')

            # 文件选择区域
            file_frame = tk.Frame(frame)
            file_frame.pack(fill='x', pady=(5, 10))

            tk.Label(file_frame, text="文件选择:", font=('Arial', 9)).pack(anchor='w')

            # 文件选择下拉菜单和按钮的容器
            file_control_frame = tk.Frame(file_frame)
            file_control_frame.pack(fill='x', pady=(2, 0))

            # 保存按钮
            save_btn = tk.Button(file_control_frame, text="保存", command=self.save_all_data,
                                bg='#4CAF50', fg='white', font=('Arial', 8), width=6)
            save_btn.pack(side='right', padx=(5, 0))

            # 读取按钮
            load_btn = tk.Button(file_control_frame, text="读取", command=self.load_all_data,
                                bg='#2196F3', fg='white', font=('Arial', 8), width=6)
            load_btn.pack(side='right', padx=(5, 0))

            # 文件选择下拉菜单
            self.file_combo = ttk.Combobox(file_control_frame, state='readonly', font=('Arial', 9))
            self.file_combo.pack(side='left', fill='x', expand=True)
            self.file_combo.bind('<<ComboboxSelected>>', self.on_file_selected)
            self.file_combo.bind('<Button-1>', self.on_file_combo_clicked)

            # 设置初始值
            self.file_combo['values'] = ["请先选择文件夹或文件"]
            self.file_combo.set("请先选择文件夹或文件")

            # 进度条
            self.progress_bar = ttk.Progressbar(frame, mode='determinate')
            self.progress_bar.pack(fill='x', pady=(5, 0))

            # 状态标签 - 实时更新
            status_label = tk.Label(frame, textvariable=self.status_var, font=('Arial', 9),
                                   fg='blue', wraplength=350, justify='left')
            status_label.pack(anchor='w', fill='x')

            # 统计信息
            stats_label = tk.Label(frame, textvariable=self.stats_var, font=('Arial', 9),
                                  fg='green', wraplength=350, justify='left')
            stats_label.pack(anchor='w', fill='x')

        except Exception as e:
            print(f"❌ 创建file_combo失败: {e}")
            import traceback
            traceback.print_exc()

    def _create_folder_selection(self, parent):
        """重写文件夹选择区域，添加单独文件选择功能"""
        frame = tk.Frame(parent)
        frame.pack(fill='x', pady=(0, 10))

        tk.Label(frame, text="数据文件夹:", font=('Arial', 10, 'bold')).pack(anchor='w')

        # 文件夹路径显示
        folder_entry = tk.Entry(frame, textvariable=self.folder_var, state='readonly')
        folder_entry.pack(fill='x', pady=(5, 0))

        # 按钮容器
        button_frame = tk.Frame(frame)
        button_frame.pack(fill='x', pady=(5, 0))

        # 选择文件夹按钮
        folder_btn = tk.Button(button_frame, text="选择文件夹", command=self.select_folder,
                              bg='#4CAF50', fg='white', font=('Arial', 9))
        folder_btn.pack(side='left', fill='x', expand=True, padx=(0, 5))

        # 选择文件按钮（新增）
        file_btn = tk.Button(button_frame, text="选择文件", command=self.select_files,
                            bg='#2196F3', fg='white', font=('Arial', 9))
        file_btn.pack(side='left', fill='x', expand=True, padx=(5, 0))

        return frame

    def select_files(self):
        """选择单独的DXF文件（新增功能）"""
        files = filedialog.askopenfilenames(
            title="选择DXF文件",
            filetypes=[
                ("DXF文件", "*.dxf"),
                ("DWG文件", "*.dwg"),
                ("所有CAD文件", "*.dxf;*.dwg"),
                ("所有文件", "*.*")
            ]
        )

        if files:
            # 处理选择的文件
            self._process_selected_files(files)

    def _process_selected_files(self, files):
        """处理选择的文件"""
        try:
            # 清空之前的文件列表
            self.all_files = []
            self.file_status = {}
            self.file_data = {}

            # 设置文件夹为第一个文件的目录
            if files:
                first_file_dir = os.path.dirname(files[0])
                self.current_folder = first_file_dir
                self.folder_var.set(first_file_dir)

            # 添加选择的文件到列表
            for file_path in files:
                if os.path.exists(file_path):
                    file_name = os.path.basename(file_path)
                    if file_name.lower().endswith(('.dxf', '.dwg')):
                        self.all_files.append(file_name)
                        self.file_status[file_name] = {
                            'processing_status': 'unprocessed',
                            'annotation_status': 'unannotated'
                        }

            # 更新状态显示
            if self.all_files:
                file_count = len(self.all_files)
                self.status_var.set(f"已选择 {file_count} 个文件")

                # 更新文件下拉菜单
                self.update_file_combo()

                # 如果只有一个文件，自动开始处理
                if file_count == 1:
                    self.current_file = os.path.join(self.current_folder, self.all_files[0])
                    self.start_processing()
                else:
                    messagebox.showinfo("成功", f"已选择 {file_count} 个文件，请点击开始处理")
            else:
                messagebox.showwarning("警告", "没有找到有效的CAD文件")

        except Exception as e:
            print(f"处理选择文件失败: {e}")
            messagebox.showerror("错误", f"处理选择文件失败: {e}")

    def on_file_combo_clicked(self, event):
        """文件下拉菜单被点击时更新文件状态"""
        self.update_file_combo()

    def on_file_selected(self, event):
        """文件被选择时的处理"""
        selected_file = self.file_combo.get()
        if selected_file and selected_file not in ["请先选择文件夹或文件", "单文件模式"]:
            # 解析文件名（去掉状态信息）
            file_name = selected_file.split(' (')[0]
            self.switch_to_file(file_name)

    def update_file_combo(self):
        """更新文件选择下拉菜单（重写以实现智能控制）"""
        # 检查file_combo是否已初始化
        if not self.file_combo:
            return

        if not self.all_files:
            self.file_combo['values'] = ["请先选择文件夹或文件"]
            self.file_combo.set("请先选择文件夹或文件")
            self.file_combo.config(state='disabled')  # 没有文件时禁用
            return

        # 智能下拉框控制：根据文件数量决定是否可下拉
        file_count = len(self.all_files)

        if file_count == 1:
            # 只有一个文件：显示为灰色，不可下拉
            file_name = self.all_files[0]
            status_info = self.file_status.get(file_name, {
                'processing_status': 'unprocessed',
                'annotation_status': 'unannotated'
            })

            proc_status = status_info['processing_status']
            anno_status = status_info['annotation_status']

            proc_text = {
                'unprocessed': '未处理',
                'processing': '处理中',
                'completed': '已完成',
                'failed': '处理失败'
            }.get(proc_status, '未知状态')

            anno_text = {
                'unannotated': '未标注',
                'incomplete': '标注未完成',
                'completed': '标注完成'
            }.get(anno_status, '未知状态')

            display_text = f"{file_name} ({proc_text}, {anno_text})"

            self.file_combo['values'] = [display_text]
            self.file_combo.set(display_text)
            self.file_combo.config(state='disabled')  # 单文件模式：禁用下拉

            print(f"📁 单文件模式: {file_name} - 下拉框已禁用")

        else:
            # 多个文件：可以下拉选择
            file_display_list = []

            # 使用显示文件而不是当前处理文件（关键修复）
            display_file_name = ""
            if hasattr(self, 'display_file') and self.display_file:
                display_file_name = os.path.basename(self.display_file)
                print(f"📁 使用display_file: {display_file_name}")
            elif self.current_file:
                display_file_name = os.path.basename(self.current_file)
                print(f"📁 使用current_file: {display_file_name}")
            else:
                # 如果都没有，使用第一个文件作为显示文件
                if self.all_files:
                    display_file_name = os.path.basename(self.all_files[0])
                    print(f"📁 使用第一个文件: {display_file_name}")

            for file_path in self.all_files:
                file_name = os.path.basename(file_path) if os.path.sep in file_path else file_path
                status_info = self.file_status.get(file_name, {
                    'processing_status': 'unprocessed',
                    'annotation_status': 'unannotated'
                })

                # 构建状态显示
                proc_status = status_info['processing_status']
                anno_status = status_info['annotation_status']

                proc_text = {
                    'unprocessed': '未处理',
                    'processing': '处理中',
                    'completed': '已完成',
                    'failed': '处理失败'
                }.get(proc_status, '未知状态')

                anno_text = {
                    'unannotated': '未标注',
                    'incomplete': '标注未完成',
                    'completed': '标注完成'
                }.get(anno_status, '未知状态')

                # 标记当前显示的文件（用方括号）
                if file_name == display_file_name:
                    display_text = f"[{file_name}] ({proc_text}, {anno_text})"
                else:
                    display_text = f"{file_name} ({proc_text}, {anno_text})"

                file_display_list.append(display_text)

            self.file_combo['values'] = file_display_list
            self.file_combo.config(state='readonly')  # 多文件模式：启用下拉

            # 设置当前选择（基于显示文件，确保选择正确）
            current_selection_set = False
            if display_file_name:
                for display_text in file_display_list:
                    if display_file_name in display_text and display_text.startswith('['):
                        self.file_combo.set(display_text)
                        current_selection_set = True
                        print(f"📁 设置当前选择: {display_text}")
                        break

            # 如果没有设置成功，使用第一个文件
            if not current_selection_set and file_display_list:
                self.file_combo.set(file_display_list[0])
                print(f"📁 使用第一个文件作为选择: {file_display_list[0]}")

            print(f"📁 多文件模式: {file_count} 个文件 - 下拉框已启用，显示文件: {display_file_name}")

    def switch_to_file(self, file_name):
        """切换到指定文件（增强版：支持状态检查和回退）"""
        if not file_name or file_name == "请先选择文件夹":
            return

        # 保存当前选择，用于回退
        current_selection = self.file_combo.get()
        current_file_name = os.path.basename(self.current_file) if self.current_file else ""

        print(f"🔄 开始切换文件: {file_name}")

        # 1. 保存当前文件的数据（如果有的话）
        if hasattr(self, 'processor') and self.processor and self.current_file:
            self._save_current_file_data()
            print(f"  💾 已保存当前文件数据: {os.path.basename(self.current_file)}")

        # 检查文件是否已处理完成
        status_info = self.file_status.get(file_name, {})
        processing_status = status_info.get('processing_status', 'unprocessed')

        if processing_status != 'completed':
            # 显示详细的状态信息
            status_text = {
                'unprocessed': '未处理',
                'processing': '处理中',
                'completed': '已完成'
            }.get(processing_status, '未知状态')

            # 弹出警告对话框
            messagebox.showwarning(
                "无法切换文件",
                f"文件 {file_name} 当前状态为：{status_text}\n\n"
                f"只有处理完成的文件才能切换查看。\n"
                f"请等待文件处理完成后再尝试切换。"
            )

            # 回退到当前文件的显示
            self._revert_file_selection(current_selection, current_file_name)
            return

        # 保存当前文件的数据
        if self.current_file:
            try:
                self._save_current_file_data()
                print(f"✅ 已保存当前文件数据: {os.path.basename(self.current_file)}")
            except Exception as e:
                print(f"⚠️ 保存当前文件数据失败: {e}")

        # 查找目标文件路径
        file_path = None
        for path in self.all_files:
            if os.path.basename(path) == file_name:
                file_path = path
                break

        if file_path:
            try:
                # 加载新文件的数据
                self._load_file_data(file_path)

                # 更新显示文件
                self.display_file = file_path

                # 执行完整的界面更新
                self._perform_complete_ui_update_for_file_switch(file_name)

                # 更新状态显示
                status_info = self.file_status.get(file_name, {})
                proc_status = status_info.get('processing_status', 'unknown')
                anno_status = status_info.get('annotation_status', 'unknown')

                self.status_var.set(f"已切换到文件: {file_name} (处理: {proc_status}, 标注: {anno_status})")

                print(f"✅ 成功切换到文件: {file_name}")

            except Exception as e:
                print(f"❌ 切换文件失败: {e}")
                # 切换失败时回退
                self._revert_file_selection(current_selection, current_file_name)
                messagebox.showerror("切换失败", f"切换到文件 {file_name} 时发生错误：\n{str(e)}")
        else:
            print(f"❌ 未找到文件: {file_name}")
            # 文件不存在时回退
            self._revert_file_selection(current_selection, current_file_name)
            messagebox.showerror("文件不存在", f"未找到文件: {file_name}")

    def _revert_file_selection(self, previous_selection, current_file_name):
        """回退文件选择到之前的状态"""
        try:
            # 恢复下拉菜单的选择
            if previous_selection:
                self.file_combo.set(previous_selection)
            elif current_file_name:
                # 如果没有之前的选择，恢复到当前文件
                for value in self.file_combo['values']:
                    if current_file_name in value:
                        self.file_combo.set(value)
                        break

            print(f"🔄 已回退文件选择: {previous_selection or current_file_name}")

        except Exception as e:
            print(f"⚠️ 回退文件选择失败: {e}")

    def _save_current_file_data(self):
        """保存当前文件的处理数据"""
        try:
            if not hasattr(self, 'processor') or not self.processor or not self.current_file:
                return

            file_name = os.path.basename(self.current_file)

            # 收集当前处理器的数据
            data = {
                'entities': self._serialize_data(getattr(self.processor, 'current_file_entities', []) or []),
                'all_groups': self._serialize_data(getattr(self.processor, 'all_groups', []) or []),
                'groups': self._serialize_data(getattr(self.processor, 'all_groups', []) or []),  # 兼容性
                'labeled_entities': self._serialize_data(getattr(self.processor, 'labeled_entities', []) or []),
                'auto_labeled_entities': self._serialize_data(getattr(self.processor, 'auto_labeled_entities', []) or []),
                'dataset': self._serialize_data(getattr(self.processor, 'dataset', []) or []),
                'groups_info': self._serialize_data(getattr(self.processor, 'groups_info', []) or []),
                'pending_manual_groups': self._serialize_data(getattr(self.processor, 'pending_manual_groups', []) or []),
                'current_manual_group_index': getattr(self.processor, 'current_manual_group_index', 0),
                'manual_grouping_mode': getattr(self.processor, 'manual_grouping_mode', False),
                'current_file': self.current_file,
                'timestamp': time.time()
            }

            # 保存到缓存
            self.file_data[file_name] = data

            # 更新文件状态
            if file_name not in self.file_status:
                self.file_status[file_name] = {}
            self.file_status[file_name]['last_saved'] = time.time()
            self.file_status[file_name]['data_saved'] = True

            print(f"  💾 文件数据已保存: {file_name} ({len(data.get('all_groups', []))} 个组)")

        except Exception as e:
            print(f"  ❌ 保存文件数据失败: {e}")
            import traceback
            traceback.print_exc()

    def _process_file_in_background(self, file_path):
        """在后台处理文件（不更新界面）"""
        file_name = os.path.basename(file_path)
        print(f"🔄 后台处理文件: {file_name}")

        try:
            # 创建独立的处理器用于后台处理
            from main_enhanced import EnhancedCADProcessor
            background_processor = EnhancedCADProcessor(None, None)  # 不传递可视化组件

            # 设置文件路径
            background_processor.current_file = file_path

            # 执行处理逻辑（简化版，不更新界面）
            # 这里可以调用处理器的核心处理方法
            # 具体实现取决于父类的处理逻辑

            # 更新文件状态为已完成
            self.file_status[file_name]['processing_status'] = 'completed'

            # 更新文件下拉菜单（不改变当前选择）
            current_selection = self.file_combo.get()
            self.update_file_combo()
            self.file_combo.set(current_selection)  # 保持当前选择

            print(f"✅ 后台处理完成: {file_name}")

        except Exception as e:
            print(f"❌ 后台处理失败: {file_name}, 错误: {e}")
            self.file_status[file_name]['processing_status'] = 'unprocessed'

    def _serialize_entity(self, entity):
        """序列化CAD实体对象为可JSON化的字典"""
        if hasattr(entity, '__dict__'):
            # 对于有__dict__的对象，提取基本属性
            serialized = {}
            for key, value in entity.__dict__.items():
                if isinstance(value, (str, int, float, bool, list, dict, type(None))):
                    serialized[key] = value
                elif hasattr(value, '__dict__'):
                    # 递归序列化嵌套对象
                    serialized[key] = self._serialize_entity(value)
                else:
                    # 对于其他类型，转换为字符串
                    serialized[key] = str(value)
            return serialized
        else:
            # 对于没有__dict__的对象，转换为字符串
            return str(entity)

    def _serialize_data(self, data, visited=None):
        """序列化数据结构（修复递归问题和数值类型处理）"""
        if visited is None:
            visited = set()

        # 防止循环引用导致的无限递归
        data_id = id(data)
        if data_id in visited:
            return f"<循环引用: {type(data).__name__}>"

        # 处理numpy数组和数值类型
        try:
            import numpy as np
            if isinstance(data, np.ndarray):
                # numpy数组转换为列表，保持数值类型
                return data.tolist()
            elif isinstance(data, (np.integer, np.floating)):
                # numpy数值类型转换为Python原生类型
                return data.item()
            elif isinstance(data, np.str_):
                # numpy字符串转换为Python字符串
                return str(data)
        except ImportError:
            pass

        if isinstance(data, list):
            visited.add(data_id)
            try:
                result = [self._serialize_data(item, visited) for item in data]
                visited.remove(data_id)
                return result
            except:
                visited.discard(data_id)
                return f"<序列化失败: list>"
        elif isinstance(data, dict):
            visited.add(data_id)
            try:
                result = {key: self._serialize_data(value, visited) for key, value in data.items()}
                visited.remove(data_id)
                return result
            except:
                visited.discard(data_id)
                return f"<序列化失败: dict>"
        elif isinstance(data, tuple):
            # 处理元组类型，保持为列表（JSON兼容）
            visited.add(data_id)
            try:
                result = [self._serialize_data(item, visited) for item in data]
                visited.remove(data_id)
                return result
            except:
                visited.discard(data_id)
                return f"<序列化失败: tuple>"
        elif hasattr(data, '__dict__'):
            visited.add(data_id)
            try:
                result = self._serialize_entity(data)
                visited.remove(data_id)
                return result
            except:
                visited.discard(data_id)
                return f"<序列化失败: entity>"
        elif isinstance(data, (str, int, float, bool, type(None))):
            return data
        else:
            try:
                # 尝试转换为基本类型
                if hasattr(data, 'item'):  # numpy标量
                    return data.item()
                elif hasattr(data, 'tolist'):  # numpy数组
                    return data.tolist()
                else:
                    return str(data)
            except:
                return f"<不可序列化: {type(data).__name__}>"

    def _deserialize_data(self, data):
        """反序列化数据结构（确保数值类型正确）"""
        if isinstance(data, list):
            return [self._deserialize_data(item) for item in data]
        elif isinstance(data, dict):
            result = {}
            for key, value in data.items():
                result[key] = self._deserialize_data(value)
            return result
        elif isinstance(data, str):
            # 尝试识别并转换数值字符串
            if data.startswith('<') and data.endswith('>'):
                # 跳过错误标记
                return data
            try:
                # 尝试转换为数值
                if '.' in data:
                    return float(data)
                else:
                    return int(data)
            except ValueError:
                return data
        else:
            return data

    def _fix_entity_data_types(self, entity):
        """修复实体数据中的数值类型问题"""
        if not isinstance(entity, dict):
            return entity

        # 需要确保为数值类型的字段
        numeric_fields = ['center', 'position', 'points', 'def_points', 'major_axis', 'ratio', 'radius']

        for field in numeric_fields:
            if field in entity and entity[field] is not None:
                try:
                    if field in ['center', 'position']:
                        # 坐标点：确保为数值元组
                        if isinstance(entity[field], (list, tuple)) and len(entity[field]) >= 2:
                            entity[field] = [float(x) for x in entity[field][:2]]
                    elif field in ['points', 'def_points']:
                        # 点列表：确保每个点都是数值
                        if isinstance(entity[field], list):
                            fixed_points = []
                            for point in entity[field]:
                                if isinstance(point, (list, tuple)) and len(point) >= 2:
                                    fixed_points.append([float(x) for x in point[:2]])
                                elif point is not None:
                                    fixed_points.append(point)
                            entity[field] = fixed_points
                    elif field == 'major_axis':
                        # 主轴：确保为数值列表
                        if isinstance(entity[field], (list, tuple)) and len(entity[field]) >= 2:
                            entity[field] = [float(x) for x in entity[field][:2]]
                    elif field in ['ratio', 'radius']:
                        # 单个数值：确保为浮点数
                        entity[field] = float(entity[field])
                except (ValueError, TypeError) as e:
                    print(f"⚠️ 修复实体字段 {field} 失败: {e}")
                    # 保持原值，避免程序崩溃
                    pass

        return entity

    def _validate_and_fix_cached_data(self, file_name, data):
        """验证和修复缓存数据（多文件处理专用）"""
        try:
            print(f"🔧 验证和修复缓存数据: {file_name}")

            # 统计修复信息
            fix_count = 0

            # 修复entities数据
            if 'entities' in data and data['entities']:
                fixed_entities = []
                for entity in data['entities']:
                    if isinstance(entity, dict):
                        fixed_entity = self._fix_entity_data_types(entity)
                        fixed_entities.append(fixed_entity)
                        fix_count += 1
                    else:
                        fixed_entities.append(entity)
                data['entities'] = fixed_entities
                print(f"  修复了 {len(fixed_entities)} 个实体")

            # 修复groups数据
            if 'groups' in data and data['groups']:
                fixed_groups = []
                for group in data['groups']:
                    if isinstance(group, list):
                        fixed_group = []
                        for entity in group:
                            if isinstance(entity, dict):
                                fixed_entity = self._fix_entity_data_types(entity)
                                fixed_group.append(fixed_entity)
                                fix_count += 1
                            else:
                                fixed_group.append(entity)
                        fixed_groups.append(fixed_group)
                    else:
                        fixed_groups.append(group)
                data['groups'] = fixed_groups
                print(f"  修复了 {len(fixed_groups)} 个组")

            # 修复labeled_entities数据
            if 'labeled_entities' in data and data['labeled_entities']:
                fixed_labeled = []
                for entity in data['labeled_entities']:
                    if isinstance(entity, dict):
                        fixed_entity = self._fix_entity_data_types(entity)
                        fixed_labeled.append(fixed_entity)
                        fix_count += 1
                    else:
                        fixed_labeled.append(entity)
                data['labeled_entities'] = fixed_labeled
                print(f"  修复了 {len(fixed_labeled)} 个已标注实体")

            print(f"✅ 数据验证和修复完成: {file_name}，共修复 {fix_count} 个实体")
            return data

        except Exception as e:
            print(f"❌ 数据验证和修复失败: {file_name}, 错误: {e}")
            import traceback
            traceback.print_exc()
            return data

    def _save_current_file_data(self):
        """保存当前文件的数据"""
        if not self.current_file:
            return

        file_name = os.path.basename(self.current_file)

        # 保存当前文件的处理数据（序列化处理）
        try:
            # 🔑 关键修复：同时保存两种键名以确保兼容性
            groups_data = self._serialize_data(self.processor.all_groups if self.processor else [])
            self.file_data[file_name] = {
                'entities': self._serialize_data(self.processor.current_file_entities if self.processor else []),
                'groups': groups_data,      # 🔑 兼容旧版本
                'all_groups': groups_data,  # 🔑 新版本键名
                'labeled_entities': self._serialize_data(self.processor.labeled_entities if self.processor else []),
                'dataset': self._serialize_data(self.processor.dataset if self.processor else []),
                'groups_info': self._serialize_data(self.processor.groups_info if self.processor else []),
                'group_fill_status': self._serialize_data(getattr(self, 'group_fill_status', {})),
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            print(f"序列化数据时出错: {e}")
            # 保存基本信息
            self.file_data[file_name] = {
                'entities': [],
                'groups': [],
                'labeled_entities': [],
                'dataset': [],
                'groups_info': [],
                'group_fill_status': {},
                'timestamp': datetime.now().isoformat(),
                'error': str(e)
            }

        # 更新文件状态
        if file_name not in self.file_status:
            self.file_status[file_name] = {
                'processing_status': 'unprocessed',
                'annotation_status': 'unannotated'
            }

        # 检查标注状态
        if self.processor and self.processor.labeled_entities:
            total_entities = len(self.processor.current_file_entities) if self.processor.current_file_entities else 0
            labeled_entities = len(self.processor.labeled_entities)

            if labeled_entities == total_entities and total_entities > 0:
                self.file_status[file_name]['annotation_status'] = 'completed'
            elif labeled_entities > 0:
                self.file_status[file_name]['annotation_status'] = 'incomplete'

    def _load_file_data(self, file_path):
        """加载文件数据（增强版：支持完整的文件切换）"""
        file_name = os.path.basename(file_path)
        print(f"📁 开始加载文件数据: {file_name}")

        # 🔧 修复：保存当前处理器状态
        previous_processor = self.processor
        
        # 更新当前文件
        self.current_file = file_path

        # 🗄️ 设置缓存系统的当前文件
        if self.overview_cache:
            self.overview_cache.set_current_file(file_path)

        # 如果有缓存数据，加载缓存
        if file_name in self.file_data:
            print(f"  📦 从缓存加载数据: {file_name}")
            self._load_from_cache(file_name, file_path)
        else:
            print(f"  ⚠️ 缓存中没有找到数据: {file_name}")
            # 尝试从文件状态中获取数据
            if file_name in self.file_status and self.file_status[file_name].get('processing_status') == 'completed':
                print(f"  🔄 文件已处理但缓存丢失，尝试重新处理")
                # 这里可以选择重新处理文件或显示错误
                self._handle_missing_cache_data(file_name, file_path)
            else:
                print(f"  ❌ 文件未处理或处理未完成")

    def _load_from_cache(self, file_name, file_path):
        """从缓存加载文件数据"""
        try:
            data = self.file_data[file_name]

            # 验证和修复缓存数据
            data = self._validate_and_fix_cached_data(file_name, data)

            # 确保处理器存在（关键修复）
            if not self.processor:
                # 🔍 简化追踪处理器重置
                if hasattr(self, '_processor_reset_count'):
                    self._processor_reset_count += 1
                    import time
                    timestamp = time.strftime("%H:%M:%S", time.localtime())
                    print(f"🔍 [{timestamp}] 处理器重置 #{self._processor_reset_count} - _load_from_cache")
                    print(f"   📁 当前文件: {getattr(self, 'current_file', '未知')}")
                    print(f"   💾 缓存大小: {len(getattr(self, 'file_cache', {}))}")
                    
                    # 简单的调用栈信息
                    import traceback
                    stack = traceback.extract_stack()
                    if len(stack) >= 2:
                        caller = stack[-2]
                        print(f"   📞 调用者: {os.path.basename(caller.filename)}:{caller.lineno} in {caller.name}")
                else:
                    print("🔍 [追踪未初始化] 处理器重置 - _load_from_cache")
                
                print("  ⚠️ 处理器不存在，创建新的处理器")


            # 恢复处理器状态（添加数据类型修复）
            self.processor.current_file = file_path

            # 反序列化并修复数据类型
            entities = self._deserialize_data(data.get('entities', []))

            # 🔑 关键修复：兼容两种键名（'groups' 和 'all_groups'）
            all_groups = self._deserialize_data(data.get('all_groups', data.get('groups', [])))


            labeled_entities = self._deserialize_data(data.get('labeled_entities', []))
            dataset = self._deserialize_data(data.get('dataset', []))
            groups_info = self._deserialize_data(data.get('groups_info', []))

            # 修复实体数据中的数值类型
            if entities:
                entities = [self._fix_entity_data_types(entity) for entity in entities]
                print(f"  修复了 {len(entities)} 个实体的数据类型")

            # 修复组数据中的数值类型
            if all_groups:
                fixed_groups = []
                for group in all_groups:
                    if isinstance(group, list):
                        fixed_group = [self._fix_entity_data_types(entity) for entity in group]
                        fixed_groups.append(fixed_group)
                    else:
                        fixed_groups.append(group)
                all_groups = fixed_groups
                print(f"  修复了 {len(all_groups)} 个组的数据类型")

            # 修复已标注实体的数据类型
            if labeled_entities:
                labeled_entities = [self._fix_entity_data_types(entity) for entity in labeled_entities]
                print(f"  修复了 {len(labeled_entities)} 个已标注实体的数据类型")

            # 赋值给处理器
            self.processor.current_file_entities = entities
            self.processor.all_groups = all_groups
            self.processor.labeled_entities = labeled_entities
            self.processor.dataset = dataset
            self.processor.groups_info = groups_info

            # 恢复填充状态（关键修复）
            self.group_fill_status = data.get('group_fill_status', {})
            print(f"  恢复填充状态: {len(self.group_fill_status)} 个组已填充")

            # 重置当前组索引，跳转到第一个待处理组
            self.processor.current_group_index = 0

            # 🔑 关键修复：更新待处理手动组列表
            if hasattr(self.processor, '_update_pending_manual_groups'):
                self.processor._update_pending_manual_groups()
                print(f"  🔄 更新待处理组列表: {len(getattr(self.processor, 'pending_manual_groups', []))} 个待处理组")

            # 查找第一个未标注的组（增强版，排除自动标注）
            first_unlabeled_index = self._find_first_unlabeled_group()
            if first_unlabeled_index is not None:
                self.processor.current_group_index = first_unlabeled_index
                print(f"  🎯 跳转到第一个待处理组: 组{first_unlabeled_index + 1}")

                # 🔑 关键修复：设置手动标注模式，确保可以选择类别
                if hasattr(self.processor, 'manual_grouping_mode'):
                    self.processor.manual_grouping_mode = True
                    print(f"  🔧 启用手动标注模式")

                # 🔑 关键修复：设置当前手动组索引
                if hasattr(self.processor, 'pending_manual_groups') and self.processor.pending_manual_groups:
                    # 在待处理组中找到当前组的索引
                    current_group = self.processor.all_groups[first_unlabeled_index]
                    try:
                        manual_index = self.processor.pending_manual_groups.index(current_group)
                        self.processor.current_manual_group_index = manual_index
                        print(f"  🔧 设置手动组索引: {manual_index}")
                    except ValueError:
                        self.processor.current_manual_group_index = 0
                        print(f"  🔧 设置手动组索引: 0 (默认)")

                # 清除缓存标记，确保使用实际数据
                if hasattr(self.processor, '_cached_groups_info'):
                    delattr(self.processor, '_cached_groups_info')
                if hasattr(self.processor, '_cached_all_groups'):
                    delattr(self.processor, '_cached_all_groups')
            else:
                print("  所有组都已标注完成")

                # 🔑 关键修复：禁用手动标注模式
                if hasattr(self.processor, 'manual_grouping_mode'):
                    self.processor.manual_grouping_mode = False
                    print(f"  🔧 禁用手动标注模式")

                # 🔧 修复：显示完整视图而不是单个组
                print(f"  🌍 显示完整视图（所有组都已标注）")
                self._refresh_complete_view()

            # 恢复填充状态
            self.group_fill_status = data.get('group_fill_status', {})

            # 更新界面的所有组件（增强版：完整更新所有界面）
            print(f"  🔄 开始完整界面更新...")

            # 1. 更新组列表和统计信息
            try:
                self.update_group_list()
                if hasattr(self, 'update_stats'):
                    self.update_stats()
                print(f"    ✅ 组列表和统计信息更新完成")
            except Exception as e:
                print(f"    ⚠️ 组列表更新失败: {e}")
                import traceback
                traceback.print_exc()

            # 2. 更新预览图（显示当前组）
            if hasattr(self, 'update_preview'):
                try:
                    self.update_preview()
                    print(f"    ✅ 预览图更新完成")
                except Exception as e:
                    print(f"    ⚠️ 预览图更新失败: {e}")

            # 3. 更新文件处理列表（如果存在）
            if hasattr(self, 'update_file_list'):
                try:
                    self.update_file_list()
                    print(f"    ✅ 文件处理列表更新完成")
                except Exception as e:
                    print(f"    ⚠️ 文件处理列表更新失败: {e}")

            # 4. 更新所有相关的界面控件
            try:
                # 更新文件夹路径显示
                self.folder_var.set(self.current_folder)

                # 更新当前文件显示
                if hasattr(self, 'current_file_var'):
                    self.current_file_var.set(file_name)

                # 更新进度显示
                if hasattr(self, 'progress_var'):
                    if self.processor and hasattr(self.processor, 'all_groups'):
                        total_groups = len(self.processor.all_groups)
                        labeled_groups = sum(1 for g in self.processor.groups_info
                                           if g.get('status') == 'labeled') if self.processor.groups_info else 0
                        self.progress_var.set(f"{labeled_groups}/{total_groups}")

                print(f"    ✅ 界面控件更新完成")
            except Exception as e:
                print(f"    ⚠️ 界面控件更新失败: {e}")

            # 5. 更新状态显示
            status_info = self.file_status.get(file_name, {})
            proc_status = status_info.get('processing_status', 'unknown')
            anno_status = status_info.get('annotation_status', 'unknown')

            # 显示当前组信息
            if (self.processor and hasattr(self.processor, 'all_groups') and
                self.processor.all_groups and
                self.processor.current_group_index < len(self.processor.all_groups)):
                group_num = self.processor.current_group_index + 1
                total_groups = len(self.processor.all_groups)
                self.status_var.set(f"已切换到文件: {file_name} (处理: {proc_status}, 标注: {anno_status}) - 组 {group_num}/{total_groups}")
            else:
                self.status_var.set(f"已切换到文件: {file_name} (处理: {proc_status}, 标注: {anno_status})")

            # 6. 更新文件下拉菜单以反映当前文件
            self.update_file_combo()

            # 7. 刷新画布显示
            if hasattr(self, 'canvas') and self.canvas:
                try:
                    self.canvas.draw()  # 修复：使用draw()而不是update()
                    print(f"    ✅ 画布刷新完成")
                except Exception as e:
                    print(f"    ⚠️ 画布刷新失败: {e}")

            print(f"✅ 文件 {file_name} 数据加载和界面更新完成")

        except Exception as e:
            print(f"  ❌ 从缓存加载数据失败: {e}")
            import traceback
            traceback.print_exc()
            messagebox.showerror("错误", f"加载文件数据失败: {e}")

    def _handle_missing_cache_data(self, file_name, file_path):
        """处理缓存数据丢失的情况"""
        print(f"  ⚠️ 处理缓存数据丢失: {file_name}")
        messagebox.showinfo("提示", f"文件 {file_name} 的缓存数据丢失，需要重新处理")

        # 即使没有缓存，也要更新当前文件显示
        self.update_file_combo()

    def _find_first_unlabeled_group(self):
        """查找第一个未标注的组索引（增强版，支持缓存数据）"""
        try:
            if not hasattr(self, 'processor') or not self.processor:
                return None

            # 优先使用缓存数据（如果存在）
            if (hasattr(self.processor, '_cached_groups_info') and
                self.processor._cached_groups_info):

                for i, group_info in enumerate(self.processor._cached_groups_info):
                    status = group_info.get('status', 'unlabeled')
                    # 排除自动标注，只查找真正的未标注组
                    if status in ['unlabeled', 'pending', 'labeling']:
                        print(f"  找到未标注组: 组{i+1} (状态: {status})")
                        return i
                print("  缓存数据中没有找到未标注组")

            # 方法1：通过groups_info查找
            if hasattr(self.processor, 'groups_info') and self.processor.groups_info:

                for i, group_info in enumerate(self.processor.groups_info):
                    status = group_info.get('status', 'unlabeled')
                    # 排除自动标注，只查找真正的未标注组
                    if status in ['unlabeled', 'pending', 'labeling']:
                        print(f"  找到未标注组: 组{i+1} (状态: {status})")
                        return i

            # 方法2：通过all_groups查找未标注的实体
            if hasattr(self.processor, 'all_groups') and self.processor.all_groups:

                for i, group in enumerate(self.processor.all_groups):
                    # 检查组中是否有未标注的实体
                    has_unlabeled = any(not entity.get('label') and not entity.get('auto_labeled', False)
                                      for entity in group)
                    if has_unlabeled:
                        print(f"  找到包含未标注实体的组: 组{i+1}")
                        return i

            # 方法3：通过pending_manual_groups查找
            if hasattr(self.processor, 'pending_manual_groups') and self.processor.pending_manual_groups:

                if self.processor.pending_manual_groups and hasattr(self.processor, 'all_groups'):
                    first_pending_group = self.processor.pending_manual_groups[0]
                    if first_pending_group in self.processor.all_groups:
                        index = self.processor.all_groups.index(first_pending_group)
                        print(f"  找到待处理组: 组{index+1}")
                        return index


            return None

        except Exception as e:
            print(f"查找第一个未标注组失败: {e}")
            return None

    def _perform_complete_ui_update_for_file_switch(self, file_name):
        """文件切换时执行完整的界面更新"""
        try:
            print(f"🔄 执行文件切换的完整界面更新: {file_name}")

            # 1. 更新组列表
            self.update_group_list()
            print("  ✅ 组列表更新完成")

            # 2. 更新详细视图（显示当前组）
            try:
                if (hasattr(self.processor, 'all_groups') and self.processor.all_groups and
                    hasattr(self.processor, 'current_group_index') and
                    self.processor.current_group_index < len(self.processor.all_groups)):

                    current_group = self.processor.all_groups[self.processor.current_group_index]
                    group_index = self.processor.current_group_index + 1

                    # 确保跳转到第一个待标注组
                    first_unlabeled_index = self._find_first_unlabeled_group()
                    if first_unlabeled_index is not None and first_unlabeled_index != self.processor.current_group_index:
                        self.processor.current_group_index = first_unlabeled_index
                        current_group = self.processor.all_groups[first_unlabeled_index]
                        group_index = first_unlabeled_index + 1
                        print(f"  🎯 自动跳转到第一个待标注组: 组{group_index}")

                    self._show_group(current_group, group_index)
                    print(f"  ✅ 详细视图更新完成: 显示组{group_index}")
                else:
                    print(f"  ⚠️ 跳过详细视图更新: 没有有效的组数据")
            except Exception as e:
                print(f"  ❌ 详细视图更新失败: {e}")
                # 继续执行其他更新，不中断整个流程

            # 3. 更新全图概览
            try:
                if (hasattr(self.processor, 'visualizer') and self.processor.visualizer and
                    hasattr(self.processor, 'current_file_entities') and self.processor.current_file_entities):

                    # 获取当前组（可能已经在步骤2中更新过）
                    current_group = None
                    if (hasattr(self.processor, 'all_groups') and self.processor.all_groups and
                        hasattr(self.processor, 'current_group_index') and
                        self.processor.current_group_index < len(self.processor.all_groups)):
                        current_group = self.processor.all_groups[self.processor.current_group_index]

                    # 清理组数据，确保只包含有效实体
                    print(f"🎨 [DEBUG] 准备可视化概览:")
                    print(f"  原始current_group: {type(current_group)} - {len(current_group) if current_group else 0} 个")

                    cleaned_current_group = self._clean_group_data(current_group) if current_group else []

                    print(f"  清理后current_group: {len(cleaned_current_group)} 个")
                    print(f"  所有实体数: {len(self.processor.current_file_entities) if hasattr(self.processor, 'current_file_entities') and self.processor.current_file_entities else 0}")
                    print(f"  已标注实体数: {len(self.processor.labeled_entities) if hasattr(self.processor, 'labeled_entities') and self.processor.labeled_entities else 0}")

                    self.processor.visualizer.visualize_overview(
                        self.processor.current_file_entities,
                        cleaned_current_group,  # 使用清理后的当前组
                        self.processor.labeled_entities if hasattr(self.processor, 'labeled_entities') else [],
                        processor=self.processor
                    )
                    print("  ✅ 全图概览更新完成")
                else:
                    print(f"  ⚠️ 跳过全图概览更新: 没有有效的可视化器或实体数据")
            except Exception as e:
                print(f"  ❌ 全图概览更新失败: {e}")
                # 继续执行其他更新，不中断整个流程

            # 4. 可视化更新
            if hasattr(self, 'canvas') and self.canvas:
                self.canvas.draw()
                print("  ✅ 可视化更新完成")

            # 5. 更新统计信息
            if hasattr(self, 'update_stats'):
                self.update_stats()
                print("  ✅ 统计信息更新完成")

            # 6. 更新文件下拉菜单
            self.update_file_combo()
            print("  ✅ 文件下拉菜单更新完成")

            # 7. 确保跳转到第一个待标注组
            if hasattr(self.processor, 'jump_to_first_unlabeled_group'):
                try:
                    self.processor.jump_to_first_unlabeled_group()
                    print("  ✅ 自动跳转到第一个待标注组完成")
                except Exception as e:
                    print(f"  ⚠️ 自动跳转失败: {e}")

            print(f"✅ 文件切换的完整界面更新完成: {file_name}")

        except Exception as e:
            print(f"❌ 文件切换界面更新失败: {e}")
            import traceback
            traceback.print_exc()

    def save_all_data(self):
        """保存所有文件的数据"""
        if not self.current_folder:
            messagebox.showwarning("警告", "请先选择文件夹")
            return

        # 检查是否所有文件都已处理完成
        unprocessed_files = []
        for file_name, status in self.file_status.items():
            if status.get('processing_status') != 'completed':
                unprocessed_files.append(file_name)

        if unprocessed_files:
            messagebox.showwarning("警告", f"以下文件尚未处理完成，无法保存：\n{', '.join(unprocessed_files)}")
            return

        # 保存当前文件数据
        self._save_current_file_data()

        # 构建保存数据（确保所有数据都可序列化）
        try:
            save_data = {
                'folder': self.current_folder,
                'files': self.all_files,
                'file_status': self._serialize_data(self.file_status),
                'file_data': self._serialize_data(self.file_data),
                'save_time': datetime.now().isoformat(),
                'version': '1.0'
            }

            # 保存到文件
            save_path = os.path.join(self.current_folder, 'cad_annotation_data.json')
            with open(save_path, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, ensure_ascii=False, indent=2)

            messagebox.showinfo("成功", f"所有数据已保存到：\n{save_path}")
            self.status_var.set("数据保存成功")

        except Exception as e:
            print(f"保存数据详细错误: {e}")
            import traceback
            traceback.print_exc()
            messagebox.showerror("错误", f"保存数据失败：{str(e)}")

    def load_all_data(self):
        """读取所有文件的数据"""
        from tkinter import filedialog

        # 弹出文件选择对话框
        load_path = filedialog.askopenfilename(
            title="选择要读取的数据文件",
            filetypes=[
                ("JSON文件", "*.json"),
                ("所有文件", "*.*")
            ],
            initialdir=self.current_folder if self.current_folder else os.getcwd()
        )

        if not load_path:
            return  # 用户取消了选择

        print(f"🔄 读取数据文件: {load_path}")

        try:
            with open(load_path, 'r', encoding='utf-8') as f:
                save_data = json.load(f)

            # 验证数据版本
            if save_data.get('version') != '1.0':
                messagebox.showwarning("警告", "数据文件版本不兼容")
                return

            print(f"  数据文件验证通过，开始恢复数据...")

            # 恢复数据
            self.current_folder = save_data.get('folder', self.current_folder)
            self.all_files = save_data.get('files', [])
            self.file_status = save_data.get('file_status', {})
            self.file_data = save_data.get('file_data', {})

            print(f"  恢复数据完成:")
            print(f"    文件夹: {self.current_folder}")
            print(f"    文件数: {len(self.all_files)}")
            print(f"    状态数: {len(self.file_status)}")
            print(f"    数据数: {len(self.file_data)}")

            # 更新文件夹路径显示
            self.folder_var.set(self.current_folder)

            # 更新文件下拉菜单
            self.update_file_combo()

            # 选择第一个有数据的文件进行显示
            first_file_with_data = None
            for file_path in self.all_files:
                file_name = os.path.basename(file_path)
                if file_name in self.file_data:
                    first_file_with_data = file_path
                    break

            if first_file_with_data:
                print(f"  加载第一个有数据的文件: {os.path.basename(first_file_with_data)}")
                self._load_file_data(first_file_with_data)

                # 更新状态显示
                file_name = os.path.basename(first_file_with_data)
                status_info = self.file_status.get(file_name, {})
                proc_status = status_info.get('processing_status', 'unknown')
                anno_status = status_info.get('annotation_status', 'unknown')

                self.status_var.set(f"数据读取成功 - 当前文件: {file_name} (处理: {proc_status}, 标注: {anno_status})")
            else:
                print(f"  没有找到有数据的文件")
                self.status_var.set("数据读取成功 - 没有找到处理过的文件")

            # 显示成功消息
            save_time = save_data.get('save_time', '未知')
            file_count = len([f for f in self.file_status.values() if f.get('processing_status') == 'completed'])

            messagebox.showinfo("成功",
                f"数据读取成功！\n"
                f"保存时间：{save_time}\n"
                f"文件夹：{os.path.basename(self.current_folder)}\n"
                f"已处理文件：{file_count} 个\n"
                f"总文件数：{len(self.all_files)} 个")

            print(f"✅ 数据读取完成")

        except Exception as e:
            print(f"❌ 读取数据失败: {e}")
            import traceback
            traceback.print_exc()
            messagebox.showerror("错误", f"读取数据失败：{str(e)}")

    def start_processing(self):
        """开始处理 - 第一阶段：基础数据加载"""
        folder = self.folder_var.get()

        if not folder:
            messagebox.showwarning("警告", "请先选择文件夹")
            return

        # 更新按钮状态
        self.start_btn.config(state='disabled')
        self.stop_btn.config(state='normal')
        self.processing_stage = "basic"

        # 初始化文件管理
        self.current_folder = folder
        self._scan_folder_files()

        if self.all_files:
            # 启动基础数据加载阶段
            threading.Thread(target=self._process_basic_stage_v2, args=(folder,), daemon=True).start()
        else:
            # 提供更详细的错误信息
            self._show_no_files_dialog()
            # 重新启用按钮
            self._reset_button_states()

    def _reset_button_states(self):
        """重置按钮状态"""
        self.start_btn.config(state='normal')
        if hasattr(self, 'line_process_btn'):
            self.line_process_btn.config(state='disabled')
        if hasattr(self, 'group_process_btn'):
            self.group_process_btn.config(state='disabled')
        self.stop_btn.config(state='disabled')
        self.processing_stage = "none"

    def start_line_processing(self):
        """开始线条处理 - 第二阶段"""
        if not hasattr(self, 'processing_stage') or self.processing_stage != "basic":
            messagebox.showwarning("警告", "请先完成基础数据加载")
            return

        self.line_process_btn.config(state='disabled')
        self.processing_stage = "line"

        # 在后台线程中进行线条处理
        threading.Thread(target=self._process_line_stage_v2, daemon=True).start()

    def start_group_processing(self):
        """开始识别分组 - 第三阶段"""
        if not hasattr(self, 'processing_stage') or self.processing_stage != "line":
            messagebox.showwarning("警告", "请先完成线条处理")
            return

        self.group_process_btn.config(state='disabled')
        self.processing_stage = "group"

        # 在后台线程中进行分组处理
        threading.Thread(target=self._process_group_stage_v2, daemon=True).start()

    def _process_basic_stage_v2(self, folder):
        """第一阶段：基础数据加载（V2版本）"""
        try:
            self.status_var.set("阶段1：加载CAD文件...")

            # 执行基础数据加载
            success = self._load_basic_data(folder)

            if success:
                # 更新界面显示基础数据
                self.root.after(0, self._update_visualization_basic_v2)

                # 启用线条处理按钮
                self.root.after(0, lambda: (
                    self.line_process_btn.config(state='normal'),
                    self.status_var.set("基础数据加载完成，可进行线条处理")
                ))
            else:
                self.root.after(0, lambda: (
                    self._reset_button_states(),
                    self.status_var.set("基础数据加载失败")
                ))

        except Exception as e:
            self.root.after(0, lambda: (
                self._reset_button_states(),
                self.status_var.set(f"基础处理失败: {str(e)}")
            ))

    def _process_line_stage_v2(self):
        """第二阶段：线条处理和合并（V2版本）"""
        try:
            self.status_var.set("阶段2：处理线条合并...")

            # 执行线条处理
            success = self._process_line_merging()

            # 无论成功与否，都继续流程
            if success:
                # 更新界面显示线条处理结果
                self.root.after(0, self._update_visualization_line_v2)
                status_message = "线条处理完成，可进行识别分组"
            else:
                # 即使处理失败，也使用原始数据继续
                if self.processor and hasattr(self.processor, 'raw_entities'):
                    self.processor.merged_entities = self.processor.raw_entities

                self.root.after(0, self._update_visualization_line_v2)
                status_message = "线条处理完成（使用原始数据），可进行识别分组"

            # 无论成功与否，都启用分组处理按钮
            self.root.after(0, lambda: (
                self.group_process_btn.config(state='normal'),
                self.status_var.set(status_message)
            ))

        except Exception as e:
            # 即使出现异常，也尝试继续流程
            try:
                if self.processor and hasattr(self.processor, 'raw_entities'):
                    self.processor.merged_entities = self.processor.raw_entities

                self.root.after(0, lambda: (
                    self.group_process_btn.config(state='normal'),
                    self.status_var.set(f"线条处理异常但可继续: {str(e)}")
                ))
            except:
                # 如果完全失败，重置到线条处理阶段
                self.root.after(0, lambda: (
                    self.line_process_btn.config(state='normal'),
                    self.status_var.set(f"线条处理失败: {str(e)}")
                ))

    def _process_group_stage_v2(self):
        """第三阶段：识别分组（V2版本）"""
        try:
            self.status_var.set("阶段3：识别分组...")

            # 执行分组处理
            success = self._process_grouping()

            if success:
                # 更新界面显示分组结果
                self.root.after(0, self._update_visualization_group_v2)

                # 启用其他操作按钮
                self.root.after(0, lambda: (
                    self._enable_post_processing_buttons_v2(),
                    self.status_var.set("识别分组完成，可进行填充、房间识别等操作")
                ))
                self.processing_stage = "complete"
            else:
                self.root.after(0, lambda: (
                    self.group_process_btn.config(state='normal'),
                    self.status_var.set("识别分组失败")
                ))

        except Exception as e:
            self.root.after(0, lambda: (
                self.group_process_btn.config(state='normal'),
                self.status_var.set(f"识别分组失败: {str(e)}")
            ))

    def _load_basic_data(self, folder):
        """加载基础数据"""
        try:
            # 获取第一个DXF文件
            dxf_files = []
            for root, dirs, files in os.walk(folder):
                for file in files:
                    if file.lower().endswith('.dxf'):
                        dxf_files.append(os.path.join(root, file))

            if not dxf_files:
                return False

            # 处理第一个文件
            file_path = dxf_files[0]
            self.current_file = os.path.basename(file_path)

            # 创建处理器
            if not self.processor:
                from main_enhanced import EnhancedCADProcessor
                self.processor = EnhancedCADProcessor(self.visualizer, self.canvas)
                self.processor.set_callbacks(self.on_status_update, self.on_progress_update)

            # 加载DXF文件
            entities = self.processor.processor.load_dxf_file(file_path)
            if not entities:
                return False

            # 保存基础数据
            self.processor.raw_entities = entities
            self.processor.current_file_entities = entities

            return True

        except Exception as e:
            print(f"基础数据加载失败: {e}")
            return False

    def _process_line_merging(self):
        """处理线条合并"""
        try:
            if not self.processor or not hasattr(self.processor, 'raw_entities'):
                return False

            # 执行线条合并
            merged_entities = self.processor.processor.merge_lines(self.processor.raw_entities)
            self.processor.merged_entities = merged_entities

            return True

        except Exception as e:
            print(f"线条处理失败: {e}")
            return False

    def _process_grouping(self):
        """处理分组"""
        try:
            if not self.processor or not hasattr(self.processor, 'merged_entities'):
                return False

            # 执行分组
            groups = self.processor.processor.group_entities(self.processor.merged_entities)
            self.processor.all_groups = groups

            # 初始化组状态信息
            self.processor.groups_info = []
            for i, group in enumerate(groups):
                self.processor.groups_info.append({
                    'index': i,
                    'status': 'unlabeled',
                    'category': None,
                    'entity_count': len(group)
                })

            return True

        except Exception as e:
            print(f"识别分组失败: {e}")
            return False

    def _update_visualization_basic_v2(self):
        """更新基础数据可视化（V2版本）"""
        try:
            if self.visualizer and self.processor and hasattr(self.processor, 'raw_entities'):
                self.visualizer.clear_all()
                self.visualizer.draw_entities(self.processor.raw_entities)
                if self.canvas:
                    self.canvas.draw()
                print("✅ 基础数据可视化更新完成")
        except Exception as e:
            print(f"⚠️ 基础数据可视化更新失败: {e}")

    def _update_visualization_line_v2(self):
        """更新线条处理可视化（V2版本）"""
        try:
            if self.visualizer and self.processor and hasattr(self.processor, 'merged_entities'):
                self.visualizer.clear_all()
                self.visualizer.draw_entities(self.processor.merged_entities)
                if self.canvas:
                    self.canvas.draw()
                print("✅ 线条处理可视化更新完成")
        except Exception as e:
            print(f"⚠️ 线条处理可视化更新失败: {e}")

    def _update_visualization_group_v2(self):
        """更新分组可视化（V2版本）"""
        try:
            if self.visualizer and self.processor and hasattr(self.processor, 'all_groups'):
                self.visualizer.clear_all()
                self.visualizer.draw_groups(self.processor.all_groups)
                if self.canvas:
                    self.canvas.draw()
                # 更新组列表
                self.update_group_list()
                print("✅ 分组可视化更新完成")
        except Exception as e:
            print(f"⚠️ 分组可视化更新失败: {e}")

    def _enable_post_processing_buttons_v2(self):
        """启用后处理按钮（V2版本）"""
        # 这里可以启用其他操作按钮，如填充、房间识别等
        # 具体实现根据界面需要调整
        pass

    def _start_separated_processing(self):
        """启动新的分离式处理流程"""
        print("🚀 启动分离式多文件处理流程")
        print("=" * 60)

        # 重置处理状态
        self.should_stop_background = False
        self.file_data = {}

        # 阶段1：批量处理所有文件（不更新界面）
        self._batch_process_all_files()

    def _batch_process_all_files(self):
        """批量处理所有文件（不更新任何界面）"""
        print("📋 阶段1：批量处理所有文件（仅显示进度，不更新界面）")

        def batch_worker():
            total_files = len(self.all_files)
            processed_count = 0
            failed_files = []

            for i, file_name in enumerate(self.all_files):
                if self.should_stop_background:
                    print("⏹️ 用户停止处理")
                    break

                # 构建完整文件路径
                file_path = os.path.join(self.current_folder, file_name)

                # 更新进度显示
                progress = (i + 1) / total_files * 100
                self.root.after(0, lambda p=progress, f=file_name, idx=i+1, total=total_files:
                    self._update_batch_progress(p, f, idx, total))

                print(f"\n📄 处理文件 {i+1}/{total_files}: {file_name}")
                print(f"  文件路径: {file_path}")

                try:
                    # 确保文件状态字典中有这个文件的记录
                    if file_name not in self.file_status:
                        print(f"  ⚠️ 文件状态字典中没有 {file_name}，创建新记录")
                        self.file_status[file_name] = {
                            'processing_status': 'unprocessed',
                            'annotation_status': 'unannotated'
                        }

                    # 更新文件状态为处理中
                    self.file_status[file_name]['processing_status'] = 'processing'

                    # 执行文件处理（完全后台，不更新界面）
                    success = self._process_file_completely_background(file_path)

                    if success:
                        self.file_status[file_name]['processing_status'] = 'completed'
                        processed_count += 1
                        print(f"  ✅ 处理成功")
                    else:
                        self.file_status[file_name]['processing_status'] = 'failed'
                        failed_files.append(file_name)
                        print(f"  ❌ 处理失败")

                except Exception as e:
                    print(f"  ❌ 处理异常: {e}")
                    import traceback
                    traceback.print_exc()

                    # 确保文件状态字典中有这个文件的记录
                    if file_name not in self.file_status:
                        self.file_status[file_name] = {
                            'processing_status': 'unprocessed',
                            'annotation_status': 'unannotated'
                        }

                    self.file_status[file_name]['processing_status'] = 'failed'
                    failed_files.append(file_name)

                # 短暂休息避免占用过多资源
                time.sleep(0.05)

            # 阶段1完成，启动阶段2
            if not self.should_stop_background:
                self.root.after(0, lambda: self._complete_batch_processing(processed_count, failed_files))

        # 启动后台线程
        threading.Thread(target=batch_worker, daemon=True).start()

    def _update_batch_progress(self, progress, file_name, current, total):
        """更新批量处理进度（仅更新进度条和状态，不更新文件下拉菜单）"""
        try:
            # 更新进度条
            if hasattr(self, 'progress_bar'):
                self.progress_bar['value'] = progress

            # 更新状态显示
            self.status_var.set(f"批量处理中: {current}/{total} - {file_name}")

            # 🔑 关键修复：处理过程中不更新文件下拉菜单
            # 避免界面跳转和显示错误
            print(f"  📊 进度更新: {progress:.1f}% - {file_name}")

        except Exception as e:
            print(f"更新批量进度失败: {e}")

    def _process_file_completely_background(self, file_path):
        """完全后台处理文件（不更新任何界面）"""
        file_name = os.path.basename(file_path)

        try:
            # 创建独立的后台处理器
            from main_enhanced import EnhancedCADProcessor
            bg_processor = EnhancedCADProcessor(None, None)  # 不传递可视化组件

            # 完全禁用所有回调和界面更新
            bg_processor.status_callback = None
            bg_processor.progress_callback = None
            bg_processor.visualizer = None
            bg_processor.canvas = None
            bg_processor._is_background_processing = True

            # 设置文件路径
            bg_processor.current_file = file_path

            # 执行核心处理逻辑
            success = bg_processor.process_single_file(file_path)

            if success:
                # 保存处理结果到缓存
                self._save_background_processing_result(file_name, bg_processor)
                return True
            else:
                print(f"  处理器返回失败")
                return False

        except Exception as e:
            print(f"  后台处理异常: {e}")
            import traceback
            traceback.print_exc()
            return False

    def _save_background_processing_result(self, file_name, bg_processor):
        """保存后台处理结果到缓存"""
        try:
            # 序列化处理结果
            data = {
                'entities': self._serialize_data(bg_processor.current_file_entities or []),
                'all_groups': self._serialize_data(bg_processor.all_groups or []),  # 🔑 保持all_groups键名
                'groups': self._serialize_data(bg_processor.all_groups or []),      # 🔑 同时保存groups键名以兼容
                'labeled_entities': self._serialize_data(bg_processor.labeled_entities or []),
                'dataset': self._serialize_data(bg_processor.dataset or []),
                'groups_info': self._serialize_data(bg_processor.groups_info or []),
                'group_fill_status': {},  # 初始化为空
                'timestamp': datetime.now().isoformat(),
                'processing_method': 'background_batch'
            }



            # 保存到文件数据缓存
            self.file_data[file_name] = data

            # 检查标注状态
            if bg_processor.labeled_entities:
                total_entities = len(bg_processor.current_file_entities) if bg_processor.current_file_entities else 0
                labeled_entities = len(bg_processor.labeled_entities)

                if labeled_entities == total_entities and total_entities > 0:
                    self.file_status[file_name]['annotation_status'] = 'completed'
                elif labeled_entities > 0:
                    self.file_status[file_name]['annotation_status'] = 'incomplete'

            print(f"  💾 缓存数据已保存")

        except Exception as e:
            print(f"  保存缓存失败: {e}")
            import traceback
            traceback.print_exc()

    def _complete_batch_processing(self, processed_count, failed_files):
        """完成批量处理，启动界面更新阶段"""
        print("\n" + "=" * 60)
        print("📋 阶段1完成：批量处理结果")
        print(f"  ✅ 成功处理: {processed_count} 个文件")
        print(f"  ❌ 处理失败: {len(failed_files)} 个文件")

        if failed_files:
            print("  失败文件列表:")
            for file_name in failed_files:
                print(f"    - {file_name}")

        print("\n📋 阶段2：界面更新和显示")

        # 更新所有文件的缓存写入完成
        print("  💾 所有文件识别内容已写入缓存")

        # 更新文件选择下拉菜单
        self.update_file_combo()
        print("  📋 文件选择下拉菜单已更新")

        # 设置第一个成功处理的文件为当前显示文件
        first_successful_file = None
        for file_name in self.all_files:
            if self.file_status[file_name]['processing_status'] == 'completed':
                first_successful_file = file_name
                break

        if first_successful_file:
            # 切换到第一个文件并更新所有界面
            self._switch_to_first_file_and_update_ui(first_successful_file)
        else:
            # 没有成功处理的文件
            self.status_var.set("所有文件处理失败")
            messagebox.showerror("处理失败", "所有文件都处理失败，请检查文件格式和内容")

        # 重新启用按钮
        self.start_btn.config(state='normal')
        self.stop_btn.config(state='disabled')

        print("✅ 分离式多文件处理流程完成")

    def _switch_to_first_file_and_update_ui(self, file_name):
        """切换到第一个文件并更新所有界面"""
        print(f"🖥️ 切换到第一个文件并更新界面: {file_name}")

        try:
            file_path = os.path.join(self.current_folder, file_name)

            # 设置为当前处理文件和显示文件
            self.current_file = file_path
            self.display_file = file_path

            # 加载文件数据（从缓存）
            self._load_file_data(file_path)
            print("  ✅ 文件数据加载完成")

            # 🔑 关键修复：保存当前组索引，防止被update_group_list修改
            saved_current_group_index = getattr(self.processor, 'current_group_index', None)


            # 更新组列表
            self.update_group_list()
            print("  ✅ 组列表更新完成")

            # 🔑 关键修复：恢复当前组索引
            if saved_current_group_index is not None:
                self.processor.current_group_index = saved_current_group_index


            # 更新详细视图（显示当前组）


            if (hasattr(self.processor, 'all_groups') and self.processor.all_groups and
                hasattr(self.processor, 'current_group_index') and
                self.processor.current_group_index is not None and
                self.processor.current_group_index < len(self.processor.all_groups)):

                current_group = self.processor.all_groups[self.processor.current_group_index]
                group_index = self.processor.current_group_index + 1
                self._show_group(current_group, group_index)
                print(f"  ✅ 详细视图更新完成: 显示组{group_index}")
            else:
                print("  ⚠️ 跳过详细视图更新: 没有有效的组数据")

            # 更新全图概览
            try:
                if (hasattr(self.processor, 'visualizer') and self.processor.visualizer and
                    hasattr(self.processor, 'current_file_entities') and self.processor.current_file_entities):

                    current_group = None
                    current_group_index = None



                    if (hasattr(self.processor, 'all_groups') and self.processor.all_groups and
                        hasattr(self.processor, 'current_group_index') and
                        self.processor.current_group_index is not None and
                        self.processor.current_group_index < len(self.processor.all_groups)):
                        current_group = self.processor.all_groups[self.processor.current_group_index]
                        current_group_index = self.processor.current_group_index + 1  # 🔑 组索引从1开始

                    # 清理组数据，确保只包含有效实体
                    cleaned_current_group = self._clean_group_data(current_group) if current_group else []

                    # 🔑 关键修复：传递组索引参数以显示高亮
                    self.processor.visualizer.visualize_overview(
                        self.processor.current_file_entities,
                        cleaned_current_group,  # 使用清理后的当前组
                        self.processor.labeled_entities or [],
                        processor=self.processor,
                        current_group_index=current_group_index  # 🔑 传递组索引
                    )
                    self.processor.visualizer.update_canvas(self.canvas)
                    print("  ✅ 全图概览更新完成")
                else:
                    print("  ⚠️ 跳过全图概览更新: 没有有效的可视化器或实体数据")
            except Exception as e:
                print(f"  ❌ 全图概览更新失败: {e}")

            # 可视化更新
            if hasattr(self, 'canvas') and self.canvas:
                self.canvas.draw()
                print("  ✅ 可视化更新完成")

            # 更新统计信息
            if hasattr(self, 'update_stats'):
                self.update_stats()
                print("  ✅ 统计信息更新完成")

            # 更新文件下拉菜单并选中当前文件
            self.update_file_combo()
            # 设置下拉菜单选择为当前文件
            for value in self.file_combo['values']:
                if file_name in value:
                    self.file_combo.set(value)
                    break
            print("  ✅ 文件下拉菜单更新完成")

            # 更新状态显示
            status_info = self.file_status.get(file_name, {})
            proc_status = status_info.get('processing_status', 'unknown')
            anno_status = status_info.get('annotation_status', 'unknown')

            if (self.processor and hasattr(self.processor, 'all_groups') and
                self.processor.all_groups and
                self.processor.current_group_index < len(self.processor.all_groups)):
                group_num = self.processor.current_group_index + 1
                total_groups = len(self.processor.all_groups)
                self.status_var.set(f"当前文件: {file_name} (处理: {proc_status}, 标注: {anno_status}) - 组 {group_num}/{total_groups}")
            else:
                self.status_var.set(f"当前文件: {file_name} (处理: {proc_status}, 标注: {anno_status})")

            print(f"✅ 界面更新完成: {file_name}")

        except Exception as e:
            print(f"❌ 切换到第一个文件失败: {e}")
            import traceback
            traceback.print_exc()
            self.status_var.set(f"切换到文件 {file_name} 失败")

    def _scan_folder_files(self):
        """扫描文件夹中的CAD文件"""
        self.all_files = []
        self.file_status = {}

        print(f"开始扫描文件夹: {self.current_folder}")

        # 检查文件夹是否存在
        if not self.current_folder:
            print("❌ 未设置文件夹路径")
            self._show_no_files_dialog()
            return

        if not os.path.exists(self.current_folder):
            print(f"❌ 文件夹不存在: {self.current_folder}")
            self._show_no_files_dialog()
            return

        if not os.path.isdir(self.current_folder):
            print(f"❌ 路径不是目录: {self.current_folder}")
            self._show_no_files_dialog()
            return

        # 支持的文件扩展名
        supported_extensions = ['.dxf', '.dwg']

        try:
            files_in_folder = os.listdir(self.current_folder)
            print(f"📋 文件夹中有 {len(files_in_folder)} 个项目")

            for file_name in files_in_folder:
                file_path = os.path.join(self.current_folder, file_name)
                print(f"  检查: {file_name}")

                if os.path.isfile(file_path):
                    _, ext = os.path.splitext(file_name.lower())
                    print(f"    文件扩展名: '{ext}'")

                    if ext in supported_extensions:
                        # 🔑 关键修复：统一使用文件名而不是完整路径
                        self.all_files.append(file_name)  # 只存储文件名
                        self.file_status[file_name] = {
                            'processing_status': 'unprocessed',
                            'annotation_status': 'unannotated'
                        }
                        print(f"    ✅ 添加CAD文件: {file_name}")
                        print(f"      完整路径: {file_path}")
                    else:
                        print(f"    ❌ 跳过非CAD文件: {file_name}")
                else:
                    print(f"    📁 跳过目录: {file_name}")

        except Exception as e:
            print(f"❌ 扫描文件夹时出错: {e}")
            import traceback
            traceback.print_exc()

        print(f"🎯 扫描结果: 找到 {len(self.all_files)} 个CAD文件")

        # 如果没有找到文件，显示诊断对话框
        if not self.all_files:
            print("⚠️ 未找到CAD文件，显示诊断对话框")
            self._show_no_files_dialog()
        else:
            print(f"✅ 成功找到以下CAD文件:")
            for file_path in self.all_files:
                print(f"    {os.path.basename(file_path)}")

        # 更新文件选择下拉菜单
        self.update_file_combo()

        self.status_var.set(f"找到 {len(self.all_files)} 个CAD文件")

    def _show_no_files_dialog(self):
        """显示未找到文件的详细诊断对话框"""
        # 收集诊断信息
        folder_exists = os.path.exists(self.current_folder)
        is_directory = os.path.isdir(self.current_folder) if folder_exists else False

        all_files = []
        cad_files = []

        if folder_exists and is_directory:
            try:
                all_files = os.listdir(self.current_folder)
                supported_extensions = ['.dxf', '.dwg']

                for file_name in all_files:
                    file_path = os.path.join(self.current_folder, file_name)
                    if os.path.isfile(file_path):
                        _, ext = os.path.splitext(file_name.lower())
                        if ext in supported_extensions:
                            cad_files.append(file_name)
            except:
                pass

        # 构建诊断消息
        message = "未找到CAD文件\n\n"
        message += f"文件夹路径: {self.current_folder}\n"
        message += f"文件夹存在: {'是' if folder_exists else '否'}\n"
        message += f"是目录: {'是' if is_directory else '否'}\n"

        if folder_exists and is_directory:
            message += f"文件夹中共有 {len(all_files)} 个项目\n"

            if all_files:
                message += "\n文件夹内容:\n"
                for file_name in all_files[:10]:  # 最多显示10个文件
                    file_path = os.path.join(self.current_folder, file_name)
                    if os.path.isfile(file_path):
                        _, ext = os.path.splitext(file_name.lower())
                        message += f"  📄 {file_name} (扩展名: {ext})\n"
                    else:
                        message += f"  📁 {file_name}\n"

                if len(all_files) > 10:
                    message += f"  ... 还有 {len(all_files) - 10} 个项目\n"

            if cad_files:
                message += f"\n找到 {len(cad_files)} 个CAD文件:\n"
                for cad_file in cad_files:
                    message += f"  ✅ {cad_file}\n"
            else:
                message += "\n❌ 没有找到 .dxf 或 .dwg 文件\n"

        message += "\n💡 解决建议:\n"
        message += "1. 确认文件夹中包含 .dxf 或 .dwg 文件\n"
        message += "2. 检查文件扩展名是否正确\n"
        message += "3. 确认文件不是隐藏文件\n"
        message += "4. 尝试选择其他文件夹"

        messagebox.showinfo("诊断信息", message)

    def _start_file_processing(self, file_path):
        """开始处理指定文件"""
        file_name = os.path.basename(file_path)

        # 区分处理文件和显示文件
        # current_file: 当前正在处理的文件（可能在后台）
        # display_file: 界面显示的文件（始终是第一个文件）
        processing_file = file_path

        # 严格区分显示文件和处理文件
        is_display_file = hasattr(self, 'display_file') and file_path == self.display_file

        # 只有在处理显示文件时才更新current_file（避免界面跳转）
        if is_display_file:
            self.current_file = file_path  # 更新当前文件用于界面显示
            print(f"🖥️ 更新界面显示文件: {file_name}")
        else:
            print(f"🔄 后台处理文件: {file_name}")

        # 更新文件状态
        self.file_status[file_name]['processing_status'] = 'processing'

        # 更新文件下拉菜单（不改变当前选择）
        current_selection = self.file_combo.get()
        self.update_file_combo()
        if current_selection and current_selection in self.file_combo['values']:
            self.file_combo.set(current_selection)

        # 设置文件夹路径（父类需要）
        self.folder_var.set(self.current_folder)

        # 确保处理器存在并设置回调
        if not self.processor:
            from main_enhanced import EnhancedCADProcessor
            self.processor = EnhancedCADProcessor(self.visualizer, self.canvas)

        # 设置回调以接收状态更新
        self.processor.set_callbacks(self.on_status_update, self.on_progress_update)

        print(f"🔄 开始处理文件: {file_name}")
        print(f"  处理文件: {processing_file}")
        print(f"  显示文件: {getattr(self, 'display_file', '未设置')}")
        print(f"  是否为显示文件: {is_display_file}")

        # 只有在处理显示文件时才调用父类的处理方法（更新界面）
        if is_display_file:
            # 调用父类的处理方法
            super().start_processing()
        else:
            # 后台处理：只处理文件，不更新界面
            self._process_file_in_background(processing_file)

    def _start_background_processing(self):
        """启动后台处理其他文件（增强版：确保界面不受影响）"""
        def background_worker():
            print(f"🔄 开始后台处理 {len(self.all_files) - 1} 个文件...")

            for i, file_path in enumerate(self.all_files[1:], 1):  # 跳过第一个文件（已在前台处理）
                file_name = os.path.basename(file_path)
                print(f"  处理文件 {i}/{len(self.all_files) - 1}: {file_name}")

                # 检查是否应该停止
                if getattr(self, 'should_stop_background', False):
                    print(f"  后台处理被停止")
                    break

                try:
                    # 更新状态为处理中
                    self.file_status[file_name]['processing_status'] = 'processing'

                    # 后台处理：只更新内部状态，不更新界面
                    print(f"    📋 后台文件状态更新: {file_name} -> 处理中")

                    # 创建独立的后台处理器（不影响前台）
                    from main_enhanced import EnhancedCADProcessor
                    bg_processor = EnhancedCADProcessor(None, None)  # 不传递可视化组件

                    # 完全禁用所有回调，避免触发界面更新
                    bg_processor.status_callback = None
                    bg_processor.progress_callback = None

                    # 设置后台处理标记
                    bg_processor._is_background_processing = True

                    # 禁用可视化相关功能
                    bg_processor.visualizer = None
                    bg_processor.canvas = None

                    # 设置文件路径
                    bg_processor.current_file = file_path

                    # 执行后台处理（简化版，不更新界面）
                    print(f"    开始处理...")
                    success = self._process_file_background_safe(bg_processor, file_path)

                    if success:
                        # 保存处理结果（序列化处理）
                        # 🔑 关键修复：同时保存两种键名以确保兼容性
                        groups_data = self._serialize_data(bg_processor.all_groups or [])
                        self.file_data[file_name] = {
                            'entities': self._serialize_data(bg_processor.current_file_entities or []),
                            'groups': groups_data,      # 🔑 兼容旧版本
                            'all_groups': groups_data,  # 🔑 新版本键名
                            'labeled_entities': self._serialize_data(bg_processor.labeled_entities or []),
                            'dataset': self._serialize_data(bg_processor.dataset or []),
                            'groups_info': self._serialize_data(bg_processor.groups_info or []),
                            'group_fill_status': {},
                            'timestamp': datetime.now().isoformat()
                        }

                        # 更新状态为已完成
                        self.file_status[file_name]['processing_status'] = 'completed'
                        print(f"    ✅ 处理完成，数据已缓存")

                        # 检查标注状态
                        if bg_processor.labeled_entities:
                            total_entities = len(bg_processor.current_file_entities) if bg_processor.current_file_entities else 0
                            labeled_entities = len(bg_processor.labeled_entities)

                            if labeled_entities == total_entities and total_entities > 0:
                                self.file_status[file_name]['annotation_status'] = 'completed'
                            elif labeled_entities > 0:
                                self.file_status[file_name]['annotation_status'] = 'incomplete'
                    else:
                        # 处理失败
                        self.file_status[file_name]['processing_status'] = 'unprocessed'
                        print(f"    ❌ 处理失败")

                    # 后台处理：只更新内部状态，不更新界面
                    print(f"    📋 后台文件状态更新: {file_name} -> {'已完成' if success else '处理失败'}")

                except Exception as e:
                    print(f"后台处理文件 {file_name} 失败: {e}")
                    import traceback
                    traceback.print_exc()

                    # 更新状态为处理失败
                    self.file_status[file_name]['processing_status'] = 'unprocessed'

                    # 后台处理：只更新内部状态，不更新界面
                    print(f"    📋 后台文件状态更新: {file_name} -> 处理失败")

                # 短暂休息避免占用过多资源
                time.sleep(0.1)

            # 检查是否所有文件都已处理完成
            self._check_all_files_completed()

        # 启动后台线程
        self.should_stop_background = False
        threading.Thread(target=background_worker, daemon=True).start()

    def _process_file_background_safe(self, processor, file_path):
        """安全的后台文件处理方法（真实CAD处理，不触发界面更新）"""
        try:
            print(f"    🔄 开始后台CAD处理: {os.path.basename(file_path)}")

            # 调用真实的CAD处理逻辑（回调已在创建处理器时禁用）
            success = processor.process_single_file(file_path)

            if success:
                print(f"    ✅ 后台CAD文件处理成功")

                # 确保处理器有必要的数据结构
                if not hasattr(processor, 'groups_info') or not processor.groups_info:
                    processor._update_groups_info()

                # 验证处理结果
                entities_count = len(processor.current_file_entities) if processor.current_file_entities else 0
                groups_count = len(processor.all_groups) if processor.all_groups else 0

                print(f"    📊 后台处理结果: {entities_count}个实体, {groups_count}个组")

                return True
            else:
                print(f"    ❌ 后台CAD文件处理失败")
                return False

        except Exception as e:
            print(f"    ❌ 后台处理异常: {e}")
            import traceback
            traceback.print_exc()
            return False

    def on_status_update(self, status_type, data):
        """状态更新回调（重写以支持文件管理，从根源解决重复调用）"""
        # 不调用父类方法，而是重新实现，避免重复调用
        self._handle_status_update_clean(status_type, data)

    def _smart_update_group_list(self, reason="unknown"):
        """智能组列表更新（从逻辑层面避免重复调用）"""
        try:
            # 检查是否正在更新中
            if self._update_state['group_list_updating']:
                print(f"⏸️ 跳过重复的组列表更新请求: {reason}")
                return

            # 标记正在更新
            self._update_state['group_list_updating'] = True
            self._update_state['group_list_update_needed'] = False

            try:
                # 执行实际的更新
                self.update_group_list()
                print(f"✅ 组列表更新完成: {reason}")
            finally:
                # 确保更新标记被清除
                self._update_state['group_list_updating'] = False

        except Exception as e:
            print(f"智能组列表更新失败: {e}")
            # 确保更新标记被清除
            self._update_state['group_list_updating'] = False

    def _handle_status_update_clean(self, status_type, data):
        """干净的状态更新处理（从逻辑层面避免重复调用）"""
        # 初始化更新状态管理
        if not hasattr(self, '_update_state'):
            self._update_state = {
                'group_list_update_needed': False,
                'group_list_updating': False,
                'current_batch_id': None
            }

        # 为这次状态更新批次生成唯一ID
        import time
        batch_id = f"{status_type}_{int(time.time() * 1000)}"

        # 只对重要状态输出日志，减少冗长输出
        important_statuses = ["manual_group", "group_labeled", "completed", "manual_complete", "stopped"]
        if status_type in important_statuses:
            print(f"🔄 处理状态更新: {status_type}")

        # 🎨 通知显示控制器数据变化
        self._notify_display_controller_change(status_type, data)

        # 检查当前是否在处理显示文件（关键修复）
        is_processing_display_file = self._is_processing_display_file()

        # 标记是否需要更新组列表（只在处理显示文件时才更新界面）
        need_update_group_list = False

        # 基础状态更新
        if status_type == "info":
            self.status_var.set(data)

        elif status_type == "error":
            self.status_var.set(f"错误: {data}")

        elif status_type == "status":
            self.status_var.set(data)

        elif status_type == "file_start":
            file_index, total_files, filename = data
            self.status_var.set(f"正在处理文件 {file_index}/{total_files}: {filename}")

        elif status_type == "file_complete":
            file_index, total_files, filename = data
            self.status_var.set(f"文件 {file_index}/{total_files} 处理完成: {filename}")

        elif status_type == "file_error":
            file_index, total_files, filename = data
            self.status_var.set(f"文件 {file_index}/{total_files} 处理失败: {filename}")

        elif status_type == "auto_labeled":
            category, group_count, entity_count = data
            self.status_var.set(f"自动标注 {category}: {group_count}组, {entity_count}个实体")
            # 只有在处理显示文件时才更新界面
            if is_processing_display_file:
                need_update_group_list = True

        elif status_type == "manual_group":
            info = data
            self.status_var.set(f"手动标注 {info['index']}/{info['total']}: {info['entity_count']}个实体")
            # 只有在处理显示文件时才更新界面
            if is_processing_display_file:
                need_update_group_list = True

        elif status_type == "group_labeled":
            _, category_name, entity_count = data
            self.status_var.set(f"已标注为 {category_name}: {entity_count}个实体")
            # 只有在处理显示文件时才更新界面
            if is_processing_display_file:
                need_update_group_list = True

        elif status_type == "group_skipped":
            self.status_var.set(data)
            # 只有在处理显示文件时才更新界面
            if is_processing_display_file:
                need_update_group_list = True

        elif status_type == "group_relabeled":
            group_index, new_label = data
            if hasattr(self.processor, 'category_mapping') and self.processor.category_mapping:
                category_name = self.processor.category_mapping.get(new_label, new_label)
                self.status_var.set(f"组{group_index} 已重新分类为: {category_name}")
            # 只有在处理显示文件时才更新界面
            if is_processing_display_file:
                need_update_group_list = True

        elif status_type == "auto_jump":
            self.status_var.set(f"自动跳转: {data}")

        elif status_type in ["update_group_list", "force_update_group_list"]:
            # 显式的组列表更新请求 - 只有在处理显示文件时才更新界面
            if is_processing_display_file:
                need_update_group_list = True

        elif status_type == "manual_complete":
            self.status_var.set(data)
            self.start_btn.config(state='normal')
            self.stop_btn.config(state='disabled')

            # 只有在处理显示文件时才更新界面
            if is_processing_display_file:
                # 更新可视化（只一次）
                self._update_completion_visualization()
                need_update_group_list = True

                # 检查是否真的所有组都已标注完成
                if hasattr(self, 'processor') and self.processor:
                    all_completed = self._check_all_groups_completed()
                    if all_completed:
                        print("所有组已标注完成，更新最终状态")
                        self._update_final_group_list()
                        self.status_var.set("所有组已标注完成！")
                        return  # 直接返回，避免重复更新
                    else:
                        print("还有组待标注，更新组列表状态")

        elif status_type == "completed":
            # 防重复处理
            if not hasattr(self, '_last_completed_time'):
                self._last_completed_time = 0

            import time
            current_time = time.time()
            if current_time - self._last_completed_time < 0.5:  # 500ms内的重复调用直接忽略
                return
            self._last_completed_time = current_time

            print(f"🔄 处理状态更新: completed")
            # 只有在处理显示文件时才更新界面
            if is_processing_display_file:
                self.status_var.set(data)
                self.start_btn.config(state='normal')
                self.stop_btn.config(state='disabled')

                # 让processor决定是否显示完成信息
                if self.processor and not self.processor.pending_manual_groups:
                    self.processor._show_completion_message()

                need_update_group_list = True

                # 检查是否真的所有组都已标注完成
                if hasattr(self, 'processor') and self.processor:
                    try:
                        all_completed = self._check_all_groups_completed()
                        if all_completed:
                            print("所有组已标注完成，更新最终状态")
                            self._update_final_completion_state()
                            self.status_var.set("所有组已标注完成！")
                            return  # 直接返回，避免重复更新
                        else:
                            print("还有组待标注，更新组列表状态")
                    except Exception as e:
                        print(f"检查完成状态时出错: {e}")
                        import traceback
                        traceback.print_exc()
                        # 出错时仍然更新组列表，避免界面卡住
            else:
                print("后台文件处理完成，不更新界面")

        elif status_type == "stopped":
            self.status_var.set(data)
            self.start_btn.config(state='normal')
            self.stop_btn.config(state='disabled')

            # 只有在处理显示文件时才更新界面
            if is_processing_display_file:
                # 更新可视化（只一次）
                self._update_stopped_visualization()
                need_update_group_list = True

        # 处理文件管理相关的状态更新
        self._handle_file_management_status(status_type, data)

        # 统一的组列表更新（从逻辑层面避免重复调用）
        if need_update_group_list and is_processing_display_file:
            self._smart_update_group_list("status_update")
        elif need_update_group_list and not is_processing_display_file:
            print("跳过界面更新：当前处理的是后台文件")

    def _is_processing_display_file(self):
        """检查当前是否在处理显示文件（优化：减少重复日志）"""
        if not hasattr(self, 'display_file') or not self.display_file:
            return True  # 如果没有设置显示文件，默认认为是在处理显示文件

        if not hasattr(self, 'processor') or not self.processor:
            return True  # 如果没有处理器，默认认为是在处理显示文件

        # 比较处理器当前文件和显示文件
        processor_file = getattr(self.processor, 'current_file', None)
        if not processor_file:
            return True  # 如果处理器没有当前文件，默认认为是在处理显示文件

        # 标准化文件路径进行比较
        processor_file_name = os.path.basename(processor_file) if processor_file else ""
        display_file_name = os.path.basename(self.display_file) if self.display_file else ""

        is_display = processor_file_name == display_file_name

        # 只在第一次检测到后台处理时输出日志，避免重复
        if not is_display:
            # 使用文件名作为键来跟踪是否已经输出过日志
            log_key = f"background_detected_{processor_file_name}"
            if not hasattr(self, '_background_log_cache'):
                self._background_log_cache = set()

            if log_key not in self._background_log_cache:

                self._background_log_cache.add(log_key)

        return is_display

    def _update_completion_visualization(self):
        """更新完成状态的可视化（避免重复代码）"""
        if self.processor and self.processor.visualizer and self.processor.canvas:
            try:
                # 清空详细视图
                self.processor.visualizer.ax_detail.clear()
                self.processor.visualizer.ax_detail.text(0.5, 0.5, '所有分组已完成',
                                                       ha='center', va='center', transform=self.processor.visualizer.ax_detail.transAxes,
                                                       fontproperties=self.processor.visualizer.chinese_font, fontsize=14)
                self.processor.visualizer.ax_detail.set_title('CAD图像预览 (已完成)', fontsize=12, fontproperties=self.processor.visualizer.chinese_font)

                # 更新全图概览，不显示当前处理组
                self.processor.visualizer.visualize_overview(
                    self.processor.current_file_entities,
                    None,  # 不显示当前处理组
                    self.processor.auto_labeled_entities + self.processor.labeled_entities  # 已标注的实体
                )
                self.processor.visualizer.update_canvas(self.processor.canvas)
                print("✅ 完成状态可视化更新成功")
            except Exception as e:
                print(f"完成时可视化更新失败: {e}")

    def _update_stopped_visualization(self):
        """更新停止状态的可视化（避免重复代码）"""
        if self.processor and self.processor.visualizer and self.processor.canvas:
            try:
                # 清空详细视图
                self.processor.visualizer.ax_detail.clear()
                self.processor.visualizer.ax_detail.text(0.5, 0.5, '处理已停止',
                                                       ha='center', va='center', transform=self.processor.visualizer.ax_detail.transAxes,
                                                       fontproperties=self.processor.visualizer.chinese_font, fontsize=14)
                self.processor.visualizer.ax_detail.set_title('CAD图像预览 (已停止)', fontsize=12, fontproperties=self.processor.visualizer.chinese_font)

                # 更新全图概览，不显示当前处理组
                self.processor.visualizer.visualize_overview(
                    self.processor.current_file_entities,
                    None,  # 不显示当前处理组
                    self.processor.auto_labeled_entities + self.processor.labeled_entities  # 已标注的实体
                )
                self.processor.visualizer.update_canvas(self.processor.canvas)
                print("✅ 停止状态可视化更新成功")
            except Exception as e:
                print(f"停止时可视化更新失败: {e}")

    def _handle_file_management_status(self, status_type, data):
        """处理文件管理相关的状态更新（修复：区分显示文件和后台文件）"""
        # 检查当前是否在处理显示文件
        is_processing_display_file = self._is_processing_display_file()

        if status_type == "completed" and self.current_file:
            file_name = os.path.basename(self.current_file)

            # 更新当前文件状态为已完成
            if file_name in self.file_status:
                self.file_status[file_name]['processing_status'] = 'completed'

                # 检查标注状态
                if self.processor and self.processor.labeled_entities:
                    total_entities = len(self.processor.current_file_entities) if self.processor.current_file_entities else 0
                    labeled_entities = len(self.processor.labeled_entities)

                    if labeled_entities == total_entities and total_entities > 0:
                        self.file_status[file_name]['annotation_status'] = 'completed'
                    elif labeled_entities > 0:
                        self.file_status[file_name]['annotation_status'] = 'incomplete'
                    else:
                        self.file_status[file_name]['annotation_status'] = 'unannotated'

                # 只有当前文件是显示文件时才保存数据（避免界面跳转）
                if is_processing_display_file:
                    self._save_current_file_data()
                    print(f"✅ 保存显示文件数据: {file_name}")

                    # 更新文件选择下拉菜单（不改变当前选择）
                    current_selection = self.file_combo.get()
                    self.update_file_combo()
                    if current_selection and current_selection in self.file_combo['values']:
                        self.file_combo.set(current_selection)
                else:
                    print(f"📝 跳过后台文件界面更新: {file_name}")

        # 处理手动标注完成
        elif status_type == "manual_complete" and self.current_file:
            file_name = os.path.basename(self.current_file)
            if file_name in self.file_status:
                # 检查所有组是否都已标注完成
                if self.processor and hasattr(self.processor, 'groups_info'):
                    total_groups = len(self.processor.groups_info)
                    labeled_groups = sum(1 for group in self.processor.groups_info
                                       if group.get('status') == 'labeled')

                    if labeled_groups == total_groups and total_groups > 0:
                        self.file_status[file_name]['annotation_status'] = 'completed'
                    elif labeled_groups > 0:
                        self.file_status[file_name]['annotation_status'] = 'incomplete'
                    else:
                        self.file_status[file_name]['annotation_status'] = 'unannotated'

                # 只有当前文件是显示文件时才保存数据和更新界面
                if is_processing_display_file:
                    self._save_current_file_data()
                    print(f"✅ 保存显示文件数据: {file_name}")

                    # 更新文件选择下拉菜单（不改变当前选择）
                    current_selection = self.file_combo.get()
                    self.update_file_combo()
                    if current_selection and current_selection in self.file_combo['values']:
                        self.file_combo.set(current_selection)
                else:
                    print(f"📝 跳过后台文件界面更新: {file_name}")

        # 处理其他状态更新
        elif status_type == "processing" and self.current_file:
            file_name = os.path.basename(self.current_file)
            if file_name in self.file_status:
                self.file_status[file_name]['processing_status'] = 'processing'
                # 只有在处理显示文件时才更新界面
                if is_processing_display_file:
                    self.update_file_combo()

        # 处理组标注完成的文件状态更新
        elif status_type == "group_labeled" and self.current_file:
            file_name = os.path.basename(self.current_file)
            if file_name in self.file_status:
                # 🔑 关键修复：立即保存当前文件数据，确保标注结果持久化
                self._save_current_file_data()
                print(f"💾 组标注完成，已保存文件数据: {file_name}")

                # 🔑 关键修复：改进标注进度检查逻辑
                if self.processor and hasattr(self.processor, 'groups_info'):
                    total_groups = len(self.processor.groups_info)
                    # 统计已标注组（包括手动标注和自动标注）
                    labeled_groups = sum(1 for group in self.processor.groups_info
                                       if group.get('status') in ['labeled', 'auto_labeled'])

                    print(f"📊 标注进度检查: {labeled_groups}/{total_groups} 组已标注")

                    if labeled_groups == total_groups and total_groups > 0:
                        self.file_status[file_name]['annotation_status'] = 'completed'
                        print(f"✅ 文件 {file_name} 标注完成")
                    elif labeled_groups > 0:
                        self.file_status[file_name]['annotation_status'] = 'incomplete'
                        print(f"🔄 文件 {file_name} 标注进行中")
                    else:
                        self.file_status[file_name]['annotation_status'] = 'unannotated'

                # 只有在处理显示文件时才更新界面
                if is_processing_display_file:
                    self.update_file_combo()
            elif status_type == "group_labeled":
                _, category_name, entity_count = data
                self.status_var.set(f"已标注为 {category_name}: {entity_count}个实体")
                # 延迟更新组列表，避免频繁调用
                self._schedule_group_list_update("group_labeled")
            elif status_type == "group_skipped":
                self.status_var.set(data)
                # 延迟更新组列表，避免频繁调用
                self._schedule_group_list_update("group_skipped")
            elif status_type == "auto_labeled":
                category, group_count, entity_count = data
                self.status_var.set(f"自动标注 {category}: {group_count}组, {entity_count}个实体")
                # 延迟更新组列表，避免频繁调用
                self._schedule_group_list_update("auto_labeled")
            elif status_type == "group_relabeled":
                group_index, new_label = data
                if hasattr(self.processor, 'category_mapping') and self.processor.category_mapping:
                    category_name = self.processor.category_mapping.get(new_label, new_label)
                    self.status_var.set(f"组{group_index} 已重新分类为: {category_name}")
                # 延迟更新组列表，避免频繁调用
                self._schedule_group_list_update("group_relabeled")

    def stop_processing(self):
        """停止处理（重写以停止后台处理）"""
        # 停止后台处理
        self.should_stop_background = True

        # 调用父类方法
        super().stop_processing()

        # 更新界面状态
        if hasattr(self, 'status_label'):
            self.status_label.config(text="处理已停止", fg="red")

        # 重新启用开始按钮
        if hasattr(self, 'start_btn'):
            self.start_btn.config(state='normal')

        # 禁用停止按钮
        if hasattr(self, 'stop_btn'):
            self.stop_btn.config(state='disabled')

    def update_stats(self):
        """更新统计信息（重写以包含文件信息）"""
        if hasattr(super(), 'update_stats'):
            super().update_stats()

        # 添加文件统计信息
        if self.all_files:
            total_files = len(self.all_files)
            completed_files = sum(1 for status in self.file_status.values()
                                if status.get('processing_status') == 'completed')
            annotated_files = sum(1 for status in self.file_status.values()
                                if status.get('annotation_status') == 'completed')

            file_stats = f"文件: {completed_files}/{total_files} 已处理, {annotated_files}/{total_files} 已标注"

            # 更新统计显示
            current_stats = self.stats_var.get()
            if "文件:" not in current_stats:
                self.stats_var.set(f"{file_stats} | {current_stats}")
            else:
                # 替换文件统计部分
                parts = current_stats.split(" | ")
                if len(parts) > 1:
                    self.stats_var.set(f"{file_stats} | {' | '.join(parts[1:])}")
                else:
                    self.stats_var.set(file_stats)

    def auto_fill_walls(self):
        """墙体自动填充功能（改进V2版本 - 处理重叠、间隙和缺失端头）"""
        if not self.processor or not self.processor.current_file_entities:
            messagebox.showwarning("警告", "请先加载CAD文件")
            return
        
        if not self.wall_fill_processor_v2:
            messagebox.showerror("错误", "V2墙体填充处理器不可用")
            return
        
        try:
            # 获取当前文件的所有实体
            entities = self.processor.current_file_entities
            
            # 询问用户选择填充模式
            choice = messagebox.askyesno("选择填充模式", 
                "是否使用交互式填充模式？\n\n"
                "是 - 交互式填充（逐步控制）\n"
                "否 - 自动填充（一键完成）")
            
            if choice:
                # 交互式填充模式
                self._start_interactive_fill(entities)
            else:
                # 自动填充模式
                self._auto_fill_walls_impl(entities)
            
        except Exception as e:
            error_msg = f"墙体填充失败: {str(e)}"
            messagebox.showerror("错误", error_msg)
            self.status_var.set(error_msg)
    
    def _start_interactive_fill(self, entities):
        """启动交互式填充（使用统一逻辑）"""
        try:
            from interactive_wall_fill_window import InteractiveWallFillWindow
            # 传递统一的填充处理方法
            interactive_window = InteractiveWallFillWindow(
                self, entities, self.wall_fill_processor_v2,
                unified_fill_method=self._unified_wall_filling_process
            )
        except ImportError as e:
            messagebox.showerror("错误", f"无法启动交互式填充窗口: {e}")
            # 回退到自动填充
            self._auto_fill_walls_impl(entities)

    def _unified_wall_filling_process(self, entities):
        """统一的墙体识别和填充处理逻辑"""
        try:
            print("开始统一墙体填充处理...")

            # 使用统一处理器
            if not hasattr(self, 'unified_processor'):
                from unified_wall_fill_processor import UnifiedWallFillProcessor
                self.unified_processor = UnifiedWallFillProcessor()

            # 调用统一处理器的主要方法
            filled_groups = self.unified_processor.process_unified_wall_filling(entities)

            if filled_groups:
                # 将结果转换为CAD绘图格式并显示
                self._display_unified_fill_results(filled_groups)

                # 🎨 通知显示控制器墙体填充完成
                if hasattr(self, 'display_controller') and self.display_controller:
                    self.display_controller.notify_data_change(
                        DataChangeType.WALL_FILL,
                        filled_groups,
                        "EnhancedCADAppV2._unified_wall_filling_process",
                        {'wall_fill_processor': getattr(self, 'wall_fill_processor_v2', None)}
                    )

            return filled_groups

        except Exception as e:
            print(f"统一墙体填充处理失败: {e}")
            import traceback
            traceback.print_exc()
            return []

    def _display_unified_fill_results(self, filled_groups):
        """显示统一填充结果"""
        try:
            print("开始显示统一填充结果...")

            # 清除之前的填充结果
            self.canvas.delete("wall_fill")

            # 显示每个填充组
            for i, group_data in enumerate(filled_groups):
                if group_data.get('is_cavity', False):
                    # 显示空腔（白色填充）
                    if group_data.get('cavities'):
                        for cavity_polygon in group_data['cavities']:
                            self._draw_polygon_on_canvas(cavity_polygon, 'white', f"cavity_{i}")
                            print(f"  显示空腔 {i+1}")
                else:
                    # 显示墙体填充（正常颜色）
                    if group_data.get('fill_polygons'):
                        for fill_polygon in group_data['fill_polygons']:
                            self._draw_polygon_on_canvas(fill_polygon, 'lightblue', f"wall_fill_{i}")
                            print(f"  显示墙体填充 {i+1}")

            print(f"统一填充结果显示完成：{len(filled_groups)} 个组")

        except Exception as e:
            print(f"显示统一填充结果失败: {e}")
            import traceback
            traceback.print_exc()

    def _draw_polygon_on_canvas(self, polygon, fill_color, tag):
        """在画布上绘制多边形"""
        try:
            if not polygon or not hasattr(polygon, 'exterior'):
                return

            # 获取多边形的外轮廓坐标
            coords = list(polygon.exterior.coords)

            # 转换为画布坐标
            canvas_coords = []
            for x, y in coords:
                canvas_x, canvas_y = self._world_to_canvas(x, y)
                canvas_coords.extend([canvas_x, canvas_y])

            # 在画布上绘制多边形
            if len(canvas_coords) >= 6:  # 至少3个点
                self.canvas.create_polygon(
                    canvas_coords,
                    fill=fill_color,
                    outline='blue',
                    width=1,
                    tags=("wall_fill", tag)
                )

        except Exception as e:
            print(f"绘制多边形失败: {e}")


    
    def _auto_fill_walls_impl(self, entities):
        """统一的自动填充实现"""
        try:
            # 使用统一的墙体识别和填充逻辑
            filled_groups = self._unified_wall_filling_process(entities)

            if not filled_groups:
                messagebox.showinfo("提示", "未找到可填充的墙体组")
                return



            # 保存填充结果到实例变量
            self.current_wall_fills = filled_groups
            self.current_wall_fill_processor = self.wall_fill_processor_v2

            # 更新可视化
            if self.visualizer:
                self._update_visualization_with_fills_v2(filled_groups)
            
            # 统计信息
            total_fill_polygons = sum(len(fg['fill_polygons']) for fg in filled_groups)
            total_cavities = sum(len(fg['cavities']) for fg in filled_groups)
            
            messagebox.showinfo("成功", 
                f"已完成 {len(filled_groups)} 个墙体组的改进V2填充（处理重叠、间隙和缺失端头）\n"
                f"填充区域: {total_fill_polygons} 个\n"
                f"空腔区域: {total_cavities} 个\n"
                f"点击'保存填充'将其应用到全图概览")
            
        except Exception as e:
            error_msg = f"自动填充失败: {str(e)}"
            messagebox.showerror("错误", error_msg)
            self.status_var.set(error_msg)
    
    def save_wall_fills(self):
        """保存完整V2墙体填充到全图概览"""
        if not hasattr(self, 'current_wall_fills') or not self.current_wall_fills:
            messagebox.showwarning("警告", "请先执行完整V2墙体自动填充")
            return

        try:
            # 🗄️ 将墙体填充数据存储到缓存
            if self.overview_cache and DataType:
                wall_fill_data = {
                    'fills': self.current_wall_fills,
                    'processor': getattr(self, 'wall_fill_processor_v2', None),
                    'timestamp': time.time()
                }

                # 检查数据是否发生变化
                if self.overview_cache.has_data_changed(DataType.WALL_FILL, wall_fill_data):
                    self.overview_cache.store_data(
                        DataType.WALL_FILL,
                        wall_fill_data,
                        metadata={'operation': 'save_wall_fills'}
                    )
                    print("🏗️ 墙体填充数据已存储到缓存")
                else:
                    print("📋 墙体填充数据无变化，跳过存储")

            # 更新全图概览显示完整V2填充
            if self.visualizer and self.processor:
                self._update_overview_with_fills()

            # 检查是否有未分类的实体组
            if self.processor and self.processor.has_unlabeled_groups():
                next_unlabeled_group = self.processor.get_next_unlabeled_group()
                if next_unlabeled_group:
                    # 跳转到第一个未分类的实体组
                    self.processor.jump_to_group(next_unlabeled_group)
                    messagebox.showinfo("提示", f"完整V2墙体填充已保存到全图概览，已跳转到第 {next_unlabeled_group} 个未分类实体组进行手动分类")
                else:
                    messagebox.showinfo("提示", "完整V2墙体填充已保存到全图概览")
            else:
                messagebox.showinfo("提示", "完整V2墙体填充已保存到全图概览，所有实体组已分类完成")

        except Exception as e:
            error_msg = f"保存完整V2填充失败: {str(e)}"
            messagebox.showerror("错误", error_msg)
            self.status_var.set(error_msg)
    
    def _update_visualization_with_fills_v2(self, filled_groups):
        """更新可视化以显示完整V2填充（仅外轮廓）"""
        if not self.visualizer:
            return
        
        try:
            # 清除当前图形
            self.visualizer.ax_detail.clear()
            
            # 绘制原始实体
            if self.processor and self.processor.current_file_entities:
                for entity in self.processor.current_file_entities:
                    self.visualizer._draw_entity(entity, '#000000', 1, 1.0, self.visualizer.ax_detail)
            
            # 使用完整V2处理器创建patches
            if hasattr(self.wall_fill_processor_v2, 'create_fill_patches'):
                patches_list = self.wall_fill_processor_v2.create_fill_patches(filled_groups)
                
                # 添加所有patches到图形
                for patch in patches_list:
                    self.visualizer.ax_detail.add_patch(patch)
            
            # 更新画布
            self.visualizer.ax_detail.set_title('完整V2墙体填充预览（仅外轮廓）', fontsize=12, fontproperties=self.visualizer.chinese_font)
            
            # 检查canvas是否存在并更新
            if hasattr(self, 'canvas') and self.canvas:
                self.canvas.draw()
            elif hasattr(self.visualizer, 'canvas') and self.visualizer.canvas:
                self.visualizer.canvas.draw()
            else:
                # 如果找不到canvas，尝试通过visualizer更新
                if hasattr(self.visualizer, 'update_canvas'):
                    self.visualizer.update_canvas(None)
            
        except Exception as e:
            print(f"更新完整V2可视化失败: {e}")
    
    def _update_overview_with_fills(self):
        """更新全图概览显示完整V2填充（仅外轮廓）"""
        if not self.visualizer or not self.processor:
            return
        
        try:
            # 设置全局墙体填充数据，确保后续的visualize_overview调用也能显示填充
            if self.current_wall_fills and self.current_wall_fill_processor:
                self.visualizer.set_global_wall_fills(self.current_wall_fills, self.current_wall_fill_processor)
            
            # 更新全图概览，包含完整V2填充效果
            self.visualizer.visualize_overview(
                self.processor.current_file_entities,
                None,  # 不显示当前处理组
                self.processor.auto_labeled_entities + self.processor.labeled_entities,  # 已标注的实体
                wall_fills=self.current_wall_fills,  # 添加完整V2墙体填充
                wall_fill_processor=self.current_wall_fill_processor
            )
            
            # 直接更新canvas（与原始版本保持一致）
            if hasattr(self, 'canvas') and self.canvas:
                self.visualizer.update_canvas(self.canvas)
            else:
                # 如果找不到canvas，尝试通过visualizer更新
                self.visualizer.update_canvas(None)
            
        except Exception as e:
            print(f"更新完整V2全图概览失败: {e}")

    def _create_group_list(self, parent):
        """创建实体组列表区域（恢复Treeview版本）"""
        # 调用父类的方法来创建标准的Treeview组列表
        super()._create_group_list(parent)

    def update_group_list(self):
        """更新组列表显示（多文件处理增强版，干净实现）"""
        try:
            # 🔧 修复：检查处理器状态并尝试恢复
            if not self.processor:
                if hasattr(self, '_processor_reset_count'):
                    self._processor_reset_count += 1
                    import time
                    timestamp = time.strftime("%H:%M:%S", time.localtime())
                    print(f"🔍 [{timestamp}] 处理器重置 #{self._processor_reset_count} - 组列表更新")
                print("⚠️ 没有处理器，尝试恢复...")
                if hasattr(self, 'current_file') and self.current_file:
                    success = self._restore_processor_from_current_file()
                    if not success:
                        print("❌ 处理器恢复失败，跳过组列表更新")
                        return
                else:
                    print("❌ 没有当前文件，跳过组列表更新")
                    return
            
            # 验证处理器数据完整性
            if not self._validate_processor_data_quick():
                print("❌ 处理器数据不完整，跳过组列表更新")
                return

            # 确保组列表显示的是当前显示文件的信息
            display_file_name = ""
            if hasattr(self, 'display_file') and self.display_file:
                display_file_name = os.path.basename(self.display_file)

            current_file_name = ""
            if hasattr(self.processor, 'current_file') and self.processor.current_file:
                current_file_name = os.path.basename(self.processor.current_file)

            # 如果处理器的当前文件不是显示文件，需要特殊处理
            if display_file_name and current_file_name and display_file_name != current_file_name:
                print(f"📋 组列表更新：显示文件({display_file_name}) != 处理器文件({current_file_name})")
                # 检查是否有显示文件的缓存数据
                if display_file_name in self.file_data:
                    print(f"  🔄 使用显示文件的缓存数据更新组列表")
                    self._update_group_list_from_cache(display_file_name)
                    return
                else:
                    print(f"  ⚠️ 显示文件无缓存数据，尝试切换到该文件")
                    # 尝试切换到显示文件
                    try:
                        self.switch_to_file(display_file_name)
                        return
                    except Exception as e:
                        print(f"  ❌ 切换失败，使用处理器数据: {e}")

            # 标记这是一个正常的组列表更新（不是由滚动触发的）
            self._updating_from_scroll = False

            # 调用增强版的组列表更新方法
            self._update_group_list_enhanced()

            # 只在处理重要操作时输出日志
            if current_file_name:
                print(f"📋 组列表更新完成：{current_file_name}")

            # 注意：父类的update_group_list已经包含了自动滚动功能，这里不需要重复调用

        except Exception as e:
            print(f"更新组列表失败: {e}")
            import traceback
            traceback.print_exc()

    def _update_group_list_enhanced(self):
        """增强版组列表更新，支持隐藏组显示"""
        if not self.processor:
            print("⚠️ 处理器不存在，尝试恢复处理器状态")
            # 尝试从当前文件缓存恢复处理器状态
            if hasattr(self, 'current_file') and self.current_file:
                success = self._restore_processor_from_current_file()
                if success:
                    print("✅ 从当前文件恢复处理器状态成功")
                else:
                    print("❌ 无法从当前文件恢复处理器状态，组列表将显示为空")
                    return
            else:
                print("❌ 没有当前文件信息，无法恢复处理器状态")
                return

        # 调用父类的组列表更新方法
        try:
            super().update_group_list()
        except Exception as e:
            print(f"更新组列表失败: {e}")

    def _load_cached_data_and_update_ui(self, file_path):
        """加载缓存数据并更新UI（增强版：完整数据恢复）"""
        try:
            print(f"🔄 开始加载缓存数据: {os.path.basename(file_path)}")
            file_name = os.path.basename(file_path)

            # 从缓存获取数据
            cached_data = self.data_cache.get(file_path)
            if cached_data:
                print(f"  找到缓存数据，开始恢复...")
                data = cached_data  # 定义data变量

                # 设置回调以接收状态更新（确保回调设置）
                if hasattr(self.processor, 'set_callbacks'):
                    self.processor.current_file = file_path

                # 反序列化并修复数据类型
                entities = self._deserialize_data(data.get('entities', []))

                # 🔑 关键修复：兼容两种键名（'groups' 和 'all_groups'）
                all_groups = self._deserialize_data(data.get('all_groups', data.get('groups', [])))

                labeled_entities = self._deserialize_data(data.get('labeled_entities', []))
                dataset = self._deserialize_data(data.get('dataset', []))
                groups_info = self._deserialize_data(data.get('groups_info', []))

                # 修复实体数据中的数值类型
                if entities:
                    entities = [self._fix_entity_data_types(entity) for entity in entities]
                    print(f"  修复了 {len(entities)} 个实体的数据类型")

                # 修复组数据中的数值类型
                if all_groups:
                    fixed_groups = []
                    for group in all_groups:
                        if isinstance(group, list):
                            fixed_group = [self._fix_entity_data_types(entity) for entity in group]
                            fixed_groups.append(fixed_group)
                        else:
                            fixed_groups.append(group)
                    all_groups = fixed_groups
                    print(f"  修复了 {len(all_groups)} 个组的数据类型")

                # 修复已标注实体的数据类型
                if labeled_entities:
                    labeled_entities = [self._fix_entity_data_types(entity) for entity in labeled_entities]
                    print(f"  修复了 {len(labeled_entities)} 个已标注实体的数据类型")

                # 赋值给处理器
                self.processor.current_file_entities = entities
                self.processor.all_groups = all_groups
                self.processor.labeled_entities = labeled_entities
                self.processor.dataset = dataset
                self.processor.groups_info = groups_info

                # 恢复填充状态（关键修复）
                self.group_fill_status = data.get('group_fill_status', {})
                print(f"  恢复填充状态: {len(self.group_fill_status)} 个组已填充")

                # 重置当前组索引，跳转到第一个待处理组
                self.processor.current_group_index = 0

                # 🔑 关键修复：更新待处理手动组列表
                if hasattr(self.processor, '_update_pending_manual_groups'):
                    self.processor._update_pending_manual_groups()
                    print(f"  🔄 更新待处理组列表: {len(getattr(self.processor, 'pending_manual_groups', []))} 个待处理组")

                # 查找第一个未标注的组（增强版，排除自动标注）
                first_unlabeled_index = self._find_first_unlabeled_group()
                if first_unlabeled_index is not None:
                    self.processor.current_group_index = first_unlabeled_index
                    print(f"  🎯 跳转到第一个待处理组: 组{first_unlabeled_index + 1}")

                    # 🔑 关键修复：设置手动标注模式，确保可以选择类别
                    if hasattr(self.processor, 'manual_grouping_mode'):
                        self.processor.manual_grouping_mode = True
                        print(f"  🔧 启用手动标注模式")

                    # 🔑 关键修复：设置当前手动组索引
                    if hasattr(self.processor, 'pending_manual_groups') and self.processor.pending_manual_groups:
                        # 在待处理组中找到当前组的索引
                        current_group = self.processor.all_groups[first_unlabeled_index]
                        try:
                            manual_index = self.processor.pending_manual_groups.index(current_group)
                            self.processor.current_manual_group_index = manual_index
                            print(f"  🔧 设置手动组索引: {manual_index}")
                        except ValueError:
                            self.processor.current_manual_group_index = 0
                            print(f"  🔧 设置手动组索引: 0 (默认)")

                    # 清除缓存标记，确保使用实际数据
                    if hasattr(self.processor, '_cached_groups_info'):
                        delattr(self.processor, '_cached_groups_info')
                    if hasattr(self.processor, '_cached_all_groups'):
                        delattr(self.processor, '_cached_all_groups')
                else:
                    print("  所有组都已标注完成")

                    # 🔑 关键修复：禁用手动标注模式
                    if hasattr(self.processor, 'manual_grouping_mode'):
                        self.processor.manual_grouping_mode = False
                        print(f"  🔧 禁用手动标注模式")

                # 恢复可视化器状态并显示当前组
                if self.processor.visualizer and self.canvas:
                    try:
                        # 显示当前组（这会更新预览图和全图概览）
                        if (hasattr(self.processor, 'all_groups') and
                            self.processor.all_groups and
                            self.processor.current_group_index < len(self.processor.all_groups)):
                            current_group = self.processor.all_groups[self.processor.current_group_index]
                            group_index = self.processor.current_group_index + 1

                            # 调用父类的显示组方法
                            self._show_group(current_group, group_index)
                            print(f"  显示当前组: 组{group_index}")
                        else:
                            # 如果没有组，至少显示全图概览
                            if self.processor.current_file_entities:
                                self.processor.visualizer.visualize_overview(
                                    self.processor.current_file_entities,
                                    None,  # 没有当前组
                                    self.processor.labeled_entities,  # 已标注的实体
                                    processor=self.processor
                                )
                                self.processor.visualizer.update_canvas(self.canvas)
                                print(f"  显示全图概览")

                        print(f"  可视化更新完成")
                    except Exception as e:
                        print(f"  更新可视化失败: {e}")

                # 恢复填充状态
                self.group_fill_status = data.get('group_fill_status', {})

                # 更新界面的所有组件（增强版：完整更新所有界面）
                print(f"  🔄 开始完整界面更新...")

                # 1. 更新组列表和统计信息
                try:
                    self.update_group_list()
                    if hasattr(self, 'update_stats'):
                        self.update_stats()
                    print(f"    ✅ 组列表和统计信息更新完成")
                except Exception as e:
                    print(f"    ⚠️ 组列表更新失败: {e}")
                    import traceback
                    traceback.print_exc()

                # 2. 更新预览图（显示当前组）
                if hasattr(self, 'update_preview'):
                    try:
                        self.update_preview()
                        print(f"    ✅ 预览图更新完成")
                    except Exception as e:
                        print(f"    ⚠️ 预览图更新失败: {e}")

                # 3. 更新文件处理列表（如果存在）
                if hasattr(self, 'update_file_list'):
                    try:
                        self.update_file_list()
                        print(f"    ✅ 文件处理列表更新完成")
                    except Exception as e:
                        print(f"    ⚠️ 文件处理列表更新失败: {e}")

                # 4. 更新所有相关的界面控件
                try:
                    # 更新文件夹路径显示
                    self.folder_var.set(self.current_folder)

                    # 更新当前文件显示
                    if hasattr(self, 'current_file_var'):
                        self.current_file_var.set(file_name)

                    # 更新进度显示
                    if hasattr(self, 'progress_var'):
                        if self.processor and hasattr(self.processor, 'all_groups'):
                            total_groups = len(self.processor.all_groups)
                            labeled_groups = sum(1 for g in self.processor.groups_info
                                               if g.get('status') == 'labeled') if self.processor.groups_info else 0
                            self.progress_var.set(f"{labeled_groups}/{total_groups}")

                    print(f"    ✅ 界面控件更新完成")
                except Exception as e:
                    print(f"    ⚠️ 界面控件更新失败: {e}")

                # 5. 更新状态显示
                status_info = self.file_status.get(file_name, {})
                proc_status = status_info.get('processing_status', 'unknown')
                anno_status = status_info.get('annotation_status', 'unknown')

                # 显示当前组信息
                if (self.processor and hasattr(self.processor, 'all_groups') and
                    self.processor.all_groups and
                    self.processor.current_group_index < len(self.processor.all_groups)):
                    group_num = self.processor.current_group_index + 1
                    total_groups = len(self.processor.all_groups)
                    self.status_var.set(f"已切换到文件: {file_name} (处理: {proc_status}, 标注: {anno_status}) - 组 {group_num}/{total_groups}")
                else:
                    self.status_var.set(f"已切换到文件: {file_name} (处理: {proc_status}, 标注: {anno_status})")

                # 6. 更新文件下拉菜单以反映当前文件
                self.update_file_combo()

                # 7. 刷新画布显示
                if hasattr(self, 'canvas') and self.canvas:
                    try:
                        self.canvas.draw()  # 修复：使用draw()而不是update()
                        print(f"    ✅ 画布刷新完成")
                    except Exception as e:
                        print(f"    ⚠️ 画布刷新失败: {e}")

                print(f"✅ 文件 {file_name} 数据加载和界面更新完成")

            else:
                # 没有缓存，需要重新处理文件
                print(f"  没有找到缓存数据")
                messagebox.showinfo("提示", f"文件 {file_name} 没有缓存数据，需要重新处理")

                # 即使没有缓存，也要更新当前文件显示
                self.update_file_combo()
        except Exception as e:
            print(f"❌ 加载缓存数据失败: {e}")
            import traceback
            traceback.print_exc()

    def _find_first_unlabeled_group(self):
        """查找第一个未标注的组索引（增强版，支持缓存数据）"""
        try:
            if not hasattr(self, 'processor') or not self.processor:
                return None

            # 优先使用缓存数据（如果存在）
            if (hasattr(self.processor, '_cached_groups_info') and
                self.processor._cached_groups_info):

                for i, group_info in enumerate(self.processor._cached_groups_info):
                    status = group_info.get('status', 'unlabeled')
                    # 排除自动标注，只查找真正的未标注组
                    if status in ['unlabeled', 'pending', 'labeling']:
                        print(f"  找到未标注组: 组{i+1} (状态: {status})")
                        return i
                print("  缓存数据中没有找到未标注组")

            # 方法1：通过groups_info查找
            if hasattr(self.processor, 'groups_info') and self.processor.groups_info:

                for i, group_info in enumerate(self.processor.groups_info):
                    status = group_info.get('status', 'unlabeled')
                    # 排除自动标注，只查找真正的未标注组
                    if status in ['unlabeled', 'pending', 'labeling']:
                        print(f"  找到未标注组: 组{i+1} (状态: {status})")
                        return i

            # 方法2：通过all_groups查找未标注的实体
            if hasattr(self.processor, 'all_groups') and self.processor.all_groups:

                for i, group in enumerate(self.processor.all_groups):
                    # 检查组中是否有未标注的实体
                    has_unlabeled = any(not entity.get('label') and not entity.get('auto_labeled', False)
                                      for entity in group)
                    if has_unlabeled:
                        print(f"  找到包含未标注实体的组: 组{i+1}")
                        return i

            # 方法3：通过pending_manual_groups查找
            if hasattr(self.processor, 'pending_manual_groups') and self.processor.pending_manual_groups:

                if self.processor.pending_manual_groups and hasattr(self.processor, 'all_groups'):
                    first_pending_group = self.processor.pending_manual_groups[0]
                    if first_pending_group in self.processor.all_groups:
                        index = self.processor.all_groups.index(first_pending_group)
                        print(f"  找到待处理组: 组{index+1}")
                        return index


            return None

        except Exception as e:
            print(f"查找第一个未标注组失败: {e}")
            return None

    def _perform_complete_ui_update_for_file_switch(self, file_name):
        """文件切换时执行完整的界面更新"""
        try:
            print(f"🔄 执行文件切换的完整界面更新: {file_name}")

            # 1. 更新组列表
            self.update_group_list()
            print("  ✅ 组列表更新完成")

            # 2. 更新详细视图（显示当前组）
            try:
                if (hasattr(self.processor, 'all_groups') and self.processor.all_groups and
                    hasattr(self.processor, 'current_group_index') and
                    self.processor.current_group_index < len(self.processor.all_groups)):

                    current_group = self.processor.all_groups[self.processor.current_group_index]
                    group_index = self.processor.current_group_index + 1

                    # 确保跳转到第一个待标注组
                    first_unlabeled_index = self._find_first_unlabeled_group()
                    if first_unlabeled_index is not None and first_unlabeled_index != self.processor.current_group_index:
                        self.processor.current_group_index = first_unlabeled_index
                        current_group = self.processor.all_groups[first_unlabeled_index]
                        group_index = first_unlabeled_index + 1
                        print(f"  🎯 自动跳转到第一个待标注组: 组{group_index}")

                    self._show_group(current_group, group_index)
                    print(f"  ✅ 详细视图更新完成: 显示组{group_index}")
                else:
                    print(f"  ⚠️ 跳过详细视图更新: 没有有效的组数据")
            except Exception as e:
                print(f"  ❌ 详细视图更新失败: {e}")
                # 继续执行其他更新，不中断整个流程

            # 3. 更新全图概览
            try:
                if (hasattr(self.processor, 'visualizer') and self.processor.visualizer and
                    hasattr(self.processor, 'current_file_entities') and self.processor.current_file_entities):

                    # 获取当前组（可能已经在步骤2中更新过）
                    current_group = None
                    if (hasattr(self.processor, 'all_groups') and self.processor.all_groups and
                        hasattr(self.processor, 'current_group_index') and
                        self.processor.current_group_index < len(self.processor.all_groups)):
                        current_group = self.processor.all_groups[self.processor.current_group_index]

                    # 清理组数据，确保只包含有效实体
                    cleaned_current_group = self._clean_group_data(current_group) if current_group else []

                    self.processor.visualizer.visualize_overview(
                        self.processor.current_file_entities,
                        cleaned_current_group,  # 使用清理后的当前组
                        self.processor.labeled_entities if hasattr(self.processor, 'labeled_entities') else [],
                        processor=self.processor
                    )
                    print("  ✅ 全图概览更新完成")
                else:
                    print(f"  ⚠️ 跳过全图概览更新: 没有有效的可视化器或实体数据")
            except Exception as e:
                print(f"  ❌ 全图概览更新失败: {e}")
                # 继续执行其他更新，不中断整个流程

            # 4. 可视化更新
            if hasattr(self, 'canvas') and self.canvas:
                self.canvas.draw()
                print("  ✅ 可视化更新完成")

            # 5. 更新统计信息
            if hasattr(self, 'update_stats'):
                self.update_stats()
                print("  ✅ 统计信息更新完成")

            # 6. 更新文件下拉菜单
            self.update_file_combo()
            print("  ✅ 文件下拉菜单更新完成")

            # 7. 确保跳转到第一个待标注组
            if hasattr(self.processor, 'jump_to_first_unlabeled_group'):
                try:
                    self.processor.jump_to_first_unlabeled_group()
                    print("  ✅ 自动跳转到第一个待标注组完成")
                except Exception as e:
                    print(f"  ⚠️ 自动跳转失败: {e}")

            print(f"✅ 文件切换的完整界面更新完成: {file_name}")

        except Exception as e:
            print(f"❌ 文件切换界面更新失败: {e}")
            import traceback
            traceback.print_exc()

    def save_all_data(self):
        """保存所有文件的数据"""
        if not self.current_folder:
            messagebox.showwarning("警告", "请先选择文件夹")
            return

        # 检查是否所有文件都已处理完成
        unprocessed_files = []
        for file_name, status in self.file_status.items():
            if status.get('processing_status') != 'completed':
                unprocessed_files.append(file_name)

        if unprocessed_files:
            messagebox.showwarning("警告", f"以下文件尚未处理完成，无法保存：\n{', '.join(unprocessed_files)}")
            return

        # 保存当前文件数据
        self._save_current_file_data()

        # 构建保存数据（确保所有数据都可序列化）
        try:
            save_data = {
                'folder': self.current_folder,
                'files': self.all_files,
                'file_status': self._serialize_data(self.file_status),
                'file_data': self._serialize_data(self.file_data),
                'save_time': datetime.now().isoformat(),
                'version': '1.0'
            }

            # 保存到文件
            save_path = os.path.join(self.current_folder, 'cad_annotation_data.json')
            with open(save_path, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, ensure_ascii=False, indent=2)

            messagebox.showinfo("成功", f"所有数据已保存到：\n{save_path}")
            self.status_var.set("数据保存成功")

        except Exception as e:
            print(f"保存数据详细错误: {e}")
            import traceback
            traceback.print_exc()
            messagebox.showerror("错误", f"保存数据失败：{str(e)}")

    def load_all_data(self):
        """读取所有文件的数据"""
        from tkinter import filedialog

        # 弹出文件选择对话框
        load_path = filedialog.askopenfilename(
            title="选择要读取的数据文件",
            filetypes=[
                ("JSON文件", "*.json"),
                ("所有文件", "*.*")
            ],
            initialdir=self.current_folder if self.current_folder else os.getcwd()
        )

        if not load_path:
            return  # 用户取消了选择

        print(f"🔄 读取数据文件: {load_path}")

        try:
            with open(load_path, 'r', encoding='utf-8') as f:
                save_data = json.load(f)

            # 验证数据版本
            if save_data.get('version') != '1.0':
                messagebox.showwarning("警告", "数据文件版本不兼容")
                return

            print(f"  数据文件验证通过，开始恢复数据...")

            # 恢复数据
            self.current_folder = save_data.get('folder', self.current_folder)
            self.all_files = save_data.get('files', [])
            self.file_status = save_data.get('file_status', {})
            self.file_data = save_data.get('file_data', {})

            print(f"  恢复数据完成:")
            print(f"    文件夹: {self.current_folder}")
            print(f"    文件数: {len(self.all_files)}")
            print(f"    状态数: {len(self.file_status)}")
            print(f"    数据数: {len(self.file_data)}")

            # 更新文件夹路径显示
            self.folder_var.set(self.current_folder)

            # 更新文件下拉菜单
            self.update_file_combo()

            # 选择第一个有数据的文件进行显示
            first_file_with_data = None
            for file_path in self.all_files:
                file_name = os.path.basename(file_path)
                if file_name in self.file_data:
                    first_file_with_data = file_path
                    break

            if first_file_with_data:
                print(f"  加载第一个有数据的文件: {os.path.basename(first_file_with_data)}")
                self._load_file_data(first_file_with_data)

                # 更新状态显示
                file_name = os.path.basename(first_file_with_data)
                status_info = self.file_status.get(file_name, {})
                proc_status = status_info.get('processing_status', 'unknown')
                anno_status = status_info.get('annotation_status', 'unknown')

                self.status_var.set(f"数据读取成功 - 当前文件: {file_name} (处理: {proc_status}, 标注: {anno_status})")
            else:
                print(f"  没有找到有数据的文件")
                self.status_var.set("数据读取成功 - 没有找到处理过的文件")

            # 显示成功消息
            save_time = save_data.get('save_time', '未知')
            file_count = len([f for f in self.file_status.values() if f.get('processing_status') == 'completed'])

            messagebox.showinfo("成功",
                f"数据读取成功！\n"
                f"保存时间：{save_time}\n"
                f"文件夹：{os.path.basename(self.current_folder)}\n"
                f"已处理文件：{file_count} 个\n"
                f"总文件数：{len(self.all_files)} 个")

            print(f"✅ 数据读取完成")

        except Exception as e:
            print(f"❌ 读取数据失败: {e}")
            import traceback
            traceback.print_exc()
            messagebox.showerror("错误", f"读取数据失败：{str(e)}")

    def _start_separated_processing(self):
        """启动新的分离式处理流程"""
        print("🚀 启动分离式多文件处理流程")
        print("=" * 60)

        # 重置处理状态
        self.should_stop_background = False
        self.file_data = {}

        # 阶段1：批量处理所有文件（不更新界面）
        self._batch_process_all_files()

    def _batch_process_all_files(self):
        """批量处理所有文件（不更新任何界面）"""
        print("📋 阶段1：批量处理所有文件（仅显示进度，不更新界面）")

        def batch_worker():
            total_files = len(self.all_files)
            processed_count = 0
            failed_files = []

            for i, file_name in enumerate(self.all_files):
                if self.should_stop_background:
                    print("⏹️ 用户停止处理")
                    break

                # 构建完整文件路径
                file_path = os.path.join(self.current_folder, file_name)

                # 更新进度显示
                progress = (i + 1) / total_files * 100
                self.root.after(0, lambda p=progress, f=file_name, idx=i+1, total=total_files:
                    self._update_batch_progress(p, f, idx, total))

                print(f"\n📄 处理文件 {i+1}/{total_files}: {file_name}")
                print(f"  文件路径: {file_path}")

                try:
                    # 确保文件状态字典中有这个文件的记录
                    if file_name not in self.file_status:
                        print(f"  ⚠️ 文件状态字典中没有 {file_name}，创建新记录")
                        self.file_status[file_name] = {
                            'processing_status': 'unprocessed',
                            'annotation_status': 'unannotated'
                        }

                    # 更新文件状态为处理中
                    self.file_status[file_name]['processing_status'] = 'processing'

                    # 执行文件处理（完全后台，不更新界面）
                    success = self._process_file_completely_background(file_path)

                    if success:
                        self.file_status[file_name]['processing_status'] = 'completed'
                        processed_count += 1
                        print(f"  ✅ 处理成功")
                    else:
                        self.file_status[file_name]['processing_status'] = 'failed'
                        failed_files.append(file_name)
                        print(f"  ❌ 处理失败")

                except Exception as e:
                    print(f"  ❌ 处理异常: {e}")
                    import traceback
                    traceback.print_exc()

                    # 确保文件状态字典中有这个文件的记录
                    if file_name not in self.file_status:
                        self.file_status[file_name] = {
                            'processing_status': 'unprocessed',
                            'annotation_status': 'unannotated'
                        }

                    self.file_status[file_name]['processing_status'] = 'failed'
                    failed_files.append(file_name)

                # 短暂休息避免占用过多资源
                time.sleep(0.05)

            # 阶段1完成，启动阶段2
            if not self.should_stop_background:
                self.root.after(0, lambda: self._complete_batch_processing(processed_count, failed_files))

        # 启动后台线程
        threading.Thread(target=batch_worker, daemon=True).start()

    def _update_batch_progress(self, progress, file_name, current, total):
        """更新批量处理进度（仅更新进度条和状态，不更新文件下拉菜单）"""
        try:
            # 更新进度条
            if hasattr(self, 'progress_bar'):
                self.progress_bar['value'] = progress

            # 更新状态显示
            self.status_var.set(f"批量处理中: {current}/{total} - {file_name}")

            # 🔑 关键修复：处理过程中不更新文件下拉菜单
            # 避免界面跳转和显示错误
            print(f"  📊 进度更新: {progress:.1f}% - {file_name}")

        except Exception as e:
            print(f"更新批量进度失败: {e}")

    def _process_file_completely_background(self, file_path):
        """完全后台处理文件（不更新任何界面）"""
        file_name = os.path.basename(file_path)

        try:
            # 创建独立的后台处理器
            from main_enhanced import EnhancedCADProcessor
            bg_processor = EnhancedCADProcessor(None, None)  # 不传递可视化组件

            # 完全禁用所有回调和界面更新
            bg_processor.status_callback = None
            bg_processor.progress_callback = None
            bg_processor.visualizer = None
            bg_processor.canvas = None
            bg_processor._is_background_processing = True

            # 设置文件路径
            bg_processor.current_file = file_path

            # 执行核心处理逻辑
            success = bg_processor.process_single_file(file_path)

            if success:
                # 保存处理结果到缓存
                self._save_background_processing_result(file_name, bg_processor)
                return True
            else:
                print(f"  处理器返回失败")
                return False

        except Exception as e:
            print(f"  后台处理异常: {e}")
            import traceback
            traceback.print_exc()
            return False

    def _save_background_processing_result(self, file_name, bg_processor):
        """保存后台处理结果到缓存"""
        try:
            # 序列化处理结果
            data = {
                'entities': self._serialize_data(bg_processor.current_file_entities or []),
                'all_groups': self._serialize_data(bg_processor.all_groups or []),  # 🔑 保持all_groups键名
                'groups': self._serialize_data(bg_processor.all_groups or []),      # 🔑 同时保存groups键名以兼容
                'labeled_entities': self._serialize_data(bg_processor.labeled_entities or []),
                'dataset': self._serialize_data(bg_processor.dataset or []),
                'groups_info': self._serialize_data(bg_processor.groups_info or []),
                'group_fill_status': {},  # 初始化为空
                'timestamp': datetime.now().isoformat(),
                'processing_method': 'background_batch'
            }



            # 保存到文件数据缓存
            self.file_data[file_name] = data

            # 检查标注状态
            if bg_processor.labeled_entities:
                total_entities = len(bg_processor.current_file_entities) if bg_processor.current_file_entities else 0
                labeled_entities = len(bg_processor.labeled_entities)

                if labeled_entities == total_entities and total_entities > 0:
                    self.file_status[file_name]['annotation_status'] = 'completed'
                elif labeled_entities > 0:
                    self.file_status[file_name]['annotation_status'] = 'incomplete'

            print(f"  💾 缓存数据已保存")

        except Exception as e:
            print(f"  保存缓存失败: {e}")
            import traceback
            traceback.print_exc()

    def _complete_batch_processing(self, processed_count, failed_files):
        """完成批量处理，启动界面更新阶段"""
        print("\n" + "=" * 60)
        print("📋 阶段1完成：批量处理结果")
        print(f"  ✅ 成功处理: {processed_count} 个文件")
        print(f"  ❌ 处理失败: {len(failed_files)} 个文件")

        if failed_files:
            print("  失败文件列表:")
            for file_name in failed_files:
                print(f"    - {file_name}")

        print("\n📋 阶段2：界面更新和显示")

        # 更新所有文件的缓存写入完成
        print("  💾 所有文件识别内容已写入缓存")

        # 更新文件选择下拉菜单
        self.update_file_combo()
        print("  📋 文件选择下拉菜单已更新")

        # 设置第一个成功处理的文件为当前显示文件
        first_successful_file = None
        for file_name in self.all_files:
            if self.file_status[file_name]['processing_status'] == 'completed':
                first_successful_file = file_name
                break

        if first_successful_file:
            # 切换到第一个文件并更新所有界面
            self._switch_to_first_file_and_update_ui(first_successful_file)
        else:
            # 没有成功处理的文件
            self.status_var.set("所有文件处理失败")
            messagebox.showerror("处理失败", "所有文件都处理失败，请检查文件格式和内容")

        # 重新启用按钮
        self.start_btn.config(state='normal')
        self.stop_btn.config(state='disabled')

        print("✅ 分离式多文件处理流程完成")

    def _switch_to_first_file_and_update_ui(self, file_name):
        """切换到第一个文件并更新所有界面"""
        print(f"🖥️ 切换到第一个文件并更新界面: {file_name}")

        try:
            file_path = os.path.join(self.current_folder, file_name)

            # 设置为当前处理文件和显示文件
            self.current_file = file_path
            self.display_file = file_path

            # 加载文件数据（从缓存）
            self._load_file_data(file_path)
            print("  ✅ 文件数据加载完成")

            # 🔑 关键修复：保存当前组索引，防止被update_group_list修改
            saved_current_group_index = getattr(self.processor, 'current_group_index', None)


            # 更新组列表
            self.update_group_list()
            print("  ✅ 组列表更新完成")

            # 🔑 关键修复：恢复当前组索引
            if saved_current_group_index is not None:
                self.processor.current_group_index = saved_current_group_index


            # 更新详细视图（显示当前组）


            if (hasattr(self.processor, 'all_groups') and self.processor.all_groups and
                hasattr(self.processor, 'current_group_index') and
                self.processor.current_group_index is not None and
                self.processor.current_group_index < len(self.processor.all_groups)):

                current_group = self.processor.all_groups[self.processor.current_group_index]
                group_index = self.processor.current_group_index + 1
                self._show_group(current_group, group_index)
                print(f"  ✅ 详细视图更新完成: 显示组{group_index}")
            else:
                print("  ⚠️ 跳过详细视图更新: 没有有效的组数据")

            # 更新全图概览
            try:
                if (hasattr(self.processor, 'visualizer') and self.processor.visualizer and
                    hasattr(self.processor, 'current_file_entities') and self.processor.current_file_entities):

                    current_group = None
                    current_group_index = None



                    if (hasattr(self.processor, 'all_groups') and self.processor.all_groups and
                        hasattr(self.processor, 'current_group_index') and
                        self.processor.current_group_index is not None and
                        self.processor.current_group_index < len(self.processor.all_groups)):
                        current_group = self.processor.all_groups[self.processor.current_group_index]
                        current_group_index = self.processor.current_group_index + 1  # 🔑 组索引从1开始

                    # 清理组数据，确保只包含有效实体
                    cleaned_current_group = self._clean_group_data(current_group) if current_group else []

                    # 🔑 关键修复：传递组索引参数以显示高亮
                    self.processor.visualizer.visualize_overview(
                        self.processor.current_file_entities,
                        cleaned_current_group,  # 使用清理后的当前组
                        self.processor.labeled_entities or [],
                        processor=self.processor,
                        current_group_index=current_group_index  # 🔑 传递组索引
                    )
                    self.processor.visualizer.update_canvas(self.canvas)
                    print("  ✅ 全图概览更新完成")
                else:
                    print("  ⚠️ 跳过全图概览更新: 没有有效的可视化器或实体数据")
            except Exception as e:
                print(f"  ❌ 全图概览更新失败: {e}")

            # 可视化更新
            if hasattr(self, 'canvas') and self.canvas:
                self.canvas.draw()
                print("  ✅ 可视化更新完成")

            # 更新统计信息
            if hasattr(self, 'update_stats'):
                self.update_stats()
                print("  ✅ 统计信息更新完成")

            # 更新文件下拉菜单并选中当前文件
            self.update_file_combo()
            # 设置下拉菜单选择为当前文件
            for value in self.file_combo['values']:
                if file_name in value:
                    self.file_combo.set(value)
                    break
            print("  ✅ 文件下拉菜单更新完成")

            # 更新状态显示
            status_info = self.file_status.get(file_name, {})
            proc_status = status_info.get('processing_status', 'unknown')
            anno_status = status_info.get('annotation_status', 'unknown')

            if (self.processor and hasattr(self.processor, 'all_groups') and
                self.processor.all_groups and
                self.processor.current_group_index < len(self.processor.all_groups)):
                group_num = self.processor.current_group_index + 1
                total_groups = len(self.processor.all_groups)
                self.status_var.set(f"当前文件: {file_name} (处理: {proc_status}, 标注: {anno_status}) - 组 {group_num}/{total_groups}")
            else:
                self.status_var.set(f"当前文件: {file_name} (处理: {proc_status}, 标注: {anno_status})")

            print(f"✅ 界面更新完成: {file_name}")

        except Exception as e:
            print(f"❌ 切换到第一个文件失败: {e}")
            import traceback
            traceback.print_exc()
            self.status_var.set(f"切换到文件 {file_name} 失败")

    def _scan_folder_files(self):
        """扫描文件夹中的CAD文件"""
        self.all_files = []
        self.file_status = {}

        print(f"开始扫描文件夹: {self.current_folder}")

        # 检查文件夹是否存在
        if not self.current_folder:
            print("❌ 未设置文件夹路径")
            self._show_no_files_dialog()
            return

        if not os.path.exists(self.current_folder):
            print(f"❌ 文件夹不存在: {self.current_folder}")
            self._show_no_files_dialog()
            return

        if not os.path.isdir(self.current_folder):
            print(f"❌ 路径不是目录: {self.current_folder}")
            self._show_no_files_dialog()
            return

        # 支持的文件扩展名
        supported_extensions = ['.dxf', '.dwg']

        try:
            files_in_folder = os.listdir(self.current_folder)
            print(f"📋 文件夹中有 {len(files_in_folder)} 个项目")

            for file_name in files_in_folder:
                file_path = os.path.join(self.current_folder, file_name)
                print(f"  检查: {file_name}")

                if os.path.isfile(file_path):
                    _, ext = os.path.splitext(file_name.lower())
                    print(f"    文件扩展名: '{ext}'")

                    if ext in supported_extensions:
                        # 🔑 关键修复：统一使用文件名而不是完整路径
                        self.all_files.append(file_name)  # 只存储文件名
                        self.file_status[file_name] = {
                            'processing_status': 'unprocessed',
                            'annotation_status': 'unannotated'
                        }
                        print(f"    ✅ 添加CAD文件: {file_name}")
                        print(f"      完整路径: {file_path}")
                    else:
                        print(f"    ❌ 跳过非CAD文件: {file_name}")
                else:
                    print(f"    📁 跳过目录: {file_name}")

        except Exception as e:
            print(f"❌ 扫描文件夹时出错: {e}")
            import traceback
            traceback.print_exc()

        print(f"🎯 扫描结果: 找到 {len(self.all_files)} 个CAD文件")

        # 如果没有找到文件，显示诊断对话框
        if not self.all_files:
            print("⚠️ 未找到CAD文件，显示诊断对话框")
            self._show_no_files_dialog()
        else:
            print(f"✅ 成功找到以下CAD文件:")
            for file_path in self.all_files:
                print(f"    {os.path.basename(file_path)}")

        # 更新文件选择下拉菜单
        self.update_file_combo()

        self.status_var.set(f"找到 {len(self.all_files)} 个CAD文件")

    def _show_no_files_dialog(self):
        """显示未找到文件的详细诊断对话框"""
        # 收集诊断信息
        folder_exists = os.path.exists(self.current_folder)
        is_directory = os.path.isdir(self.current_folder) if folder_exists else False

        all_files = []
        cad_files = []

        if folder_exists and is_directory:
            try:
                all_files = os.listdir(self.current_folder)
                supported_extensions = ['.dxf', '.dwg']

                for file_name in all_files:
                    file_path = os.path.join(self.current_folder, file_name)
                    if os.path.isfile(file_path):
                        _, ext = os.path.splitext(file_name.lower())
                        if ext in supported_extensions:
                            cad_files.append(file_name)
            except:
                pass

        # 构建诊断消息
        message = "未找到CAD文件\n\n"
        message += f"文件夹路径: {self.current_folder}\n"
        message += f"文件夹存在: {'是' if folder_exists else '否'}\n"
        message += f"是目录: {'是' if is_directory else '否'}\n"

        if folder_exists and is_directory:
            message += f"文件夹中共有 {len(all_files)} 个项目\n"

            if all_files:
                message += "\n文件夹内容:\n"
                for file_name in all_files[:10]:  # 最多显示10个文件
                    file_path = os.path.join(self.current_folder, file_name)
                    if os.path.isfile(file_path):
                        _, ext = os.path.splitext(file_name.lower())
                        message += f"  📄 {file_name} (扩展名: {ext})\n"
                    else:
                        message += f"  📁 {file_name}\n"

                if len(all_files) > 10:
                    message += f"  ... 还有 {len(all_files) - 10} 个项目\n"

            if cad_files:
                message += f"\n找到 {len(cad_files)} 个CAD文件:\n"
                for cad_file in cad_files:
                    message += f"  ✅ {cad_file}\n"
            else:
                message += "\n❌ 没有找到 .dxf 或 .dwg 文件\n"

        message += "\n💡 解决建议:\n"
        message += "1. 确认文件夹中包含 .dxf 或 .dwg 文件\n"
        message += "2. 检查文件扩展名是否正确\n"
        message += "3. 确认文件不是隐藏文件\n"
        message += "4. 尝试选择其他文件夹"

        messagebox.showinfo("诊断信息", message)

    def _start_file_processing(self, file_path):
        """开始处理指定文件"""
        file_name = os.path.basename(file_path)

        # 区分处理文件和显示文件
        # current_file: 当前正在处理的文件（可能在后台）
        # display_file: 界面显示的文件（始终是第一个文件）
        processing_file = file_path

        # 严格区分显示文件和处理文件
        is_display_file = hasattr(self, 'display_file') and file_path == self.display_file

        # 只有在处理显示文件时才更新current_file（避免界面跳转）
        if is_display_file:
            self.current_file = file_path  # 更新当前文件用于界面显示
            print(f"🖥️ 更新界面显示文件: {file_name}")
        else:
            print(f"🔄 后台处理文件: {file_name}")

        # 更新文件状态
        self.file_status[file_name]['processing_status'] = 'processing'

        # 更新文件下拉菜单（不改变当前选择）
        current_selection = self.file_combo.get()
        self.update_file_combo()
        if current_selection and current_selection in self.file_combo['values']:
            self.file_combo.set(current_selection)

        # 设置文件夹路径（父类需要）
        self.folder_var.set(self.current_folder)

        # 确保处理器存在并设置回调
        if not self.processor:
            from main_enhanced import EnhancedCADProcessor
            self.processor = EnhancedCADProcessor(self.visualizer, self.canvas)

        # 设置回调以接收状态更新
        self.processor.set_callbacks(self.on_status_update, self.on_progress_update)

        print(f"🔄 开始处理文件: {file_name}")
        print(f"  处理文件: {processing_file}")
        print(f"  显示文件: {getattr(self, 'display_file', '未设置')}")
        print(f"  是否为显示文件: {is_display_file}")

        # 只有在处理显示文件时才调用父类的处理方法（更新界面）
        if is_display_file:
            # 调用父类的处理方法
            super().start_processing()
        else:
            # 后台处理：只处理文件，不更新界面
            self._process_file_in_background(processing_file)

    def _start_background_processing(self):
        """启动后台处理其他文件（增强版：确保界面不受影响）"""
        def background_worker():
            print(f"🔄 开始后台处理 {len(self.all_files) - 1} 个文件...")

            for i, file_path in enumerate(self.all_files[1:], 1):  # 跳过第一个文件（已在前台处理）
                file_name = os.path.basename(file_path)
                print(f"  处理文件 {i}/{len(self.all_files) - 1}: {file_name}")

                # 检查是否应该停止
                if getattr(self, 'should_stop_background', False):
                    print(f"  后台处理被停止")
                    break

                try:
                    # 更新状态为处理中
                    self.file_status[file_name]['processing_status'] = 'processing'

                    # 后台处理：只更新内部状态，不更新界面
                    print(f"    📋 后台文件状态更新: {file_name} -> 处理中")

                    # 创建独立的后台处理器（不影响前台）
                    from main_enhanced import EnhancedCADProcessor
                    bg_processor = EnhancedCADProcessor(None, None)  # 不传递可视化组件

                    # 完全禁用所有回调，避免触发界面更新
                    bg_processor.status_callback = None
                    bg_processor.progress_callback = None

                    # 设置后台处理标记
                    bg_processor._is_background_processing = True

                    # 禁用可视化相关功能
                    bg_processor.visualizer = None
                    bg_processor.canvas = None

                    # 设置文件路径
                    bg_processor.current_file = file_path

                    # 执行后台处理（简化版，不更新界面）
                    print(f"    开始处理...")
                    success = self._process_file_background_safe(bg_processor, file_path)

                    if success:
                        # 保存处理结果（序列化处理）
                        # 🔑 关键修复：同时保存两种键名以确保兼容性
                        groups_data = self._serialize_data(bg_processor.all_groups or [])
                        self.file_data[file_name] = {
                            'entities': self._serialize_data(bg_processor.current_file_entities or []),
                            'groups': groups_data,      # 🔑 兼容旧版本
                            'all_groups': groups_data,  # 🔑 新版本键名
                            'labeled_entities': self._serialize_data(bg_processor.labeled_entities or []),
                            'dataset': self._serialize_data(bg_processor.dataset or []),
                            'groups_info': self._serialize_data(bg_processor.groups_info or []),
                            'group_fill_status': {},
                            'timestamp': datetime.now().isoformat()
                        }

                        # 更新状态为已完成
                        self.file_status[file_name]['processing_status'] = 'completed'
                        print(f"    ✅ 处理完成，数据已缓存")

                        # 检查标注状态
                        if bg_processor.labeled_entities:
                            total_entities = len(bg_processor.current_file_entities) if bg_processor.current_file_entities else 0
                            labeled_entities = len(bg_processor.labeled_entities)

                            if labeled_entities == total_entities and total_entities > 0:
                                self.file_status[file_name]['annotation_status'] = 'completed'
                            elif labeled_entities > 0:
                                self.file_status[file_name]['annotation_status'] = 'incomplete'
                    else:
                        # 处理失败
                        self.file_status[file_name]['processing_status'] = 'unprocessed'
                        print(f"    ❌ 处理失败")

                    # 后台处理：只更新内部状态，不更新界面
                    print(f"    📋 后台文件状态更新: {file_name} -> {'已完成' if success else '处理失败'}")

                except Exception as e:
                    print(f"后台处理文件 {file_name} 失败: {e}")
                    import traceback
                    traceback.print_exc()

                    # 更新状态为处理失败
                    self.file_status[file_name]['processing_status'] = 'unprocessed'

                    # 后台处理：只更新内部状态，不更新界面
                    print(f"    📋 后台文件状态更新: {file_name} -> 处理失败")

                # 短暂休息避免占用过多资源
                time.sleep(0.1)

            # 检查是否所有文件都已处理完成
            self._check_all_files_completed()

        # 启动后台线程
        self.should_stop_background = False
        threading.Thread(target=background_worker, daemon=True).start()

    def _process_file_background_safe(self, processor, file_path):
        """安全的后台文件处理方法（真实CAD处理，不触发界面更新）"""
        try:
            print(f"    🔄 开始后台CAD处理: {os.path.basename(file_path)}")

            # 调用真实的CAD处理逻辑（回调已在创建处理器时禁用）
            success = processor.process_single_file(file_path)

            if success:
                print(f"    ✅ 后台CAD文件处理成功")

                # 确保处理器有必要的数据结构
                if not hasattr(processor, 'groups_info') or not processor.groups_info:
                    processor._update_groups_info()

                # 验证处理结果
                entities_count = len(processor.current_file_entities) if processor.current_file_entities else 0
                groups_count = len(processor.all_groups) if processor.all_groups else 0

                print(f"    📊 后台处理结果: {entities_count}个实体, {groups_count}个组")

                return True
            else:
                print(f"    ❌ 后台CAD文件处理失败")
                return False

        except Exception as e:
            print(f"    ❌ 后台处理异常: {e}")
            import traceback
            traceback.print_exc()
            return False

    def on_status_update(self, status_type, data):
        """状态更新回调（重写以支持文件管理，从根源解决重复调用）"""
        # 不调用父类方法，而是重新实现，避免重复调用
        self._handle_status_update_clean(status_type, data)

    def _smart_update_group_list(self, reason="unknown"):
        """智能组列表更新（从逻辑层面避免重复调用）"""
        try:
            # 检查是否正在更新中
            if self._update_state['group_list_updating']:
                print(f"⏸️ 跳过重复的组列表更新请求: {reason}")
                return

            # 标记正在更新
            self._update_state['group_list_updating'] = True
            self._update_state['group_list_update_needed'] = False

            try:
                # 执行实际的更新
                self.update_group_list()
                print(f"✅ 组列表更新完成: {reason}")
            finally:
                # 确保更新标记被清除
                self._update_state['group_list_updating'] = False

        except Exception as e:
            print(f"智能组列表更新失败: {e}")
            # 确保更新标记被清除
            self._update_state['group_list_updating'] = False

    def _handle_status_update_clean(self, status_type, data):
        """干净的状态更新处理（从逻辑层面避免重复调用）"""
        # 初始化更新状态管理
        if not hasattr(self, '_update_state'):
            self._update_state = {
                'group_list_update_needed': False,
                'group_list_updating': False,
                'current_batch_id': None
            }

        # 为这次状态更新批次生成唯一ID
        import time
        batch_id = f"{status_type}_{int(time.time() * 1000)}"

        # 只对重要状态输出日志，减少冗长输出
        important_statuses = ["manual_group", "group_labeled", "completed", "manual_complete", "stopped"]
        if status_type in important_statuses:
            print(f"🔄 处理状态更新: {status_type}")

        # 🎨 通知显示控制器数据变化
        self._notify_display_controller_change(status_type, data)

        # 检查当前是否在处理显示文件（关键修复）
        is_processing_display_file = self._is_processing_display_file()

        # 标记是否需要更新组列表（只在处理显示文件时才更新界面）
        need_update_group_list = False

        # 基础状态更新
        if status_type == "info":
            self.status_var.set(data)

        elif status_type == "error":
            self.status_var.set(f"错误: {data}")

        elif status_type == "status":
            self.status_var.set(data)

        elif status_type == "file_start":
            file_index, total_files, filename = data
            self.status_var.set(f"正在处理文件 {file_index}/{total_files}: {filename}")

        elif status_type == "file_complete":
            file_index, total_files, filename = data
            self.status_var.set(f"文件 {file_index}/{total_files} 处理完成: {filename}")

        elif status_type == "file_error":
            file_index, total_files, filename = data
            self.status_var.set(f"文件 {file_index}/{total_files} 处理失败: {filename}")

        elif status_type == "auto_labeled":
            category, group_count, entity_count = data
            self.status_var.set(f"自动标注 {category}: {group_count}组, {entity_count}个实体")
            # 只有在处理显示文件时才更新界面
            if is_processing_display_file:
                need_update_group_list = True

        elif status_type == "manual_group":
            info = data
            self.status_var.set(f"手动标注 {info['index']}/{info['total']}: {info['entity_count']}个实体")
            # 只有在处理显示文件时才更新界面
            if is_processing_display_file:
                need_update_group_list = True

        elif status_type == "group_labeled":
            _, category_name, entity_count = data
            self.status_var.set(f"已标注为 {category_name}: {entity_count}个实体")
            # 只有在处理显示文件时才更新界面
            if is_processing_display_file:
                need_update_group_list = True

        elif status_type == "group_skipped":
            self.status_var.set(data)
            # 只有在处理显示文件时才更新界面
            if is_processing_display_file:
                need_update_group_list = True

        elif status_type == "group_relabeled":
            group_index, new_label = data
            if hasattr(self.processor, 'category_mapping') and self.processor.category_mapping:
                category_name = self.processor.category_mapping.get(new_label, new_label)
                self.status_var.set(f"组{group_index} 已重新分类为: {category_name}")
            # 只有在处理显示文件时才更新界面
            if is_processing_display_file:
                need_update_group_list = True

        elif status_type == "auto_jump":
            self.status_var.set(f"自动跳转: {data}")

        elif status_type in ["update_group_list", "force_update_group_list"]:
            # 显式的组列表更新请求 - 只有在处理显示文件时才更新界面
            if is_processing_display_file:
                need_update_group_list = True

        elif status_type == "manual_complete":
            self.status_var.set(data)
            self.start_btn.config(state='normal')
            self.stop_btn.config(state='disabled')

            # 只有在处理显示文件时才更新界面
            if is_processing_display_file:
                # 更新可视化（只一次）
                self._update_completion_visualization()
                need_update_group_list = True

                # 检查是否真的所有组都已标注完成
                if hasattr(self, 'processor') and self.processor:
                    all_completed = self._check_all_groups_completed()
                    if all_completed:
                        print("所有组已标注完成，更新最终状态")
                        self._update_final_group_list()
                        self.status_var.set("所有组已标注完成！")
                        return  # 直接返回，避免重复更新
                    else:
                        print("还有组待标注，更新组列表状态")

        elif status_type == "completed":
            # 防重复处理
            if not hasattr(self, '_last_completed_time'):
                self._last_completed_time = 0

            import time
            current_time = time.time()
            if current_time - self._last_completed_time < 0.5:  # 500ms内的重复调用直接忽略
                return
            self._last_completed_time = current_time

            print(f"🔄 处理状态更新: completed")
            # 只有在处理显示文件时才更新界面
            if is_processing_display_file:
                self.status_var.set(data)
                self.start_btn.config(state='normal')
                self.stop_btn.config(state='disabled')

                # 让processor决定是否显示完成信息
                if self.processor and not self.processor.pending_manual_groups:
                    self.processor._show_completion_message()

                need_update_group_list = True

                # 检查是否真的所有组都已标注完成
                if hasattr(self, 'processor') and self.processor:
                    try:
                        all_completed = self._check_all_groups_completed()
                        if all_completed:
                            print("所有组已标注完成，更新最终状态")
                            self._update_final_completion_state()
                            self.status_var.set("所有组已标注完成！")
                            return  # 直接返回，避免重复更新
                        else:
                            print("还有组待标注，更新组列表状态")
                    except Exception as e:
                        print(f"检查完成状态时出错: {e}")
                        import traceback
                        traceback.print_exc()
                        # 出错时仍然更新组列表，避免界面卡住
            else:
                print("后台文件处理完成，不更新界面")

        elif status_type == "stopped":
            self.status_var.set(data)
            self.start_btn.config(state='normal')
            self.stop_btn.config(state='disabled')

            # 只有在处理显示文件时才更新界面
            if is_processing_display_file:
                # 更新可视化（只一次）
                self._update_stopped_visualization()
                need_update_group_list = True

        # 处理文件管理相关的状态更新
        self._handle_file_management_status(status_type, data)

        # 统一的组列表更新（从逻辑层面避免重复调用）
        if need_update_group_list and is_processing_display_file:
            self._smart_update_group_list("status_update")
        elif need_update_group_list and not is_processing_display_file:
            print("跳过界面更新：当前处理的是后台文件")

    def _is_processing_display_file(self):
        """检查当前是否在处理显示文件（优化：减少重复日志）"""
        if not hasattr(self, 'display_file') or not self.display_file:
            return True  # 如果没有设置显示文件，默认认为是在处理显示文件

        if not hasattr(self, 'processor') or not self.processor:
            return True  # 如果没有处理器，默认认为是在处理显示文件

        # 比较处理器当前文件和显示文件
        processor_file = getattr(self.processor, 'current_file', None)
        if not processor_file:
            return True  # 如果处理器没有当前文件，默认认为是在处理显示文件

        # 标准化文件路径进行比较
        processor_file_name = os.path.basename(processor_file) if processor_file else ""
        display_file_name = os.path.basename(self.display_file) if self.display_file else ""

        is_display = processor_file_name == display_file_name

        # 只在第一次检测到后台处理时输出日志，避免重复
        if not is_display:
            # 使用文件名作为键来跟踪是否已经输出过日志
            log_key = f"background_detected_{processor_file_name}"
            if not hasattr(self, '_background_log_cache'):
                self._background_log_cache = set()

            if log_key not in self._background_log_cache:

                self._background_log_cache.add(log_key)

        return is_display

    def _update_completion_visualization(self):
        """更新完成状态的可视化（避免重复代码）"""
        if self.processor and self.processor.visualizer and self.processor.canvas:
            try:
                # 清空详细视图
                self.processor.visualizer.ax_detail.clear()
                self.processor.visualizer.ax_detail.text(0.5, 0.5, '所有分组已完成',
                                                       ha='center', va='center', transform=self.processor.visualizer.ax_detail.transAxes,
                                                       fontproperties=self.processor.visualizer.chinese_font, fontsize=14)
                self.processor.visualizer.ax_detail.set_title('CAD图像预览 (已完成)', fontsize=12, fontproperties=self.processor.visualizer.chinese_font)

                # 更新全图概览，不显示当前处理组
                self.processor.visualizer.visualize_overview(
                    self.processor.current_file_entities,
                    None,  # 不显示当前处理组
                    self.processor.auto_labeled_entities + self.processor.labeled_entities  # 已标注的实体
                )
                self.processor.visualizer.update_canvas(self.processor.canvas)
                print("✅ 完成状态可视化更新成功")
            except Exception as e:
                print(f"完成时可视化更新失败: {e}")

    def _update_stopped_visualization(self):
        """更新停止状态的可视化（避免重复代码）"""
        if self.processor and self.processor.visualizer and self.processor.canvas:
            try:
                # 清空详细视图
                self.processor.visualizer.ax_detail.clear()
                self.processor.visualizer.ax_detail.text(0.5, 0.5, '处理已停止',
                                                       ha='center', va='center', transform=self.processor.visualizer.ax_detail.transAxes,
                                                       fontproperties=self.processor.visualizer.chinese_font, fontsize=14)
                self.processor.visualizer.ax_detail.set_title('CAD图像预览 (已停止)', fontsize=12, fontproperties=self.processor.visualizer.chinese_font)

                # 更新全图概览，不显示当前处理组
                self.processor.visualizer.visualize_overview(
                    self.processor.current_file_entities,
                    None,  # 不显示当前处理组
                    self.processor.auto_labeled_entities + self.processor.labeled_entities  # 已标注的实体
                )
                self.processor.visualizer.update_canvas(self.processor.canvas)
                print("✅ 停止状态可视化更新成功")
            except Exception as e:
                print(f"停止时可视化更新失败: {e}")

    def _handle_file_management_status(self, status_type, data):
        """处理文件管理相关的状态更新（修复：区分显示文件和后台文件）"""
        # 检查当前是否在处理显示文件
        is_processing_display_file = self._is_processing_display_file()

        if status_type == "completed" and self.current_file:
            file_name = os.path.basename(self.current_file)

            # 更新当前文件状态为已完成
            if file_name in self.file_status:
                self.file_status[file_name]['processing_status'] = 'completed'

                # 检查标注状态
                if self.processor and self.processor.labeled_entities:
                    total_entities = len(self.processor.current_file_entities) if self.processor.current_file_entities else 0
                    labeled_entities = len(self.processor.labeled_entities)

                    if labeled_entities == total_entities and total_entities > 0:
                        self.file_status[file_name]['annotation_status'] = 'completed'
                    elif labeled_entities > 0:
                        self.file_status[file_name]['annotation_status'] = 'incomplete'
                    else:
                        self.file_status[file_name]['annotation_status'] = 'unannotated'

                # 只有当前文件是显示文件时才保存数据（避免界面跳转）
                if is_processing_display_file:
                    self._save_current_file_data()
                    print(f"✅ 保存显示文件数据: {file_name}")

                    # 更新文件选择下拉菜单（不改变当前选择）
                    current_selection = self.file_combo.get()
                    self.update_file_combo()
                    if current_selection and current_selection in self.file_combo['values']:
                        self.file_combo.set(current_selection)
                else:
                    print(f"📝 跳过后台文件界面更新: {file_name}")

        # 处理手动标注完成
        elif status_type == "manual_complete" and self.current_file:
            file_name = os.path.basename(self.current_file)
            if file_name in self.file_status:
                # 检查所有组是否都已标注完成
                if self.processor and hasattr(self.processor, 'groups_info'):
                    total_groups = len(self.processor.groups_info)
                    labeled_groups = sum(1 for group in self.processor.groups_info
                                       if group.get('status') == 'labeled')

                    if labeled_groups == total_groups and total_groups > 0:
                        self.file_status[file_name]['annotation_status'] = 'completed'
                    elif labeled_groups > 0:
                        self.file_status[file_name]['annotation_status'] = 'incomplete'
                    else:
                        self.file_status[file_name]['annotation_status'] = 'unannotated'

                # 只有当前文件是显示文件时才保存数据和更新界面
                if is_processing_display_file:
                    self._save_current_file_data()
                    print(f"✅ 保存显示文件数据: {file_name}")

                    # 更新文件选择下拉菜单（不改变当前选择）
                    current_selection = self.file_combo.get()
                    self.update_file_combo()
                    if current_selection and current_selection in self.file_combo['values']:
                        self.file_combo.set(current_selection)
                else:
                    print(f"📝 跳过后台文件界面更新: {file_name}")

        # 处理其他状态更新
        elif status_type == "processing" and self.current_file:
            file_name = os.path.basename(self.current_file)
            if file_name in self.file_status:
                self.file_status[file_name]['processing_status'] = 'processing'
                # 只有在处理显示文件时才更新界面
                if is_processing_display_file:
                    self.update_file_combo()

        # 处理组标注完成的文件状态更新
        elif status_type == "group_labeled" and self.current_file:
            file_name = os.path.basename(self.current_file)
            if file_name in self.file_status:
                # 🔑 关键修复：立即保存当前文件数据，确保标注结果持久化
                self._save_current_file_data()
                print(f"💾 组标注完成，已保存文件数据: {file_name}")

                # 🔑 关键修复：改进标注进度检查逻辑
                if self.processor and hasattr(self.processor, 'groups_info'):
                    total_groups = len(self.processor.groups_info)
                    # 统计已标注组（包括手动标注和自动标注）
                    labeled_groups = sum(1 for group in self.processor.groups_info
                                       if group.get('status') in ['labeled', 'auto_labeled'])

                    print(f"📊 标注进度检查: {labeled_groups}/{total_groups} 组已标注")

                    if labeled_groups == total_groups and total_groups > 0:
                        self.file_status[file_name]['annotation_status'] = 'completed'
                        print(f"✅ 文件 {file_name} 标注完成")
                    elif labeled_groups > 0:
                        self.file_status[file_name]['annotation_status'] = 'incomplete'
                        print(f"🔄 文件 {file_name} 标注进行中")
                    else:
                        self.file_status[file_name]['annotation_status'] = 'unannotated'

                # 只有在处理显示文件时才更新界面
                if is_processing_display_file:
                    self.update_file_combo()
            elif status_type == "group_labeled":
                _, category_name, entity_count = data
                self.status_var.set(f"已标注为 {category_name}: {entity_count}个实体")
                # 延迟更新组列表，避免频繁调用
                self._schedule_group_list_update("group_labeled")
            elif status_type == "group_skipped":
                self.status_var.set(data)
                # 延迟更新组列表，避免频繁调用
                self._schedule_group_list_update("group_skipped")
            elif status_type == "auto_labeled":
                category, group_count, entity_count = data
                self.status_var.set(f"自动标注 {category}: {group_count}组, {entity_count}个实体")
                # 延迟更新组列表，避免频繁调用
                self._schedule_group_list_update("auto_labeled")
            elif status_type == "group_relabeled":
                group_index, new_label = data
                if hasattr(self.processor, 'category_mapping') and self.processor.category_mapping:
                    category_name = self.processor.category_mapping.get(new_label, new_label)
                    self.status_var.set(f"组{group_index} 已重新分类为: {category_name}")
                # 延迟更新组列表，避免频繁调用
                self._schedule_group_list_update("group_relabeled")

    def stop_processing(self):
        """停止处理（重写以停止后台处理）"""
        # 停止后台处理
        self.should_stop_background = True

        # 调用父类方法
        super().stop_processing()

        # 更新界面状态
        if hasattr(self, 'status_label'):
            self.status_label.config(text="处理已停止", fg="red")

        # 重新启用开始按钮
        if hasattr(self, 'start_btn'):
            self.start_btn.config(state='normal')

        # 禁用停止按钮
        if hasattr(self, 'stop_btn'):
            self.stop_btn.config(state='disabled')

    def update_stats(self):
        """更新统计信息（重写以包含文件信息）"""
        if hasattr(super(), 'update_stats'):
            super().update_stats()

        # 添加文件统计信息
        if self.all_files:
            total_files = len(self.all_files)
            completed_files = sum(1 for status in self.file_status.values()
                                if status.get('processing_status') == 'completed')
            annotated_files = sum(1 for status in self.file_status.values()
                                if status.get('annotation_status') == 'completed')

            file_stats = f"文件: {completed_files}/{total_files} 已处理, {annotated_files}/{total_files} 已标注"

            # 更新统计显示
            current_stats = self.stats_var.get()
            if "文件:" not in current_stats:
                self.stats_var.set(f"{file_stats} | {current_stats}")
            else:
                # 替换文件统计部分
                parts = current_stats.split(" | ")
                if len(parts) > 1:
                    self.stats_var.set(f"{file_stats} | {' | '.join(parts[1:])}")
                else:
                    self.stats_var.set(file_stats)

    def auto_fill_walls(self):
        """墙体自动填充功能（改进V2版本 - 处理重叠、间隙和缺失端头）"""
        if not self.processor or not self.processor.current_file_entities:
            messagebox.showwarning("警告", "请先加载CAD文件")
            return
        
        if not self.wall_fill_processor_v2:
            messagebox.showerror("错误", "V2墙体填充处理器不可用")
            return
        
        try:
            # 获取当前文件的所有实体
            entities = self.processor.current_file_entities
            
            # 询问用户选择填充模式
            choice = messagebox.askyesno("选择填充模式", 
                "是否使用交互式填充模式？\n\n"
                "是 - 交互式填充（逐步控制）\n"
                "否 - 自动填充（一键完成）")
            
            if choice:
                # 交互式填充模式
                self._start_interactive_fill(entities)
            else:
                # 自动填充模式
                self._auto_fill_walls_impl(entities)
            
        except Exception as e:
            error_msg = f"墙体填充失败: {str(e)}"
            messagebox.showerror("错误", error_msg)
            self.status_var.set(error_msg)
    
    def _start_interactive_fill(self, entities):
        """启动交互式填充（使用统一逻辑）"""
        try:
            from interactive_wall_fill_window import InteractiveWallFillWindow
            # 传递统一的填充处理方法
            interactive_window = InteractiveWallFillWindow(
                self, entities, self.wall_fill_processor_v2,
                unified_fill_method=self._unified_wall_filling_process
            )
        except ImportError as e:
            messagebox.showerror("错误", f"无法启动交互式填充窗口: {e}")
            # 回退到自动填充
            self._auto_fill_walls_impl(entities)

    def _unified_wall_filling_process(self, entities):
        """统一的墙体识别和填充处理逻辑"""
        try:
            print("开始统一墙体填充处理...")

            # 使用统一处理器
            if not hasattr(self, 'unified_processor'):
                from unified_wall_fill_processor import UnifiedWallFillProcessor
                self.unified_processor = UnifiedWallFillProcessor()

            # 调用统一处理器的主要方法
            filled_groups = self.unified_processor.process_unified_wall_filling(entities)

            if filled_groups:
                # 将结果转换为CAD绘图格式并显示
                self._display_unified_fill_results(filled_groups)

                # 🎨 通知显示控制器墙体填充完成
                if hasattr(self, 'display_controller') and self.display_controller:
                    self.display_controller.notify_data_change(
                        DataChangeType.WALL_FILL,
                        filled_groups,
                        "EnhancedCADAppV2._unified_wall_filling_process",
                        {'wall_fill_processor': getattr(self, 'wall_fill_processor_v2', None)}
                    )

            return filled_groups

        except Exception as e:
            print(f"统一墙体填充处理失败: {e}")
            import traceback
            traceback.print_exc()
            return []

    def _display_unified_fill_results(self, filled_groups):
        """显示统一填充结果"""
        try:
            print("开始显示统一填充结果...")

            # 清除之前的填充结果
            self.canvas.delete("wall_fill")

            # 显示每个填充组
            for i, group_data in enumerate(filled_groups):
                if group_data.get('is_cavity', False):
                    # 显示空腔（白色填充）
                    if group_data.get('cavities'):
                        for cavity_polygon in group_data['cavities']:
                            self._draw_polygon_on_canvas(cavity_polygon, 'white', f"cavity_{i}")
                            print(f"  显示空腔 {i+1}")
                else:
                    # 显示墙体填充（正常颜色）
                    if group_data.get('fill_polygons'):
                        for fill_polygon in group_data['fill_polygons']:
                            self._draw_polygon_on_canvas(fill_polygon, 'lightblue', f"wall_fill_{i}")
                            print(f"  显示墙体填充 {i+1}")

            print(f"统一填充结果显示完成：{len(filled_groups)} 个组")

        except Exception as e:
            print(f"显示统一填充结果失败: {e}")
            import traceback
            traceback.print_exc()

    def _draw_polygon_on_canvas(self, polygon, fill_color, tag):
        """在画布上绘制多边形"""
        try:
            if not polygon or not hasattr(polygon, 'exterior'):
                return

            # 获取多边形的外轮廓坐标
            coords = list(polygon.exterior.coords)

            # 转换为画布坐标
            canvas_coords = []
            for x, y in coords:
                canvas_x, canvas_y = self._world_to_canvas(x, y)
                canvas_coords.extend([canvas_x, canvas_y])

            # 在画布上绘制多边形
            if len(canvas_coords) >= 6:  # 至少3个点
                self.canvas.create_polygon(
                    canvas_coords,
                    fill=fill_color,
                    outline='blue',
                    width=1,
                    tags=("wall_fill", tag)
                )

        except Exception as e:
            print(f"绘制多边形失败: {e}")


    
    def _auto_fill_walls_impl(self, entities):
        """统一的自动填充实现"""
        try:
            # 使用统一的墙体识别和填充逻辑
            filled_groups = self._unified_wall_filling_process(entities)

            if not filled_groups:
                messagebox.showinfo("提示", "未找到可填充的墙体组")
                return



            # 保存填充结果到实例变量
            self.current_wall_fills = filled_groups
            self.current_wall_fill_processor = self.wall_fill_processor_v2

            # 更新可视化
            if self.visualizer:
                self._update_visualization_with_fills_v2(filled_groups)
            
            # 统计信息
            total_fill_polygons = sum(len(fg['fill_polygons']) for fg in filled_groups)
            total_cavities = sum(len(fg['cavities']) for fg in filled_groups)
            
            messagebox.showinfo("成功", 
                f"已完成 {len(filled_groups)} 个墙体组的改进V2填充（处理重叠、间隙和缺失端头）\n"
                f"填充区域: {total_fill_polygons} 个\n"
                f"空腔区域: {total_cavities} 个\n"
                f"点击'保存填充'将其应用到全图概览")
            
        except Exception as e:
            error_msg = f"自动填充失败: {str(e)}"
            messagebox.showerror("错误", error_msg)
            self.status_var.set(error_msg)
    
    def save_wall_fills(self):
        """保存完整V2墙体填充到全图概览"""
        if not hasattr(self, 'current_wall_fills') or not self.current_wall_fills:
            messagebox.showwarning("警告", "请先执行完整V2墙体自动填充")
            return

        try:
            # 🗄️ 将墙体填充数据存储到缓存
            if self.overview_cache and DataType:
                wall_fill_data = {
                    'fills': self.current_wall_fills,
                    'processor': getattr(self, 'wall_fill_processor_v2', None),
                    'timestamp': time.time()
                }

                # 检查数据是否发生变化
                if self.overview_cache.has_data_changed(DataType.WALL_FILL, wall_fill_data):
                    self.overview_cache.store_data(
                        DataType.WALL_FILL,
                        wall_fill_data,
                        metadata={'operation': 'save_wall_fills'}
                    )
                    print("🏗️ 墙体填充数据已存储到缓存")
                else:
                    print("📋 墙体填充数据无变化，跳过存储")

            # 更新全图概览显示完整V2填充
            if self.visualizer and self.processor:
                self._update_overview_with_fills()

            # 检查是否有未分类的实体组
            if self.processor and self.processor.has_unlabeled_groups():
                next_unlabeled_group = self.processor.get_next_unlabeled_group()
                if next_unlabeled_group:
                    # 跳转到第一个未分类的实体组
                    self.processor.jump_to_group(next_unlabeled_group)
                    messagebox.showinfo("提示", f"完整V2墙体填充已保存到全图概览，已跳转到第 {next_unlabeled_group} 个未分类实体组进行手动分类")
                else:
                    messagebox.showinfo("提示", "完整V2墙体填充已保存到全图概览")
            else:
                messagebox.showinfo("提示", "完整V2墙体填充已保存到全图概览，所有实体组已分类完成")

        except Exception as e:
            error_msg = f"保存完整V2填充失败: {str(e)}"
            messagebox.showerror("错误", error_msg)
            self.status_var.set(error_msg)
    
    def _update_visualization_with_fills_v2(self, filled_groups):
        """更新可视化以显示完整V2填充（仅外轮廓）"""
        if not self.visualizer:
            return
        
        try:
            # 清除当前图形
            self.visualizer.ax_detail.clear()
            
            # 绘制原始实体
            if self.processor and self.processor.current_file_entities:
                for entity in self.processor.current_file_entities:
                    self.visualizer._draw_entity(entity, '#000000', 1, 1.0, self.visualizer.ax_detail)
            
            # 使用完整V2处理器创建patches
            if hasattr(self.wall_fill_processor_v2, 'create_fill_patches'):
                patches_list = self.wall_fill_processor_v2.create_fill_patches(filled_groups)
                
                # 添加所有patches到图形
                for patch in patches_list:
                    self.visualizer.ax_detail.add_patch(patch)
            
            # 更新画布
            self.visualizer.ax_detail.set_title('完整V2墙体填充预览（仅外轮廓）', fontsize=12, fontproperties=self.visualizer.chinese_font)
            
            # 检查canvas是否存在并更新
            if hasattr(self, 'canvas') and self.canvas:
                self.canvas.draw()
            elif hasattr(self.visualizer, 'canvas') and self.visualizer.canvas:
                self.visualizer.canvas.draw()
            else:
                # 如果找不到canvas，尝试通过visualizer更新
                if hasattr(self.visualizer, 'update_canvas'):
                    self.visualizer.update_canvas(None)
            
        except Exception as e:
            print(f"更新完整V2可视化失败: {e}")
    
    def _update_overview_with_fills(self):
        """更新全图概览显示完整V2填充（仅外轮廓）"""
        if not self.visualizer or not self.processor:
            return
        
        try:
            # 设置全局墙体填充数据，确保后续的visualize_overview调用也能显示填充
            if self.current_wall_fills and self.current_wall_fill_processor:
                self.visualizer.set_global_wall_fills(self.current_wall_fills, self.current_wall_fill_processor)
            
            # 更新全图概览，包含完整V2填充效果
            self.visualizer.visualize_overview(
                self.processor.current_file_entities,
                None,  # 不显示当前处理组
                self.processor.auto_labeled_entities + self.processor.labeled_entities,  # 已标注的实体
                wall_fills=self.current_wall_fills,  # 添加完整V2墙体填充
                wall_fill_processor=self.current_wall_fill_processor
            )
            
            # 直接更新canvas（与原始版本保持一致）
            if hasattr(self, 'canvas') and self.canvas:
                self.visualizer.update_canvas(self.canvas)
            else:
                # 如果找不到canvas，尝试通过visualizer更新
                self.visualizer.update_canvas(None)
            
        except Exception as e:
            print(f"更新完整V2全图概览失败: {e}")

    def _create_group_list(self, parent):
        """创建实体组列表区域（恢复Treeview版本）"""
        # 调用父类的方法来创建标准的Treeview组列表
        super()._create_group_list(parent)

    def update_group_list(self):
        """更新组列表显示（多文件处理增强版，干净实现）"""
        try:
            # 🔧 修复：检查处理器状态并尝试恢复
            if not self.processor:
                print("⚠️ 没有处理器，尝试恢复...")
                if hasattr(self, 'current_file') and self.current_file:
                    success = self._restore_processor_from_current_file()
                    if not success:
                        print("❌ 处理器恢复失败，跳过组列表更新")
                        return
                else:
                    print("❌ 没有当前文件，跳过组列表更新")
                    return
            
            # 验证处理器数据完整性
            if not self._validate_processor_data_quick():
                print("❌ 处理器数据不完整，跳过组列表更新")
                return

            # 确保组列表显示的是当前显示文件的信息
            display_file_name = ""
            if hasattr(self, 'display_file') and self.display_file:
                display_file_name = os.path.basename(self.display_file)

            current_file_name = ""
            if hasattr(self.processor, 'current_file') and self.processor.current_file:
                current_file_name = os.path.basename(self.processor.current_file)

            # 如果处理器的当前文件不是显示文件，需要特殊处理
            if display_file_name and current_file_name and display_file_name != current_file_name:
                print(f"📋 组列表更新：显示文件({display_file_name}) != 处理器文件({current_file_name})")
                # 检查是否有显示文件的缓存数据
                if display_file_name in self.file_data:
                    print(f"  🔄 使用显示文件的缓存数据更新组列表")
                    self._update_group_list_from_cache(display_file_name)
                    return
                else:
                    print(f"  ⚠️ 显示文件无缓存数据，尝试切换到该文件")
                    # 尝试切换到显示文件
                    try:
                        self.switch_to_file(display_file_name)
                        return
                    except Exception as e:
                        print(f"  ❌ 切换失败，使用处理器数据: {e}")

            # 标记这是一个正常的组列表更新（不是由滚动触发的）
            self._updating_from_scroll = False

            # 调用增强版的组列表更新方法
            self._update_group_list_enhanced()

            # 只在处理重要操作时输出日志
            if current_file_name:
                print(f"📋 组列表更新完成：{current_file_name}")

            # 注意：父类的update_group_list已经包含了自动滚动功能，这里不需要重复调用

        except Exception as e:
            print(f"更新组列表失败: {e}")
            import traceback
            traceback.print_exc()

    def _update_group_list_enhanced_v2(self):
        """增强版组列表更新V2，支持隐藏组显示"""
        if not self.processor:
            print("⚠️ 处理器不存在，尝试恢复处理器状态")
            # 尝试从当前文件缓存恢复处理器状态
            if hasattr(self, 'current_file') and self.current_file:
                success = self._restore_processor_from_current_file()
                if success:
                    print("✅ 从当前文件恢复处理器状态成功")
                else:
                    print("❌ 无法从当前文件恢复处理器状态，组列表将显示为空")
                    return
            else:
                print("❌ 没有当前文件信息，无法恢复处理器状态")
                return

        # 清空现有列表
        for item in self.group_tree.get_children():
            self.group_tree.delete(item)



    def _validate_processor_data_quick(self):
        """快速验证处理器数据完整性"""
        try:
            if not self.processor:
                return False
            
            # 检查关键数据是否存在
            has_entities = hasattr(self.processor, 'current_file_entities') and self.processor.current_file_entities
            has_groups = hasattr(self.processor, 'all_groups') and self.processor.all_groups
            has_mapping = hasattr(self.processor, 'category_mapping') and self.processor.category_mapping
            
            if has_entities and has_groups and has_mapping:
                print(f"  📊 数据验证通过: 实体={len(self.processor.current_file_entities)}, 组={len(self.processor.all_groups)}")
                return True
            else:
                print(f"  ⚠️ 数据不完整: 实体={has_entities}, 组={has_groups}, 映射={has_mapping}")
                return False
                
        except Exception as e:
            print(f"  ❌ 数据验证失败: {e}")
            return False


    def _monitor_processor_state(self, operation_name):
        """监控处理器状态"""
        try:
            if not self.processor:
                print(f"  ⚠️ {operation_name}: 处理器不存在")
                return False
            
            # 检查关键数据
            entity_count = len(getattr(self.processor, 'current_file_entities', []))
            group_count = len(getattr(self.processor, 'all_groups', []))
            
            if entity_count == 0 and group_count == 0:
                print(f"  ⚠️ {operation_name}: 处理器数据为空")
                return False
            
            print(f"  ✅ {operation_name}: 处理器状态正常 (实体={entity_count}, 组={group_count})")
            return True
            
        except Exception as e:
            print(f"  ❌ {operation_name}: 处理器状态监控失败: {e}")
            return False

    def _validate_processor_data(self):
        """验证处理器数据完整性"""
        try:
            if not self.processor:
                print("❌ 处理器不存在")
                return False
            
            # 检查关键属性
            required_attrs = [
                'current_file_entities', 'all_groups', 'groups_info',
                'auto_labeled_entities', 'labeled_entities'
            ]
            
            missing_attrs = []
            empty_attrs = []
            
            for attr in required_attrs:
                if not hasattr(self.processor, attr):
                    missing_attrs.append(attr)
                elif not getattr(self.processor, attr, []):
                    empty_attrs.append(attr)
            
            if missing_attrs:
                print(f"❌ 缺少属性: {missing_attrs}")
                return False
            
            if empty_attrs:
                print(f"⚠️ 空属性: {empty_attrs}")
            
            print(f"✅ 处理器数据验证通过")
            return True
            
        except Exception as e:
            print(f"❌ 数据验证失败: {e}")
            return False

    def _restore_processor_from_current_file(self):
        """从当前文件恢复处理器状态"""
        try:
            if not hasattr(self, 'current_file') or not self.current_file:
                return False

            # 检查缓存中是否有当前文件的数据
            cache_key = self.current_file
            if cache_key not in self.file_cache:
                print(f"  ❌ 缓存中没有文件数据: {cache_key}")
                return False

            print(f"  🔄 从缓存恢复处理器状态: {cache_key}")

            # 创建新的处理器
            from main_enhanced import EnhancedCADProcessor
            self.processor = EnhancedCADProcessor(self.visualizer, self.canvas)

            # 设置回调
            if hasattr(self, 'on_status_update') and hasattr(self, 'on_progress_update'):
                self.processor.set_callbacks(self.on_status_update, self.on_progress_update)

            # 恢复数据
            data = self.file_cache[cache_key]

            # 恢复基本属性
            self.processor.current_file = self.current_file

            # 恢复实体数据
            if 'entities' in data:
                self.processor.entities = self._deserialize_data(data['entities'])
                self.processor.current_file_entities = self.processor.entities
                print(f"    恢复实体: {len(self.processor.entities)} 个")

            # 恢复组数据
            if 'all_groups' in data or 'groups' in data:
                self.processor.all_groups = self._deserialize_data(
                    data.get('all_groups', data.get('groups', []))
                )
                print(f"    恢复组: {len(self.processor.all_groups)} 个")

            # 恢复标注数据
            if 'auto_labeled_entities' in data:
                self.processor.auto_labeled_entities = self._deserialize_data(data['auto_labeled_entities'])
                print(f"    恢复自动标注实体: {len(self.processor.auto_labeled_entities)} 个")

            if 'labeled_entities' in data:
                self.processor.labeled_entities = self._deserialize_data(data['labeled_entities'])
                print(f"    恢复已标注实体: {len(self.processor.labeled_entities)} 个")

            # 更新组信息
            if hasattr(self.processor, '_update_groups_info'):
                self.processor._update_groups_info()
                print(f"    更新组信息: {len(self.processor.groups_info)} 个")

            # 🔧 修复：验证恢复的数据完整性
            print(f"  📊 数据完整性验证:")
            print(f"    总实体数: {len(getattr(self.processor, 'current_file_entities', []))}")
            print(f"    总组数: {len(getattr(self.processor, 'all_groups', []))}")
            print(f"    组信息数: {len(getattr(self.processor, 'groups_info', []))}")
            print(f"    自动标注实体数: {len(getattr(self.processor, 'auto_labeled_entities', []))}")
            print(f"    已标注实体数: {len(getattr(self.processor, 'labeled_entities', []))}")

            # 检查关键数据是否为空
            if not getattr(self.processor, 'current_file_entities', []):
                print(f"  ⚠️ 警告: current_file_entities 为空")
            if not getattr(self.processor, 'all_groups', []):
                print(f"  ⚠️ 警告: all_groups 为空")

            print(f"  ✅ 处理器状态恢复完成")
            return True

        except Exception as e:
            print(f"  ❌ 恢复处理器状态失败: {e}")
            import traceback
            traceback.print_exc()
            return False

        # 配置隐藏组的标签样式
        self.group_tree.tag_configure('hidden', foreground='#808080', background='#f0f0f0')

        # 获取所有组的信息
        groups_info = self.processor.get_groups_info()

        # 保持原始顺序，不改变排序
        for i, group_info in enumerate(groups_info):
            group_id = f"组{i+1}"
            status = group_info['status']
            group_type = group_info.get('type', '')
            entity_count = group_info['entity_count']

            # 根据状态选择标签和显示文本
            if status == 'auto_labeled':
                status_text = '自动标注'
                tag = 'auto_labeled'
                # 安全获取类型映射
                if hasattr(self.processor, 'category_mapping') and self.processor.category_mapping:
                    type_text = self.processor.category_mapping.get(group_type, group_type)
                else:
                    type_text = group_type
            elif status == 'labeled':
                status_text = '已标注'
                tag = 'labeled'
                # 安全获取类型映射
                if hasattr(self.processor, 'category_mapping') and self.processor.category_mapping:
                    type_text = self.processor.category_mapping.get(group_type, group_type)
                else:
                    type_text = group_type
            elif status == 'relabeled':
                status_text = '重新标注'
                tag = 'relabeled'
                # 安全获取类型映射
                if hasattr(self.processor, 'category_mapping') and self.processor.category_mapping:
                    type_text = self.processor.category_mapping.get(group_type, group_type)
                else:
                    type_text = group_type
            elif status == 'pending':
                status_text = '待处理'
                tag = 'pending'
                type_text = '待标注'
            elif status == 'labeling':
                status_text = '标注中'
                tag = 'labeling'
                type_text = '待标注'
            else:
                status_text = '未标注'
                tag = 'unlabeled'
                type_text = '待标注'

            # 检查是否为隐藏组
            if i in self.hidden_groups:
                status_text = f"[隐藏] {status_text}"
                tag = 'hidden'

            # 确定填充状态
            fill_status = self._get_group_fill_status(i)

            # 确定填充状态的标签
            fill_tag = self._get_fill_tag(i, fill_status)

            # 插入到列表中
            item_id = self.group_tree.insert('', 'end', text=group_id,
                                            values=(status_text, type_text, entity_count, fill_status),
                                            tags=(tag,))

            # 为填充列设置特殊的颜色标记
            self._set_fill_column_color(item_id, i, fill_status)

        # 更新完成后，自动滚动到第一个未标注组（逻辑控制）
        # 只有在不是由滚动触发的更新时才进行滚动
        if not hasattr(self, '_updating_from_scroll') or not self._updating_from_scroll:
            self._scroll_to_first_unlabeled_group()

        # 同时更新右侧的索引图
        self._update_legend_display()



    def _update_group_list_from_cache(self, file_name):
        """从缓存数据更新组列表（多文件处理专用）"""
        try:
            if file_name not in self.file_data:
                print(f"⚠️ 文件 {file_name} 没有缓存数据")
                return

            cached_data = self.file_data[file_name]

            # 清空现有列表
            if hasattr(self, 'group_tree') and self.group_tree:
                for item in self.group_tree.get_children():
                    self.group_tree.delete(item)

            # 从缓存获取组信息
            groups_info = cached_data.get('groups_info', [])
            all_groups = cached_data.get('all_groups', [])

            print(f"📋 从缓存更新组列表：{file_name}，{len(groups_info)}个组")

            # 关键修复：同步处理器状态与缓存数据
            if hasattr(self, 'processor') and self.processor:
                # 临时保存缓存数据到处理器，用于跳转逻辑
                self.processor._cached_groups_info = groups_info
                self.processor._cached_all_groups = all_groups

            # 更新组列表显示（修复：使用与父类相同的格式）
            for i, group_info in enumerate(groups_info):
                group_id = f"组{i+1}"
                status = group_info.get('status', 'unlabeled')
                group_type = group_info.get('type', '')
                entity_count = group_info.get('entity_count', 0)

                # 根据状态选择标签和显示文本（与父类保持一致）
                if status == 'auto_labeled':
                    status_text = '自动标注'
                    tag = 'auto_labeled'
                    # 尝试获取类型映射，如果没有处理器则直接使用原始类型
                    if hasattr(self, 'processor') and self.processor and hasattr(self.processor, 'category_mapping'):
                        type_text = self.processor.category_mapping.get(group_type, group_type)
                    else:
                        type_text = group_type
                elif status == 'labeled':
                    status_text = '已标注'
                    tag = 'labeled'
                    if hasattr(self, 'processor') and self.processor and hasattr(self.processor, 'category_mapping'):
                        type_text = self.processor.category_mapping.get(group_type, group_type)
                    else:
                        type_text = group_type
                elif status == 'relabeled':
                    status_text = '重新标注'
                    tag = 'relabeled'
                    if hasattr(self, 'processor') and self.processor and hasattr(self.processor, 'category_mapping'):
                        type_text = self.processor.category_mapping.get(group_type, group_type)
                    else:
                        type_text = group_type
                elif status == 'pending':
                    status_text = '待处理'
                    tag = 'pending'
                    type_text = '待标注'
                elif status == 'labeling':
                    status_text = '标注中'
                    tag = 'labeling'
                    type_text = '待标注'
                else:
                    status_text = '未标注'
                    tag = 'unlabeled'
                    type_text = '待标注'

                # 获取填充状态
                fill_status = self._get_group_fill_status(i)

                # 插入到TreeView（修复：使用与父类相同的格式）
                item_id = self.group_tree.insert('', 'end', text=group_id,
                                                values=(status_text, type_text, entity_count, fill_status),
                                                tags=(tag,))

                # 为填充列设置特殊的颜色标记（如果父类有这个方法）
                if hasattr(self, '_set_fill_column_color'):
                    self._set_fill_column_color(item_id, i, fill_status)

            print(f"✅ 组列表从缓存更新完成：{file_name}")

            # 关键修复：从缓存数据中查找第一个未标注组并跳转
            self._jump_to_first_unlabeled_from_cache(groups_info, file_name)

        except Exception as e:
            print(f"从缓存更新组列表失败: {e}")
            import traceback
            traceback.print_exc()

    def _jump_to_first_unlabeled_from_cache(self, groups_info, file_name):
        """从缓存数据中查找并跳转到第一个未标注组"""
        try:


            first_unlabeled_index = None

            # 遍历组信息，查找第一个未标注组
            for i, group_info in enumerate(groups_info):
                status = group_info.get('status', 'unlabeled')
                print(f"  组{i+1}: 状态={status}")

                # 检查是否为未标注状态（排除自动标注）
                if status in ['unlabeled', 'pending', 'labeling']:
                    first_unlabeled_index = i
                    print(f"  🎯 找到第一个未标注组: 组{i+1} (状态: {status})")
                    break

            if first_unlabeled_index is not None:
                # 滚动到第一个未标注组
                try:
                    all_items = self.group_tree.get_children()
                    if first_unlabeled_index < len(all_items):
                        target_item = all_items[first_unlabeled_index]

                        # 计算滚动位置：让第一个未标注组显示在第四行
                        target_position = max(0, first_unlabeled_index - 3)
                        if target_position < len(all_items):
                            scroll_item = all_items[target_position]
                            self.group_tree.see(scroll_item)

                        # 选中第一个未标注组
                        self.group_tree.selection_set(target_item)
                        self.group_tree.focus(target_item)

                        print(f"📍 已滚动并选中第一个未标注组: 组{first_unlabeled_index + 1}")
                    else:
                        print(f"⚠️ 组索引超出范围: {first_unlabeled_index} >= {len(all_items)}")

                except Exception as e:
                    print(f"滚动到未标注组失败: {e}")
            else:
                print("📍 所有组都已标注，滚动到组1")
                # 所有组都已标注，滚动到组1
                all_items = self.group_tree.get_children()
                if all_items:
                    self.group_tree.see(all_items[0])
                    self.group_tree.selection_set(all_items[0])
                    self.group_tree.focus(all_items[0])

        except Exception as e:
            print(f"从缓存跳转到未标注组失败: {e}")
            import traceback
            traceback.print_exc()

    def _get_group_fill_status(self, group_index):
        """获取组的填充状态（重写以修复填充按钮逻辑）"""
        # 初始化填充状态字典
        if not hasattr(self, 'group_fill_status'):
            self.group_fill_status = {}

        # 检查组是否已填充
        if group_index in self.group_fill_status and self.group_fill_status[group_index]:
            return "●填充"  # 使用圆点符号表示已填充状态

        # 检查组的状态，确定填充按钮的可用性
        if hasattr(self.processor, 'groups_info') and self.processor.groups_info and group_index < len(self.processor.groups_info):
            group_info = self.processor.groups_info[group_index]
            status = group_info.get('status', 'unlabeled')

            # 只有已标注的组才能填充
            if status in ['labeled', 'auto_labeled', 'relabeled']:
                return "□填充"  # 可填充状态
            else:
                return "▢填充"  # 不可填充状态（待标注、标注中等）

        return "▢填充"  # 默认不可填充状态

    def fill_group(self, group_index):
        """对指定组进行填充（重写以修复索引问题和状态检查）"""
        try:
            # 检查处理器和组数据
            if not self.processor or not hasattr(self.processor, 'all_groups') or not self.processor.all_groups:
                messagebox.showwarning("警告", "没有可用的组数据")
                return

            # 检查组索引有效性
            if group_index < 0 or group_index >= len(self.processor.all_groups):
                messagebox.showwarning("警告", f"无效的组索引: {group_index}")
                return

            group = self.processor.all_groups[group_index]
            if not group:
                messagebox.showwarning("警告", "组为空")
                return

            # 检查组的标注状态
            if hasattr(self.processor, 'groups_info') and self.processor.groups_info and group_index < len(self.processor.groups_info):
                group_info = self.processor.groups_info[group_index]
                status = group_info.get('status', 'unlabeled')

                # 只允许已标注的组进行填充
                if status not in ['labeled', 'auto_labeled', 'relabeled']:
                    status_text = {
                        'unlabeled': '未标注',
                        'labeling': '标注中',
                        'pending': '待处理'
                    }.get(status, '未知状态')
                    messagebox.showwarning("警告", f"只能对已标注的组进行填充\n当前组状态: {status_text}")
                    return

            # 检查是否已填充，如果是则询问是否清除
            if hasattr(self, 'group_fill_status') and group_index in self.group_fill_status and self.group_fill_status[group_index]:
                result = messagebox.askyesno("确认", f"组 {group_index + 1} 已填充，是否清除填充？")
                if result:
                    # 清除填充
                    del self.group_fill_status[group_index]
                    self.update_group_list()
                    # 更新可视化显示
                    if hasattr(self, 'visualizer') and self.visualizer:
                        self._update_visualization_after_fill_change()
                    messagebox.showinfo("成功", f"组 {group_index + 1} 的填充已清除")
                return

            # 执行填充逻辑
            success = self._perform_group_fill_v2(group, group_index)

            if success:
                # 初始化填充状态字典
                if not hasattr(self, 'group_fill_status'):
                    self.group_fill_status = {}

                # 更新填充状态
                self.group_fill_status[group_index] = True

                # 🎨 通知显示控制器组填充完成
                if hasattr(self, 'display_controller') and self.display_controller:
                    fill_data = getattr(self, 'group_fill_results', {}).get(group_index, {})
                    self.display_controller.notify_data_change(
                        DataChangeType.GROUP_FILL,
                        {group_index: fill_data},
                        "EnhancedCADAppV2.fill_group"
                    )

                # 更新组列表显示
                self.update_group_list()

                # 更新可视化显示
                if hasattr(self, 'visualizer') and self.visualizer:
                    self._update_visualization_after_fill_change()

                messagebox.showinfo("成功", f"组 {group_index + 1} 填充完成")
            else:
                messagebox.showwarning("失败", f"组 {group_index + 1} 无法填充")

        except Exception as e:
            print(f"填充组失败: {e}")
            import traceback
            traceback.print_exc()
            messagebox.showerror("错误", f"填充组失败: {e}")

    def _perform_group_fill_v2(self, group, group_index):
        """执行组填充的具体逻辑（V2版本 - 借鉴墙体自动填充的轮廓识别逻辑）"""
        try:
            print(f"🔧 开始对组 {group_index + 1} 进行高级轮廓识别填充...")

            # 使用V2墙体填充处理器进行填充
            if self.wall_fill_processor_v2:
                # 使用改进的外轮廓识别方法（借鉴墙体自动填充逻辑）
                outer_contour = self.wall_fill_processor_v2._identify_outer_contour_improved(group)

                if not outer_contour:
                    print(f"❌ 组 {group_index + 1} 无法识别外轮廓")
                    return False

                print(f"✅ 组 {group_index + 1} 成功识别外轮廓，面积: {outer_contour.area:.2f}")

                # 识别空腔（如果有其他实体可用）
                cavities = []
                if hasattr(self.processor, 'current_file_entities') and self.processor.current_file_entities:
                    # 从所有实体中识别空腔
                    cavities = self.wall_fill_processor_v2._identify_cavities_from_all_entities(
                        group, outer_contour, self.processor.current_file_entities
                    )
                    print(f"🔍 组 {group_index + 1} 识别到 {len(cavities)} 个空腔")

                # 创建填充结果
                fill_result = {
                    'fill_polygons': [outer_contour],
                    'cavities': cavities
                }

                # 保存填充结果到组填充状态
                if not hasattr(self, 'group_fill_results'):
                    self.group_fill_results = {}

                self.group_fill_results[group_index] = {
                    'wall_group': group,
                    'fill_polygons': fill_result['fill_polygons'],
                    'cavities': fill_result['cavities']
                }

                print(f"✅ 组 {group_index + 1} 填充成功，生成 {len(fill_result['fill_polygons'])} 个填充多边形，{len(cavities)} 个空腔")
                return True
            else:
                print("❌ V2墙体填充处理器不可用")
                return False

        except Exception as e:
            print(f"❌ 执行V2填充失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def _update_visualization_after_fill_change(self):
        """填充状态改变后更新可视化"""
        try:
            # 重新显示当前组以更新可视化
            if (hasattr(self.processor, 'all_groups') and
                self.processor.all_groups and
                hasattr(self.processor, 'current_group_index') and
                self.processor.current_group_index < len(self.processor.all_groups)):

                current_group = self.processor.all_groups[self.processor.current_group_index]
                group_index = self.processor.current_group_index + 1

                # 调用父类的显示组方法
                self._show_group(current_group, group_index)

        except Exception as e:
            print(f"更新可视化失败: {e}")

    def _update_visualization_with_group_fills(self):
        """更新可视化以显示组填充结果"""
        try:
            if not self.visualizer or not hasattr(self, 'group_fill_results'):
                return

            # 清除当前图形
            self.visualizer.ax_detail.clear()

            # 绘制原始实体
            if self.processor and self.processor.current_file_entities:
                for entity in self.processor.current_file_entities:
                    self.visualizer._draw_entity(entity, '#000000', 1, 1.0, self.visualizer.ax_detail)

            # 绘制组填充结果
            for group_index, fill_result in self.group_fill_results.items():
                # 绘制填充多边形
                for polygon in fill_result.get('fill_polygons', []):
                    if polygon and hasattr(polygon, 'exterior'):
                        # 创建填充patch
                        from matplotlib.patches import Polygon as MPLPolygon
                        coords = list(polygon.exterior.coords)
                        patch = MPLPolygon(coords, facecolor='lightblue', edgecolor='blue', alpha=0.3)
                        self.visualizer.ax_detail.add_patch(patch)

                # 绘制空腔
                for cavity in fill_result.get('cavities', []):
                    if cavity and hasattr(cavity, 'exterior'):
                        coords = list(cavity.exterior.coords)
                        patch = MPLPolygon(coords, facecolor='white', edgecolor='red', alpha=0.8)
                        self.visualizer.ax_detail.add_patch(patch)

            # 更新显示
            self.visualizer.ax_detail.set_aspect('equal')
            self.visualizer.canvas_detail.draw()

        except Exception as e:
            print(f"更新组填充可视化失败: {e}")
            import traceback
            traceback.print_exc()


    def _refresh_complete_view(self):
        """刷新完整视图，显示所有组"""
        try:
            print("🔄 刷新完整视图...")
            
            if not self.processor or not hasattr(self.processor, 'current_file_entities'):
                print("  ⚠️ 处理器或实体数据不存在")
                return False
            
            current_file_entities = getattr(self.processor, 'current_file_entities', [])
            auto_labeled_entities = getattr(self.processor, 'auto_labeled_entities', [])
            labeled_entities = getattr(self.processor, 'labeled_entities', [])
            all_groups = getattr(self.processor, 'all_groups', [])
            
            print(f"  📊 数据统计:")
            print(f"    总实体: {len(current_file_entities)}")
            print(f"    总组数: {len(all_groups)}")
            print(f"    已标注实体: {len(auto_labeled_entities + labeled_entities)}")
            
            if not current_file_entities:
                print("  ⚠️ 没有实体数据")
                return False
            
            # 使用可视化器显示完整视图
            if (hasattr(self.processor, 'visualizer') and self.processor.visualizer and
                hasattr(self, 'canvas') and self.canvas):
                
                # 显示全图概览（不高亮任何特定组）
                self.processor.visualizer.visualize_overview(
                    current_file_entities,  # 所有实体
                    None,  # 不高亮特定组
                    auto_labeled_entities + labeled_entities,  # 已标注实体
                    processor=self.processor  # 处理器信息
                )
                
                # 刷新画布
                self.processor.visualizer.update_canvas(self.canvas)
                
                print("  ✅ 完整视图刷新成功")
                return True
            else:
                print("  ⚠️ 可视化器或画布未初始化")
                return False
                
        except Exception as e:
            print(f"  ❌ 完整视图刷新失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def _show_group(self, group, group_index=None):
        """显示指定组（修复缺失方法，支持后台处理检查）"""
        try:
            # 检查是否在后台处理中，如果是则跳过界面更新
            if hasattr(self.processor, '_is_background_processing') and self.processor._is_background_processing:
                print(f"  跳过后台处理的界面更新: 组{group_index if group_index else '未知'}")
                return

            # 检查当前是否在处理显示文件
            is_display_file = self._is_processing_display_file()

            if hasattr(self, 'display_file') and self.display_file:
                print(f"  - 显示文件: {os.path.basename(self.display_file)}")
            if hasattr(self.processor, 'current_file') and self.processor.current_file:
                print(f"  - 处理器文件: {os.path.basename(self.processor.current_file)}")

            if not is_display_file:
                print(f"  跳过后台文件的界面更新: 组{group_index if group_index else '未知'}")
                return

            # 🔑 关键修复：使用处理器的可视化器确保一致性
            if (hasattr(self.processor, 'visualizer') and self.processor.visualizer and
                hasattr(self, 'canvas') and self.canvas):

                # 更新详细视图
                print("更新详细视图...")



                # 清理组数据，确保只包含有效实体
                cleaned_group = self.processor._clean_group_data(group)

                if hasattr(self.processor, 'category_mapping'):
                    self.processor.visualizer.visualize_entity_group(cleaned_group, self.processor.category_mapping)
                else:
                    self.processor.visualizer.visualize_entity_group(cleaned_group, {})

                # 更新全图概览
                print("更新全图概览...")
                
                # 🔧 修复：安全获取实体数据，确保数据不为空
                current_file_entities = getattr(self.processor, 'current_file_entities', [])
                auto_labeled_entities = getattr(self.processor, 'auto_labeled_entities', [])
                labeled_entities = getattr(self.processor, 'labeled_entities', [])
                
                print(f"  数据检查: 总实体={len(current_file_entities)}, 自动标注={len(auto_labeled_entities)}, 已标注={len(labeled_entities)}")
                
                # 🔍 详细调试信息
                if current_file_entities:
                    # 统计实体类型
                    entity_types = {}
                    entity_layers = {}
                    for entity in current_file_entities:
                        if isinstance(entity, dict):
                            entity_type = entity.get('type', 'unknown')
                            entity_layer = entity.get('layer', 'unknown')
                            entity_types[entity_type] = entity_types.get(entity_type, 0) + 1
                            entity_layers[entity_layer] = entity_layers.get(entity_layer, 0) + 1
                    
                    print(f"  📊 实体类型统计: {entity_types}")
                    print(f"  📊 图层统计: {entity_layers}")
                    
                    # 统计组信息
                    all_groups = getattr(self.processor, 'all_groups', [])
                    if all_groups:
                        print(f"  📊 组统计: 共{len(all_groups)}个组")
                        for i, group in enumerate(all_groups[:5]):  # 只显示前5个组
                            group_size = len([e for e in group if isinstance(e, dict)])
                            print(f"    组{i+1}: {group_size}个实体")
                    else:
                        print(f"  ⚠️ 没有组数据")
                
                if current_file_entities:
                    # 🔑 关键修复：显示所有组，高亮当前组
                    print(f"  🌍 显示全图概览: 总实体={len(current_file_entities)}, 当前组实体={len(cleaned_group)}")
                    
                    # 获取所有组信息用于完整显示
                    all_groups = getattr(self.processor, 'all_groups', [])
                    print(f"  📊 所有组数: {len(all_groups)}")
                    
                    self.processor.visualizer.visualize_overview(
                        current_file_entities,  # 显示所有实体
                        cleaned_group,  # 高亮当前组
                        auto_labeled_entities + labeled_entities,  # 已标注实体
                        processor=self.processor,  # 传递处理器以获取组信息
                        current_group_index=group_index  # 当前组索引用于高亮
                    )
                    print(f"  ✅ 全图概览更新完成")
                else:
                    print("  ⚠️ 没有实体数据，跳过全图概览更新")

                # 刷新画布
                self.processor.visualizer.update_canvas(self.canvas)
                print("可视化更新成功")

                print(f"  显示当前组: 组{group_index if group_index is not None else '未知'}")
            else:
                print("⚠️ 可视化器或画布未初始化")

        except Exception as e:
            print(f"显示组失败: {e}")
            import traceback
            traceback.print_exc()

    def _clear_group_highlight(self):
        """清除组列表中的高亮显示（问题1修复）"""
        try:
            if hasattr(self, 'group_tree') and self.group_tree:
                # 清除所有项目的选择状态
                for item in self.group_tree.get_children():
                    self.group_tree.selection_remove(item)

                # 清除详细视图中的当前组显示
                if hasattr(self, 'visualizer') and self.visualizer:
                    self.visualizer.ax_detail.clear()
                    self.visualizer.ax_detail.text(0.5, 0.5, '所有组已标注完成',
                                                  ha='center', va='center', fontsize=14, color='green')
                    self.visualizer.ax_detail.set_xlim(0, 1)
                    self.visualizer.ax_detail.set_ylim(0, 1)
                    self.visualizer.ax_detail.axis('off')

                    # 更新画布
                    if hasattr(self, 'canvas') and self.canvas:
                        self.canvas.draw()

                print("✅ 已清除组列表高亮显示")

        except Exception as e:
            print(f"清除组高亮显示失败: {e}")

    def _update_final_completion_state(self):
        """更新最终完成状态（安全版本，修复IndexError）"""
        try:
            print("🎯 开始更新最终完成状态")

            # 1. 清除所有高亮显示
            self._clear_all_highlights_safe()

            # 2. 更新处理器状态
            self._update_processor_completion_state()

            # 3. 更新可视化显示（无高亮）
            self._update_completion_visualization()

            # 4. 更新组列表显示（所有组显示为已标注，无高亮）
            self._update_completion_group_list()

            print("✅ 最终完成状态更新成功")

        except Exception as e:
            print(f"更新最终完成状态失败: {e}")
            import traceback
            traceback.print_exc()
            # 即使出错，也要尝试基本的清理
            try:
                self._clear_all_highlights_safe()
                if hasattr(self, 'update_group_list'):
                    self.update_group_list()
            except:
                pass

    def _clear_all_highlights_safe(self):
        """安全地清除所有高亮显示"""
        try:
            if hasattr(self, 'group_tree') and self.group_tree:
                # 清除TreeView选择
                try:
                    self.group_tree.selection_remove(self.group_tree.selection())
                except:
                    pass
                print("已清除组列表高亮显示")
        except Exception as e:
            print(f"清除高亮显示失败: {e}")

    def _update_processor_completion_state(self):
        """更新处理器的完成状态"""
        try:
            if hasattr(self, 'processor') and self.processor:
                # 设置为非手动标注模式
                if hasattr(self.processor, 'manual_grouping_mode'):
                    self.processor.manual_grouping_mode = False

                # 清除当前组索引
                if hasattr(self.processor, 'current_group_index'):
                    self.processor.current_group_index = -1

                # 清空待处理组列表
                if hasattr(self.processor, 'pending_manual_groups'):
                    self.processor.pending_manual_groups = []

                print("处理器完成状态已更新")
        except Exception as e:
            print(f"更新处理器状态失败: {e}")

    def _update_completion_visualization(self):
        """更新完成时的可视化显示（无高亮）"""
        try:
            if (hasattr(self, 'processor') and self.processor and
                hasattr(self.processor, 'visualizer') and self.processor.visualizer and
                hasattr(self, 'canvas') and self.canvas):

                # 清空详细视图，显示完成信息
                self.processor.visualizer.ax_detail.clear()
                self.processor.visualizer.ax_detail.text(
                    0.5, 0.5, '所有分组已完成',
                    ha='center', va='center',
                    transform=self.processor.visualizer.ax_detail.transAxes,
                    fontproperties=self.processor.visualizer.chinese_font,
                    fontsize=14, color='green'
                )
                self.processor.visualizer.ax_detail.set_title(
                    'CAD图像预览 (已完成)',
                    fontsize=12,
                    fontproperties=self.processor.visualizer.chinese_font
                )

                # 更新全图概览，不显示任何高亮组
                if hasattr(self.processor, 'current_file_entities') and self.processor.current_file_entities:
                    self.processor.visualizer.visualize_overview(
                        self.processor.current_file_entities,
                        None,  # 不显示当前处理组（无高亮）
                        (getattr(self.processor, 'auto_labeled_entities', []) +
                         getattr(self.processor, 'labeled_entities', [])),  # 已标注的实体
                        processor=self.processor
                    )

                # 更新画布
                self.processor.visualizer.update_canvas(self.canvas)
                print("完成时可视化更新成功")

        except Exception as e:
            print(f"更新完成可视化失败: {e}")

    def _update_completion_group_list(self):
        """更新完成时的组列表显示"""
        try:
            # 强制更新所有组状态为已标注
            if (hasattr(self, 'processor') and self.processor and
                hasattr(self.processor, 'groups_info') and self.processor.groups_info):

                for group_info in self.processor.groups_info:
                    if group_info.get('status') in ['labeling', 'unlabeled']:
                        group_info['status'] = 'labeled'

                print("所有组状态已更新为已标注")

            # 更新组列表显示
            if hasattr(self, 'update_group_list'):
                self.update_group_list()
                print("组列表显示已更新")

        except Exception as e:
            print(f"更新完成组列表失败: {e}")

    def _update_final_group_list(self):
        """更新最终的组列表状态（修复问题2）"""
        try:
            print("更新最终组列表状态")

            # 清除高亮显示
            self._clear_group_highlight()

            # 强制更新所有组的状态为已标注
            if hasattr(self, 'processor') and self.processor and hasattr(self.processor, 'groups_info'):
                updated_count = 0
                for i, group_info in enumerate(self.processor.groups_info):
                    if group_info.get('status') in ['labeling', 'unlabeled']:
                        group_info['status'] = 'labeled'
                        updated_count += 1
                        print(f"强制更新组{i+1}状态为已标注")

                if updated_count > 0:
                    print(f"总共强制更新了 {updated_count} 个组的状态")
                else:
                    print("所有组状态已经是已标注状态")

            # 更新组列表，确保显示所有组的最终状态
            if hasattr(self, 'update_group_list'):
                self.update_group_list()

            # 确保所有组都显示正确的状态
            if hasattr(self, 'group_tree') and self.group_tree:
                # 遍历所有组，确保状态正确显示
                for item in self.group_tree.get_children():
                    try:
                        # 安全获取组信息（修复IndexError）
                        values = self.group_tree.item(item, 'values')
                        if values and isinstance(values, (list, tuple)) and len(values) > 0:
                            # 安全访问第一个元素
                            group_id = values[0] if len(values) > 0 else "未知组"
                            # 确保状态显示正确
                            print(f"最终状态 - {group_id}: {values}")
                        else:
                            print(f"组 {item} 的values为空或无效: {values}")
                    except Exception as e:
                        print(f"更新组 {item} 状态失败: {e}")
                        import traceback
                        traceback.print_exc()

            # 清除当前组索引，避免继续显示高亮
            if hasattr(self, 'processor') and self.processor:
                if hasattr(self.processor, 'current_group_index'):
                    self.processor.current_group_index = -1  # 设置为无效索引
                if hasattr(self.processor, 'manual_grouping_mode'):
                    self.processor.manual_grouping_mode = False

            print("✅ 最终组列表状态更新完成")

        except Exception as e:
            print(f"更新最终组列表失败: {e}")
            import traceback
            traceback.print_exc()

    def _clear_group_highlight(self):
        """清除组列表的高亮显示"""
        try:
            if hasattr(self, 'group_tree') and self.group_tree:
                # 清除所有选择
                self.group_tree.selection_remove(self.group_tree.selection())
                print("已清除组列表高亮显示")
        except Exception as e:
            print(f"清除组列表高亮失败: {e}")

    def _update_group_list_with_highlight(self):
        """更新组列表并保持高亮显示（修复问题2）"""
        try:
            print("更新组列表并保持高亮")

            # 直接更新组列表
            self.update_group_list()

            # 确保当前组保持高亮
            if hasattr(self, 'processor') and self.processor:
                try:
                    current_group_index = getattr(self.processor, 'current_group_index', None)
                    if current_group_index is not None and hasattr(self, 'group_tree') and self.group_tree:
                        # 查找并高亮当前组（修复IndexError）
                        for item in self.group_tree.get_children():
                            try:
                                values = self.group_tree.item(item, 'values')
                                if values and isinstance(values, (list, tuple)) and len(values) > 0:
                                    group_id = values[0]
                                    if group_id == f"组{current_group_index + 1}":
                                        self.group_tree.selection_set(item)
                                        self.group_tree.focus(item)
                                        print(f"保持组 {group_id} 的高亮显示")
                                        break
                                else:
                                    print(f"组 {item} 的values为空或无效: {values}")
                            except Exception as e:
                                print(f"处理组 {item} 高亮失败: {e}")
                                continue
                except Exception as e:
                    print(f"设置组高亮失败: {e}")

            print("✅ 组列表更新完成，保持高亮")

        except Exception as e:
            print(f"更新组列表失败: {e}")
            import traceback
            traceback.print_exc()

    def _get_pending_manual_groups_safe(self):
        """安全获取待处理的手动组（修复IndexError）"""
        try:
            # 检查处理器是否存在
            if not hasattr(self, 'processor') or not self.processor:
                print("处理器不存在，返回空列表")
                return []

            # 检查处理器是否有pending_manual_groups属性
            if hasattr(self.processor, 'pending_manual_groups'):
                pending_groups = self.processor.pending_manual_groups
                if isinstance(pending_groups, list):
                    print(f"从处理器获取到 {len(pending_groups)} 个待处理组")
                    return pending_groups
                else:
                    print("pending_manual_groups不是列表类型")
                    return []

            # 检查处理器是否有get_pending_manual_groups方法
            if hasattr(self.processor, 'get_pending_manual_groups'):
                try:
                    pending_groups = self.processor.get_pending_manual_groups()
                    if isinstance(pending_groups, list):
                        print(f"通过方法获取到 {len(pending_groups)} 个待处理组")
                        return pending_groups
                    else:
                        print("get_pending_manual_groups返回的不是列表")
                        return []
                except Exception as e:
                    print(f"调用get_pending_manual_groups方法失败: {e}")
                    return []

            # 检查处理器是否有_update_pending_manual_groups方法
            if hasattr(self.processor, '_update_pending_manual_groups'):
                try:
                    self.processor._update_pending_manual_groups()
                    if hasattr(self.processor, 'pending_manual_groups'):
                        pending_groups = self.processor.pending_manual_groups
                        if isinstance(pending_groups, list):
                            print(f"更新后获取到 {len(pending_groups)} 个待处理组")
                            return pending_groups
                except Exception as e:
                    print(f"更新待处理组失败: {e}")
                    return []

            # 方法3：通过groups_info检查未标注的组
            if hasattr(self.processor, 'groups_info') and self.processor.groups_info:
                try:
                    unlabeled_groups = []
                    for i, group_info in enumerate(self.processor.groups_info):
                        if group_info.get('status') in ['unlabeled', 'labeling']:
                            unlabeled_groups.append(i)  # 添加组索引

                    print(f"通过groups_info找到 {len(unlabeled_groups)} 个未标注组")
                    return unlabeled_groups
                except Exception as e:
                    print(f"通过groups_info检查未标注组失败: {e}")

            # 方法4：通过all_groups检查未标注的实体
            if hasattr(self.processor, 'all_groups') and self.processor.all_groups:
                try:
                    unlabeled_groups = []
                    for i, group in enumerate(self.processor.all_groups):
                        # 检查组中是否有未标注的实体
                        has_unlabeled = any(not entity.get('label') for entity in group)
                        if has_unlabeled:
                            unlabeled_groups.append(i)

                    print(f"通过all_groups找到 {len(unlabeled_groups)} 个未标注组")
                    return unlabeled_groups
                except Exception as e:
                    print(f"通过all_groups检查未标注组失败: {e}")

            print("无法获取待处理组信息，返回空列表")
            return []

        except Exception as e:
            print(f"安全获取待处理组失败: {e}")
            import traceback
            traceback.print_exc()
            return []

    def _check_all_groups_completed(self):
        """检查是否所有组都已标注完成（修复最后一个组状态问题）"""
        try:
            if not hasattr(self, 'processor') or not self.processor:
                return True  # 没有处理器时认为已完成

            # 首先检查是否还在手动标注模式
            if hasattr(self.processor, 'manual_grouping_mode') and self.processor.manual_grouping_mode:
                print("仍在手动标注模式，未完成")
                return False

            # 检查是否还有待处理的手动组
            if hasattr(self.processor, 'pending_manual_groups') and self.processor.pending_manual_groups:
                # 检查当前索引是否还在范围内
                current_index = getattr(self.processor, 'current_manual_group_index', 0)
                if current_index < len(self.processor.pending_manual_groups):
                    print(f"还有待处理的手动组，当前索引{current_index}，总数{len(self.processor.pending_manual_groups)}")
                    return False

            # 检查groups_info中的状态（更严格的检查）
            if hasattr(self.processor, 'groups_info') and self.processor.groups_info:
                unlabeled_count = 0
                labeling_count = 0
                for i, group_info in enumerate(self.processor.groups_info):
                    status = group_info.get('status', 'unlabeled')
                    if status == 'unlabeled':
                        unlabeled_count += 1
                        print(f"组{i+1}状态为未标注")
                    elif status == 'labeling':
                        labeling_count += 1
                        print(f"组{i+1}状态为标注中")

                if unlabeled_count > 0 or labeling_count > 0:
                    print(f"有{unlabeled_count}个未标注组，{labeling_count}个标注中组，未完成")
                    return False

            # 检查all_groups中是否有未标注的实体
            if hasattr(self.processor, 'all_groups') and self.processor.all_groups:
                for i, group in enumerate(self.processor.all_groups):
                    # 安全检查：只处理字典类型的实体
                    has_unlabeled = any(
                        isinstance(entity, dict) and not entity.get('label')
                        for entity in group
                        if isinstance(entity, dict)
                    )
                    if has_unlabeled:
                        print(f"组{i+1}有未标注实体，未完成")
                        return False

            print("所有组都已标注完成")
            return True

        except Exception as e:
            print(f"检查组完成状态失败: {e}")
            import traceback
            traceback.print_exc()
            return True  # 出错时认为已完成，避免无限循环

    def on_group_double_click(self, event):
        """处理组双击事件（重写以修复IndexError）"""
        try:
            selection = self.group_tree.selection()
            if selection:
                item = selection[0]
                group_id = self.group_tree.item(item, 'text')
                values = self.group_tree.item(item, 'values')

                # 修复IndexError: 安全访问values
                if values and isinstance(values, (list, tuple)) and len(values) > 0:
                    status = values[0]
                else:
                    print(f"警告: 组 {group_id} 的values为空或不存在，values={values}")
                    status = ""

                try:
                    group_index = int(group_id.replace('组', '')) - 1  # 转换为0基索引
                except ValueError:
                    print(f"无法解析组索引: {group_id}")
                    return

                if status in ['已标注', '自动标注', '重新标注']:
                    # 已标注的组，进入重新分类模式
                    if self.processor:
                        self.processor.start_relabel_group(group_index)
                else:
                    # 未标注的组，跳转到该组
                    if self.processor:
                        self.processor.jump_to_group(group_index)

        except Exception as e:
            print(f"处理组双击事件失败: {e}")
            import traceback
            traceback.print_exc()

    def on_group_right_click(self, event):
        """处理组右键点击事件（重写以修复IndexError）"""
        try:
            # 选择被右键点击的项目
            item = self.group_tree.identify_row(event.y)
            if item:
                self.group_tree.selection_set(item)

                # 获取组信息
                group_id = self.group_tree.item(item, 'text')
                values = self.group_tree.item(item, 'values')

                # 修复IndexError: 安全访问values
                if values and isinstance(values, (list, tuple)) and len(values) > 0:
                    status = values[0]
                else:
                    print(f"警告: 组 {group_id} 的values为空或不存在，values={values}")
                    status = ""

                # 创建右键菜单
                context_menu = tk.Menu(self.root, tearoff=0)

                try:
                    group_index = int(group_id.replace('组', '')) - 1  # 转换为0基索引
                except ValueError:
                    print(f"无法解析组索引: {group_id}")
                    return

                # 根据状态添加菜单项
                if status in ['已标注', '自动标注', '重新标注']:
                    context_menu.add_command(label="重新分类",
                                           command=lambda: self.processor.start_relabel_group(group_index) if self.processor else None)
                else:
                    context_menu.add_command(label="跳转到此组",
                                           command=lambda: self.processor.jump_to_group(group_index) if self.processor else None)

                context_menu.add_separator()

                # 显示/隐藏功能
                if group_index in self.hidden_groups:
                    context_menu.add_command(label="显示此组",
                                           command=lambda idx=group_index: self.show_group(idx))
                else:
                    context_menu.add_command(label="不显示此组",
                                           command=lambda idx=group_index: self.hide_group(idx))

                context_menu.add_separator()
                context_menu.add_command(label="查看组详细数据",
                                       command=lambda idx=group_index: self.show_group_details(idx))

                # 显示菜单
                try:
                    context_menu.tk_popup(event.x_root, event.y_root)
                finally:
                    context_menu.grab_release()

        except Exception as e:
            print(f"处理组右键点击事件失败: {e}")
            import traceback
            traceback.print_exc()

    def hide_group(self, group_index):
        """隐藏指定组"""
        try:
            self.hidden_groups.add(group_index)
            print(f"隐藏组{group_index + 1}")

            # 更新组列表显示
            self.update_group_list()

            # 更新可视化
            self._refresh_visualization()

        except Exception as e:
            print(f"隐藏组失败: {e}")

    def show_group(self, group_index):
        """显示指定组"""
        try:
            self.hidden_groups.discard(group_index)
            print(f"显示组{group_index + 1}")

            # 更新组列表显示
            self.update_group_list()

            # 更新可视化
            self._refresh_visualization()

        except Exception as e:
            print(f"显示组失败: {e}")

    def show_group_details(self, group_index):
        """显示组的详细信息"""
        try:
            if not self.processor or not hasattr(self.processor, 'all_groups'):
                messagebox.showwarning("警告", "没有可用的组数据")
                return

            if group_index >= len(self.processor.all_groups):
                messagebox.showwarning("警告", f"组索引超出范围: {group_index}")
                return

            group = self.processor.all_groups[group_index]
            if not group:
                messagebox.showwarning("警告", f"组{group_index + 1}为空")
                return

            # 创建详细信息窗口
            detail_window = tk.Toplevel(self.root)
            detail_window.title(f"组{group_index + 1} 详细数据")
            detail_window.geometry("800x600")
            detail_window.resizable(True, True)

            # 创建主框架
            main_frame = tk.Frame(detail_window)
            main_frame.pack(fill='both', expand=True, padx=10, pady=10)

            # 标题
            title_label = tk.Label(main_frame, text=f"组{group_index + 1} 详细数据",
                                 font=('Arial', 14, 'bold'))
            title_label.pack(pady=(0, 10))

            # 组概要信息
            info_frame = tk.Frame(main_frame)
            info_frame.pack(fill='x', pady=(0, 10))

            tk.Label(info_frame, text=f"实体总数: {len(group)}",
                    font=('Arial', 10)).pack(anchor='w')

            # 统计信息
            layer_stats = {}
            type_stats = {}
            for entity in group:
                layer = entity.get('layer', '未知')
                entity_type = entity.get('type', '未知')
                layer_stats[layer] = layer_stats.get(layer, 0) + 1
                type_stats[entity_type] = type_stats.get(entity_type, 0) + 1

            tk.Label(info_frame, text=f"涉及图层: {len(layer_stats)} 个",
                    font=('Arial', 10)).pack(anchor='w')
            tk.Label(info_frame, text=f"实体类型: {len(type_stats)} 种",
                    font=('Arial', 10)).pack(anchor='w')

            # 创建表格显示实体详细信息
            table_frame = tk.Frame(main_frame)
            table_frame.pack(fill='both', expand=True)

            # 创建Treeview
            columns = ('实体ID', '图层', '线型', '类型', '颜色')
            tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=20)

            # 设置列标题
            for col in columns:
                tree.heading(col, text=col)
                tree.column(col, width=120)

            # 添加滚动条
            scrollbar_v = ttk.Scrollbar(table_frame, orient='vertical', command=tree.yview)
            scrollbar_h = ttk.Scrollbar(table_frame, orient='horizontal', command=tree.xview)
            tree.configure(yscrollcommand=scrollbar_v.set, xscrollcommand=scrollbar_h.set)

            # 布局
            tree.grid(row=0, column=0, sticky='nsew')
            scrollbar_v.grid(row=0, column=1, sticky='ns')
            scrollbar_h.grid(row=1, column=0, sticky='ew')

            table_frame.grid_rowconfigure(0, weight=1)
            table_frame.grid_columnconfigure(0, weight=1)

            # 填充数据
            for i, entity in enumerate(group):
                entity_id = f"E{i+1}"
                layer = entity.get('layer', '未知')
                linetype = entity.get('linetype', '未知')
                entity_type = entity.get('type', '未知')

                # 获取在全图预览中的颜色
                color = self._get_entity_preview_color(entity, group_index)

                tree.insert('', 'end', values=(entity_id, layer, linetype, entity_type, color))

            # 关闭按钮
            close_btn = tk.Button(main_frame, text="关闭", command=detail_window.destroy,
                                font=('Arial', 10), bg='#f0f0f0')
            close_btn.pack(pady=(10, 0))

        except Exception as e:
            print(f"显示组详细信息失败: {e}")
            import traceback
            traceback.print_exc()
            messagebox.showerror("错误", f"显示组详细信息失败: {e}")

    def _get_entity_preview_color(self, entity, group_index):
        """获取实体在全图预览中的颜色"""
        try:
            # 检查组是否隐藏
            if group_index in self.hidden_groups:
                return "隐藏"

            # 获取实体标签
            entity_label = entity.get('label')
            if entity_label == 'None' or entity_label == 'none':
                entity_label = None

            # 判断实体状态
            if entity_label and not entity.get('auto_labeled', False):
                return "绿色(已标注)"
            elif entity.get('auto_labeled', False):
                return "蓝色(自动标注)"
            else:
                return "灰色(未标注)"

        except Exception as e:
            print(f"获取实体颜色失败: {e}")
            return "未知"

    def _refresh_visualization(self):
        """刷新可视化显示"""
        try:
            if self.processor and hasattr(self.processor, 'visualizer') and self.processor.visualizer:
                # 获取当前组
                current_group = None
                current_group_index = None

                if hasattr(self.processor, 'current_group') and self.processor.current_group:
                    current_group = self.processor.current_group
                    if hasattr(self.processor, 'all_groups') and self.processor.all_groups:
                        try:
                            current_group_index = self.processor.all_groups.index(current_group)
                        except ValueError:
                            current_group_index = None

                # 更新全图概览
                if hasattr(self.processor, 'current_file_entities') and self.processor.current_file_entities:
                    labeled_entities = []
                    if hasattr(self.processor, 'auto_labeled_entities'):
                        labeled_entities.extend(self.processor.auto_labeled_entities)
                    if hasattr(self.processor, 'labeled_entities'):
                        labeled_entities.extend(self.processor.labeled_entities)

                    # 清理组数据，确保只包含有效实体
                    cleaned_current_group = self._clean_group_data(current_group) if current_group else []

                    self.processor.visualizer.visualize_overview(
                        self.processor.current_file_entities,
                        cleaned_current_group,  # 使用清理后的当前组
                        labeled_entities,
                        processor=self.processor,
                        current_group_index=current_group_index,
                        hidden_groups=self.hidden_groups  # 传递隐藏组信息
                    )

                    # 更新画布
                    if hasattr(self, 'canvas') and self.canvas:
                        self.processor.visualizer.update_canvas(self.canvas)

        except Exception as e:
            print(f"刷新可视化失败: {e}")



    def _create_visualization(self, parent):
        """创建可视化区域（重新设计为4区域布局）"""
        # 标题
        viz_title = tk.Label(parent, text="CAD实体可视化 - 四区域布局", font=('Arial', 12, 'bold'))
        viz_title.pack(pady=(0, 5))

        # 创建主容器框架，使用grid布局
        main_container = tk.Frame(parent)
        main_container.pack(fill='both', expand=True, padx=5, pady=5)

        # 配置grid权重，使界面能够按比例自动适应 - 增高图像控制窗口
        main_container.grid_rowconfigure(0, weight=1)  # 上排权重1（减少）
        main_container.grid_rowconfigure(1, weight=3)  # 下排权重3（显著增加）
        main_container.grid_columnconfigure(0, weight=1)  # 左列权重1
        main_container.grid_columnconfigure(1, weight=1)  # 右列权重1

        # 区域1：图像预览（左上）
        self.detail_frame = tk.Frame(main_container, relief='ridge', bd=2)
        self.detail_frame.grid(row=0, column=0, sticky='nsew', padx=(0, 2), pady=(0, 2))
        self._create_detail_view(self.detail_frame)

        # 区域2：填充控制（右上）
        self.overview_frame = tk.Frame(main_container, relief='ridge', bd=2)
        self.overview_frame.grid(row=0, column=1, sticky='nsew', padx=(2, 0), pady=(0, 2))
        self._create_overview_view(self.overview_frame)

        # 区域3：图像控制（左下）
        self.zoom_frame = tk.Frame(main_container, relief='ridge', bd=2)
        self.zoom_frame.grid(row=1, column=0, sticky='nsew', padx=(0, 2), pady=(2, 0))
        self._create_zoom_button_area(self.zoom_frame)

        # 区域4：配色系统（右下）
        self.color_frame = tk.Frame(main_container, relief='ridge', bd=2)
        self.color_frame.grid(row=1, column=1, sticky='nsew', padx=(2, 0), pady=(2, 0))
        self._create_color_system_area(self.color_frame)



        # 设置可视化器兼容性
        self._setup_visualizer_compatibility()

    def _create_detail_view(self, parent):
        """创建区域1：图像预览（恢复原有尺寸和显示）"""
        # 标题
        title_label = tk.Label(parent, text="1. 图像预览",
                              font=('Arial', 10, 'bold'), bg='lightblue')
        title_label.pack(fill='x', pady=(0, 2))

        # 创建画布容器（恢复原有的自适应布局）
        canvas_container = tk.Frame(parent)
        canvas_container.pack(fill='both', expand=True, padx=2, pady=2)

        try:
            # 恢复原有的可视化器创建方式
            from cad_visualizer import CADVisualizer

            # 创建可视化器（使用原有方式，不指定固定大小）
            if not hasattr(self, 'visualizer') or not self.visualizer:
                self.visualizer = CADVisualizer()

            # 创建画布（恢复原有的自适应方式）
            if not hasattr(self, 'canvas') or not self.canvas:
                self.canvas = FigureCanvasTkAgg(self.visualizer.get_figure(), canvas_container)
                canvas_widget = self.canvas.get_tk_widget()
                # 恢复原有的自适应布局
                canvas_widget.pack(fill='both', expand=True)

            # 设置详细视图轴的引用
            if hasattr(self.visualizer, 'ax_detail'):
                self.detail_ax = self.visualizer.ax_detail



        except Exception as e:
            print(f"实体组预览创建失败: {e}")
            error_label = tk.Label(canvas_container, text=f"预览创建失败: {e}")
            error_label.pack(fill='both', expand=True)

    def _create_overview_view(self, parent):
        """创建区域2：填充控制（按红框划分的三区域布局）"""
        # 标题
        title_label = tk.Label(parent, text="2. 填充控制",
                              font=('Arial', 10, 'bold'), bg='lightgreen')
        title_label.pack(fill='x', pady=(0, 2))

        # 创建主容器，使用垂直布局
        main_container = tk.Frame(parent)
        main_container.pack(fill='both', expand=True, padx=2, pady=2)

        # 上半部分：房间布局图（占60%）
        self.room_layout_container = tk.Frame(main_container, relief='ridge', bd=1)
        self.room_layout_container.pack(fill='both', expand=True, pady=(0, 2))

        # 创建房间布局图
        self._create_room_layout_display(self.room_layout_container)

        # 下半部分：左右分割的控制区域（占40%）
        bottom_container = tk.Frame(main_container)
        bottom_container.pack(fill='both', expand=False, pady=(2, 0))

        # 配置下半部分的网格权重
        bottom_container.grid_rowconfigure(0, weight=1)
        bottom_container.grid_columnconfigure(0, weight=1)  # 左侧权重1
        bottom_container.grid_columnconfigure(1, weight=1)  # 右侧权重1

        # 左下：房间识别模块和房间类型选择
        self.room_control_container = tk.Frame(bottom_container, relief='ridge', bd=1)
        self.room_control_container.grid(row=0, column=0, sticky='nsew', padx=(0, 1))

        # 创建房间识别控制区
        self._create_room_control_area(self.room_control_container)

        # 右下：房间列表
        self.room_list_container = tk.Frame(bottom_container, relief='ridge', bd=1)
        self.room_list_container.grid(row=0, column=1, sticky='nsew', padx=(1, 0))

        # 创建房间列表区
        self._create_room_list_area(self.room_list_container)

        try:
            # 恢复原有的可视化器使用方式
            # 由于我们已经在detail_view中创建了可视化器，这里只需要设置引用
            if hasattr(self, 'visualizer') and self.visualizer:
                # 设置概览视图轴的引用
                if hasattr(self.visualizer, 'ax_overview'):
                    self.overview_ax = self.visualizer.ax_overview

                # 概览视图使用同一个画布，这是原有的设计
                # 原来的设计是左右分割显示在同一个画布上

            else:
                print("⚠️ 可视化器未初始化，概览视图将在可视化器创建后可用")

        except Exception as e:
            print(f"填充控制区域创建失败: {e}")
            error_label = tk.Label(main_container, text=f"填充控制创建失败: {e}")
            error_label.pack(fill='both', expand=True)

    def _create_room_layout_display(self, parent):
        """创建房间布局图显示区域（上方红框）"""
        try:
            # 标题
            layout_title = tk.Label(parent, text="房间布局图",
                                  font=('Arial', 9, 'bold'), bg='#E6F3FF')
            layout_title.pack(fill='x', pady=(2, 2))

            # 创建matplotlib画布用于显示房间布局
            import matplotlib.pyplot as plt
            from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg

            # 设置中文字体
            plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'SimSun', 'DejaVu Sans']
            plt.rcParams['axes.unicode_minus'] = False

            # 创建图形
            self.room_layout_fig, self.room_layout_ax = plt.subplots(figsize=(6, 4))
            self.room_layout_fig.patch.set_facecolor('white')
            self.room_layout_ax.set_aspect('equal')
            self.room_layout_ax.set_title('房间识别结果', fontsize=10)

            # 创建画布
            self.room_layout_canvas = FigureCanvasTkAgg(self.room_layout_fig, parent)
            self.room_layout_canvas.get_tk_widget().pack(fill='both', expand=True, padx=2, pady=2)

            # 初始化显示
            self._update_room_layout_display()



        except Exception as e:
            print(f"❌ 创建房间布局图显示失败: {e}")
            error_label = tk.Label(parent, text=f"房间布局图创建失败: {e}")
            error_label.pack(fill='both', expand=True)

    def _create_room_control_area(self, parent):
        """创建房间识别控制区域（左下红框）"""
        try:
            # 标题
            control_title = tk.Label(parent, text="房间识别模块",
                                   font=('Arial', 9, 'bold'), bg='#F0FFF0')
            control_title.pack(fill='x', pady=(2, 2))

            # 创建滚动容器
            canvas = tk.Canvas(parent)
            scrollbar = tk.Scrollbar(parent, orient="vertical", command=canvas.yview)
            scrollable_frame = tk.Frame(canvas)

            scrollable_frame.bind(
                "<Configure>",
                lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
            )

            canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
            canvas.configure(yscrollcommand=scrollbar.set)

            # 房间识别按钮区 - 按图示重新排布
            button_frame = tk.Frame(scrollable_frame)
            button_frame.pack(fill='x', padx=5, pady=5)

            # 第一行：自动房间识别按钮（红框，占满宽度）
            auto_btn = tk.Button(button_frame, text="🤖 自动房间识别",
                               command=self._auto_room_recognition,
                               bg='#FFE4B5', font=('Arial', 9, 'bold'),
                               relief='raised', bd=2)
            auto_btn.pack(fill='x', pady=(0, 5))

            # 第二行：三个功能按钮水平排列（红框）
            func_frame = tk.Frame(button_frame)
            func_frame.pack(fill='x', pady=(0, 5))

            # 识别外轮廓按钮
            outline_btn = tk.Button(func_frame, text="识别外轮廓",
                                  command=self._identify_building_outline,
                                  bg='#E6F3FF', font=('Arial', 8))
            outline_btn.pack(side='left', fill='x', expand=True, padx=(0, 2))

            # 识别房间按钮
            room_btn = tk.Button(func_frame, text="识别房间",
                               command=self._identify_rooms,
                               bg='#F0FFF0', font=('Arial', 8))
            room_btn.pack(side='left', fill='x', expand=True, padx=2)

            # 房间切分按钮
            split_btn = tk.Button(func_frame, text="房间切分",
                                command=self._split_rooms,
                                bg='#FFF8DC', font=('Arial', 8))
            split_btn.pack(side='left', fill='x', expand=True, padx=(2, 0))

            # 第三行：房间类型修改区（红框）
            type_frame = tk.Frame(scrollable_frame)
            type_frame.pack(fill='x', padx=5, pady=5)

            # 房间类型标签
            type_label = tk.Label(type_frame, text="房间类型修改:",
                                font=('Arial', 9, 'bold'))
            type_label.pack(anchor='w', pady=(0, 2))

            # 下拉框和按钮的水平布局
            type_control_frame = tk.Frame(type_frame)
            type_control_frame.pack(fill='x', pady=(0, 5))

            # 类型选择下拉框（左侧，红框）
            self.room_type_var = tk.StringVar()
            if hasattr(self, 'room_processor') and self.room_processor:
                room_types = getattr(self.room_processor, 'room_types',
                                   ['客厅', '卧室', '厨房', '卫生间', '阳台', '杂物间', '其他房间', '设备平台'])
            else:
                room_types = ['客厅', '卧室', '厨房', '卫生间', '阳台', '杂物间', '其他房间', '设备平台']

            self.room_type_combo = ttk.Combobox(type_control_frame,
                                              textvariable=self.room_type_var,
                                              values=room_types,
                                              state='readonly', width=15)
            self.room_type_combo.pack(side='left', padx=(0, 10))

            # 应用修改按钮（右侧，红框）
            apply_btn = tk.Button(type_control_frame, text="应用修改",
                                command=self._apply_room_type_change,
                                bg='#FFE4E1', font=('Arial', 8))
            apply_btn.pack(side='left')

            # 第四行：两个空的红框（占位）
            empty_frame = tk.Frame(scrollable_frame)
            empty_frame.pack(fill='x', padx=5, pady=5)

            # 左侧空红框
            empty_left = tk.Frame(empty_frame, relief='ridge', bd=1, height=40)
            empty_left.pack(side='left', fill='both', expand=True, padx=(0, 2))

            # 右侧空红框
            empty_right = tk.Frame(empty_frame, relief='ridge', bd=1, height=40)
            empty_right.pack(side='right', fill='both', expand=True, padx=(2, 0))

            # 布局滚动组件
            canvas.pack(side="left", fill="both", expand=True)
            scrollbar.pack(side="right", fill="y")



        except Exception as e:
            print(f"❌ 创建房间识别控制区域失败: {e}")
            error_label = tk.Label(parent, text=f"控制区域创建失败: {e}")
            error_label.pack(fill='both', expand=True)

    def _create_room_list_area(self, parent):
        """创建房间列表区域（右下红框）"""
        try:
            # 标题
            list_title = tk.Label(parent, text="房间列表",
                                font=('Arial', 9, 'bold'), bg='#FFF8DC')
            list_title.pack(fill='x', pady=(2, 2))

            # 创建房间列表容器
            list_container = tk.Frame(parent)
            list_container.pack(fill='both', expand=True, padx=5, pady=5)

            # 创建Treeview
            columns = ('房间号', '类型', '面积')
            self.room_tree = ttk.Treeview(list_container, columns=columns,
                                        show='headings', height=8)

            # 设置列标题
            self.room_tree.heading('房间号', text='房间号')
            self.room_tree.heading('类型', text='类型')
            self.room_tree.heading('面积', text='面积(m²)')

            # 设置列宽
            self.room_tree.column('房间号', width=60, anchor='center')
            self.room_tree.column('类型', width=80, anchor='center')
            self.room_tree.column('面积', width=80, anchor='center')

            # 添加滚动条
            list_scrollbar = ttk.Scrollbar(list_container, orient='vertical',
                                         command=self.room_tree.yview)
            self.room_tree.configure(yscrollcommand=list_scrollbar.set)

            # 布局
            self.room_tree.pack(side='left', fill='both', expand=True)
            list_scrollbar.pack(side='right', fill='y')

            # 绑定选择事件
            self.room_tree.bind('<<TreeviewSelect>>', self._on_room_select)



        except Exception as e:
            print(f"❌ 创建房间列表区域失败: {e}")
            error_label = tk.Label(parent, text=f"房间列表创建失败: {e}")
            error_label.pack(fill='both', expand=True)

    def _update_room_layout_display(self):
        """更新房间布局图显示"""
        try:
            if not hasattr(self, 'room_layout_ax'):
                return

            # 清空当前显示
            self.room_layout_ax.clear()
            self.room_layout_ax.set_aspect('equal')

            # 检查是否有房间数据
            if hasattr(self, 'room_processor') and self.room_processor and hasattr(self.room_processor, 'rooms'):
                rooms = self.room_processor.rooms
                if rooms:
                    # 显示房间
                    self._draw_rooms_on_layout(rooms)
                    self.room_layout_ax.set_title(f'房间识别结果 - {len(rooms)}个房间', fontsize=10)
                else:
                    # 显示提示信息
                    self.room_layout_ax.text(0.5, 0.5, '暂无房间数据\n请先进行房间识别',
                                           ha='center', va='center', transform=self.room_layout_ax.transAxes,
                                           fontsize=12, color='gray')
                    self.room_layout_ax.set_title('房间布局图', fontsize=10)
            else:
                # 显示初始状态
                self.room_layout_ax.text(0.5, 0.5, '房间识别模块未初始化',
                                       ha='center', va='center', transform=self.room_layout_ax.transAxes,
                                       fontsize=12, color='gray')
                self.room_layout_ax.set_title('房间布局图', fontsize=10)

            # 更新画布
            if hasattr(self, 'room_layout_canvas'):
                self.room_layout_canvas.draw()

        except Exception as e:
            print(f"❌ 更新房间布局图显示失败: {e}")

    def _draw_rooms_on_layout(self, rooms):
        """在布局图上绘制房间"""
        try:
            import matplotlib.patches as patches
            import numpy as np

            # 房间类型颜色映射
            room_colors = {
                '客厅': '#FFB6C1',      # 浅粉色
                '卧室': '#87CEEB',      # 天蓝色
                '厨房': '#98FB98',      # 浅绿色
                '卫生间': '#DDA0DD',    # 梅花色
                '阳台': '#F0E68C',      # 卡其色
                '杂物间': '#D3D3D3',    # 浅灰色
                '其他房间': '#FFEFD5',  # 乳白色
                '设备平台': '#CD853F'   # 秘鲁色
            }

            for i, room in enumerate(rooms):
                try:
                    # 获取房间轮廓
                    if 'outline' in room and room['outline']:
                        outline = room['outline']
                        room_type = room.get('type', '其他房间')
                        color = room_colors.get(room_type, '#FFEFD5')

                        # 绘制房间轮廓
                        if len(outline) >= 3:
                            # 创建多边形
                            polygon = patches.Polygon(outline, closed=True,
                                                    facecolor=color, edgecolor='black',
                                                    alpha=0.7, linewidth=1)
                            self.room_layout_ax.add_patch(polygon)

                            # 添加房间标签
                            center_x = np.mean([p[0] for p in outline])
                            center_y = np.mean([p[1] for p in outline])

                            # 房间编号和类型
                            label = f"R{i+1:02d}\n{room_type}"
                            if 'area' in room:
                                label += f"\n{room['area']:.1f}m²"

                            self.room_layout_ax.text(center_x, center_y, label,
                                                   ha='center', va='center',
                                                   fontsize=8, fontweight='bold',
                                                   bbox=dict(boxstyle="round,pad=0.3",
                                                           facecolor='white', alpha=0.8))

                except Exception as e:
                    print(f"❌ 绘制房间 {i} 失败: {e}")
                    continue

            # 设置坐标轴
            self.room_layout_ax.set_xlabel('X坐标')
            self.room_layout_ax.set_ylabel('Y坐标')
            self.room_layout_ax.grid(True, alpha=0.3)

        except Exception as e:
            print(f"❌ 绘制房间布局失败: {e}")

    def _update_room_list_display(self):
        """更新房间列表显示"""
        try:
            if not hasattr(self, 'room_tree'):
                return

            # 清空现有项目
            for item in self.room_tree.get_children():
                self.room_tree.delete(item)

            # 添加房间数据
            if hasattr(self, 'room_processor') and self.room_processor and hasattr(self.room_processor, 'rooms'):
                rooms = self.room_processor.rooms
                for i, room in enumerate(rooms):
                    room_id = f"R{i+1:02d}"
                    room_type = room.get('type', '未知')
                    area = f"{room.get('area', 0):.1f}"

                    # 添加宽度信息（如果有的话）
                    width_info = ""
                    if 'width' in room and room['width'] > 0:
                        width_info = f" (宽:{room['width']:.0f})"

                    # 组合显示信息
                    display_type = f"{room_type}{width_info}"

                    self.room_tree.insert('', 'end', values=(room_id, display_type, area))

                print(f"✅ 房间列表已更新，共 {len(rooms)} 个房间")
            else:
                print("⚠️ 暂无房间数据")

        except Exception as e:
            print(f"❌ 更新房间列表失败: {e}")

    def _auto_room_recognition(self):
        """自动房间识别"""
        try:
            if not hasattr(self, 'room_processor') or not self.room_processor:
                messagebox.showerror("错误", "房间识别模块未初始化")
                return

            print("🤖 开始自动房间识别...")

            # 获取墙体和门窗数据
            wall_groups, door_window_groups = self._get_wall_door_data()

            if not wall_groups:
                messagebox.showwarning("警告", "未找到墙体数据，无法进行房间识别")
                return

            # 设置数据到房间处理器
            self.room_processor.wall_groups = wall_groups
            self.room_processor.door_window_groups = door_window_groups or []

            # 执行房间识别
            identified_rooms = self.room_processor._identify_rooms()

            if identified_rooms:
                print(f"✅ 房间识别成功: {len(identified_rooms)} 个房间")
                self.room_processor.rooms = identified_rooms

                # 🎨 通知显示控制器房间识别完成
                if hasattr(self, 'display_controller') and self.display_controller:
                    self.display_controller.notify_data_change(
                        DataChangeType.ROOM_FILL,
                        identified_rooms,
                        "EnhancedCADAppV2._auto_room_recognition",
                        {'room_processor': self.room_processor}
                    )

                # 更新显示
                self._update_room_layout_display()
                self._update_room_list_display()

                messagebox.showinfo("成功", f"自动房间识别完成！\n识别到 {len(identified_rooms)} 个房间")
            else:
                messagebox.showerror("错误", "房间识别失败，请检查墙体和门窗数据")

        except Exception as e:
            print(f"❌ 自动房间识别失败: {e}")
            messagebox.showerror("错误", f"自动房间识别失败: {e}")

    def _identify_building_outline(self):
        """识别建筑外轮廓"""
        try:
            if not hasattr(self, 'room_processor') or not self.room_processor:
                messagebox.showerror("错误", "房间识别模块未初始化")
                return

            print("🏢 开始识别建筑外轮廓...")

            # 获取墙体数据
            wall_groups, _ = self._get_wall_door_data()

            if not wall_groups:
                messagebox.showwarning("警告", "未找到墙体数据")
                return

            # 设置墙体数据
            self.room_processor.wall_groups = wall_groups

            # 识别建筑外轮廓
            outline = self.room_processor._identify_building_outline()

            if outline:
                messagebox.showinfo("成功", "建筑外轮廓识别成功")
                self._update_room_layout_display()
            else:
                messagebox.showerror("失败", "建筑外轮廓识别失败")

        except Exception as e:
            print(f"❌ 识别建筑外轮廓失败: {e}")
            messagebox.showerror("错误", f"识别建筑外轮廓失败: {e}")

    def _identify_rooms(self):
        """识别房间"""
        try:
            if not hasattr(self, 'room_processor') or not self.room_processor:
                messagebox.showerror("错误", "房间识别模块未初始化")
                return

            print("🏠 开始识别房间...")

            if not hasattr(self.room_processor, 'building_outline') or not self.room_processor.building_outline:
                messagebox.showwarning("警告", "请先识别建筑外轮廓")
                return

            # 识别房间
            rooms = self.room_processor._identify_rooms()

            if rooms:
                # 自动分类房间
                if hasattr(self.room_processor, '_classify_rooms_automatically'):
                    self.room_processor._classify_rooms_automatically()

                # 更新显示
                self._update_room_layout_display()
                self._update_room_list_display()

                messagebox.showinfo("成功", f"房间识别成功！识别到 {len(rooms)} 个房间")
            else:
                messagebox.showerror("失败", "房间识别失败")

        except Exception as e:
            print(f"❌ 识别房间失败: {e}")
            messagebox.showerror("错误", f"识别房间失败: {e}")

    def _split_rooms(self):
        """房间切分"""
        try:
            if not hasattr(self, 'room_processor') or not self.room_processor:
                messagebox.showerror("错误", "房间识别模块未初始化")
                return

            print("✂️ 开始房间切分...")

            if not hasattr(self.room_processor, 'rooms') or not self.room_processor.rooms:
                messagebox.showwarning("警告", "请先识别房间")
                return

            # 执行房间切分
            if hasattr(self.room_processor, '_split_rooms_by_doors'):
                result = self.room_processor._split_rooms_by_doors()

                if result:
                    # 更新显示
                    self._update_room_layout_display()
                    self._update_room_list_display()

                    messagebox.showinfo("成功", "房间切分完成")
                else:
                    messagebox.showwarning("提示", "房间切分未产生新的分割")
            else:
                messagebox.showwarning("提示", "房间切分功能不可用")

        except Exception as e:
            print(f"❌ 房间切分失败: {e}")
            messagebox.showerror("错误", f"房间切分失败: {e}")

    def _apply_room_type_change(self):
        """应用房间类型修改"""
        try:
            if not hasattr(self, 'room_tree') or not hasattr(self, 'room_type_var'):
                return

            # 获取选中的房间
            selected_items = self.room_tree.selection()
            if not selected_items:
                messagebox.showwarning("警告", "请先选择要修改的房间")
                return

            # 获取新的房间类型
            new_type = self.room_type_var.get()
            if not new_type:
                messagebox.showwarning("警告", "请选择房间类型")
                return

            # 获取选中房间的索引
            item = selected_items[0]
            values = self.room_tree.item(item, 'values')
            room_id = values[0]  # 房间号，如 "R01"

            # 解析房间索引
            room_index = int(room_id[1:]) - 1  # 去掉 "R" 并转换为0基索引

            # 修改房间类型
            if (hasattr(self, 'room_processor') and self.room_processor and
                hasattr(self.room_processor, 'rooms') and
                0 <= room_index < len(self.room_processor.rooms)):

                old_type = self.room_processor.rooms[room_index].get('type', '未知')
                self.room_processor.rooms[room_index]['type'] = new_type

                # 更新显示
                self._update_room_layout_display()
                self._update_room_list_display()

                messagebox.showinfo("成功", f"房间 {room_id} 类型已从 '{old_type}' 修改为 '{new_type}'")
            else:
                messagebox.showerror("错误", "无效的房间索引")

        except Exception as e:
            print(f"❌ 应用房间类型修改失败: {e}")
            messagebox.showerror("错误", f"应用房间类型修改失败: {e}")

    def _on_room_select(self, event):
        """房间选择事件处理"""
        try:
            if not hasattr(self, 'room_tree'):
                return

            selected_items = self.room_tree.selection()
            if selected_items:
                item = selected_items[0]
                values = self.room_tree.item(item, 'values')
                room_id = values[0]
                room_type = values[1].split(' ')[0]  # 去掉宽度信息

                # 设置房间类型选择框
                if hasattr(self, 'room_type_var'):
                    self.room_type_var.set(room_type)

                print(f"📍 选中房间: {room_id} - {room_type}")

        except Exception as e:
            print(f"❌ 房间选择事件处理失败: {e}")

    def _create_room_recognition_panel(self, parent):
        """创建房间识别面板"""
        try:
            if not self.room_processor or not RoomRecognitionUI:
                print("❌ 房间识别模块不可用，跳过创建面板")
                return

            # 创建房间识别UI
            self.room_ui = RoomRecognitionUI(
                parent,
                self.room_processor,
                update_callback=self._on_room_update
            )

            # 设置数据获取回调
            self.room_ui.set_data_callback(self._get_wall_door_data)

            print("✅ 房间识别面板创建成功")

        except Exception as e:
            print(f"❌ 创建房间识别面板失败: {e}")
            import traceback
            traceback.print_exc()

    def _get_wall_door_data(self):
        """获取墙体和门窗数据供房间识别使用"""
        try:
            wall_groups = []
            door_window_groups = []

            # 从当前处理的实体中获取墙体和门窗组
            if hasattr(self, 'processor') and self.processor:
                entities = getattr(self.processor, 'current_file_entities', [])

                if entities:
                    # 获取墙体组
                    wall_entities = [e for e in entities if e.get('label') == 'wall']
                    if wall_entities:
                        # 简单分组：按连接性分组墙体
                        wall_groups = self._group_entities_by_connectivity(wall_entities)

                    # 获取门窗组
                    door_window_entities = [e for e in entities if e.get('label') in ['door', 'window', 'door_window']]
                    if door_window_entities:
                        # 简单分组：按连接性分组门窗
                        door_window_groups = self._group_entities_by_connectivity(door_window_entities)

            print(f"🏠 获取到墙体组: {len(wall_groups)}, 门窗组: {len(door_window_groups)}")
            return wall_groups, door_window_groups

        except Exception as e:
            print(f"❌ 获取墙体门窗数据失败: {e}")
            return [], []

    def _group_entities_by_connectivity(self, entities, threshold=50):
        """按连接性对实体进行简单分组"""
        try:
            if not entities:
                return []

            groups = []
            used_indices = set()

            for i, entity in enumerate(entities):
                if i in used_indices:
                    continue

                # 创建新组
                group = [entity]
                used_indices.add(i)

                # 查找连接的实体
                self._find_connected_entities_simple(entities, entity, group, used_indices, threshold)

                if len(group) >= 1:  # 至少1个实体
                    groups.append(group)

            return groups

        except Exception as e:
            print(f"❌ 实体分组失败: {e}")
            return []

    def _find_connected_entities_simple(self, entities, base_entity, group, used_indices, threshold):
        """简单的连接实体查找"""
        try:
            base_coords = self._get_entity_coordinates(base_entity)
            if not base_coords:
                return

            for i, entity in enumerate(entities):
                if i in used_indices:
                    continue

                entity_coords = self._get_entity_coordinates(entity)
                if not entity_coords:
                    continue

                # 检查是否连接
                if self._are_entities_connected(base_coords, entity_coords, threshold):
                    group.append(entity)
                    used_indices.add(i)
                    # 递归查找连接的实体
                    self._find_connected_entities_simple(entities, entity, group, used_indices, threshold)

        except Exception as e:
            print(f"❌ 查找连接实体失败: {e}")

    def _get_entity_coordinates(self, entity):
        """获取实体坐标"""
        try:
            entity_type = entity.get('type', '')

            if entity_type == 'LINE':
                start = entity.get('start', {})
                end = entity.get('end', {})
                if start and end:
                    return [(start.get('x', 0), start.get('y', 0)),
                           (end.get('x', 0), end.get('y', 0))]

            elif entity_type in ['LWPOLYLINE', 'POLYLINE']:
                points = entity.get('points', [])
                if points:
                    return [(p.get('x', 0), p.get('y', 0)) for p in points]

            return []

        except Exception as e:
            print(f"❌ 获取实体坐标失败: {e}")
            return []

    def _are_entities_connected(self, coords1, coords2, threshold):
        """检查两个实体是否连接"""
        try:
            if not coords1 or not coords2:
                return False

            # 检查端点是否接近
            for p1 in coords1:
                for p2 in coords2:
                    distance = ((p1[0] - p2[0])**2 + (p1[1] - p2[1])**2)**0.5
                    if distance <= threshold:
                        return True

            return False

        except Exception as e:
            print(f"❌ 检查实体连接失败: {e}")
            return False

    def _on_room_update(self, action=None):
        """房间更新回调（增强版 - 支持数据获取）"""
        try:
            if action == 'get_wall_door_data':
                # 返回墙体和门窗数据
                return self._get_wall_door_data()
            else:
                print("🏠 房间信息已更新")
                # 这里可以添加房间更新后的处理逻辑

        except Exception as e:
            print(f"❌ 房间更新回调失败: {e}")
            return None

    def _safe_get_layer_name(self, entity):
        """安全地获取实体的图层名称"""
        try:
            layer_raw = entity.get('layer', '')
            # 确保layer是字符串类型
            if isinstance(layer_raw, (int, float)):
                return str(layer_raw).lower()
            elif isinstance(layer_raw, str):
                return layer_raw.lower()
            else:
                return str(layer_raw).lower() if layer_raw else ''
        except Exception as e:
            print(f"⚠️ 获取图层名称失败: {e}")
            return ''

    def _get_wall_door_data(self):
        """获取墙体和门窗数据供房间识别使用"""
        try:
            print("📊 获取墙体和门窗数据...")

            wall_groups = []
            door_window_groups = []

            # 获取墙体组数据
            if hasattr(self.processor, 'all_groups') and self.processor.all_groups:
                print(f"🔍 检查 {len(self.processor.all_groups)} 个实体组...")

                for group_idx, group in enumerate(self.processor.all_groups):
                    if not group:
                        continue

                    # 检查组的标签来确定类型
                    group_labels = set()
                    wall_entities = []
                    door_window_entities = []

                    # 调试：收集组中所有标签
                    debug_labels = []
                    for entity in group:
                        label = entity.get('label', '').lower()
                        if label:
                            group_labels.add(label)
                            debug_labels.append(label)

                        # 根据标签分类实体（使用包含匹配）
                        if any(wall_pattern in label for wall_pattern in ['wall', '墙体', '墙']):
                            wall_entities.append(entity)
                        elif any(door_pattern in label for door_pattern in ['door', 'window', '门', '窗', '门窗', 'railing', '栏杆']):
                            door_window_entities.append(entity)
                        elif not label or label in ['none', 'unlabeled']:
                            # 未标注的实体，根据图层判断（使用安全方法）
                            layer = self._safe_get_layer_name(entity)
                            if any(wall_pattern in layer for wall_pattern in ['wall', '墙', 'qiang']):
                                wall_entities.append(entity)
                            elif any(door_pattern in layer for door_pattern in ['door', 'window', '门', '窗', 'railing', '栏杆']):
                                door_window_entities.append(entity)

                    # 调试：显示组的标签信息
                    if debug_labels:
                        unique_labels = list(set(debug_labels))
                        print(f"   🔍 组 {group_idx+1}: 标签 {unique_labels}, 墙体实体: {len(wall_entities)}, 门窗实体: {len(door_window_entities)}")

                    # 根据主要标签确定组类型
                    if wall_entities:
                        wall_groups.append(wall_entities)
                        print(f"   ✅ 墙体组 {len(wall_groups)}: {len(wall_entities)} 个实体")

                    if door_window_entities:
                        door_window_groups.append(door_window_entities)
                        print(f"   ✅ 门窗组 {len(door_window_groups)}: {len(door_window_entities)} 个实体")

            # 如果没有找到标注的组，尝试从自动识别的墙体组获取
            if not wall_groups and hasattr(self.processor, 'wall_groups'):
                wall_groups = self.processor.wall_groups or []
                print(f"📋 使用自动识别的墙体组: {len(wall_groups)} 个")

            # 如果没有找到门窗组，尝试从处理器获取
            if not door_window_groups and hasattr(self.processor, 'door_window_groups'):
                door_window_groups = self.processor.door_window_groups or []
                print(f"📋 使用处理器中的门窗组: {len(door_window_groups)} 个")

            print(f"📊 数据获取完成: {len(wall_groups)} 个墙体组, {len(door_window_groups)} 个门窗组")

            return {
                'wall_groups': wall_groups,
                'door_window_groups': door_window_groups
            }

        except Exception as e:
            print(f"❌ 获取墙体门窗数据失败: {e}")
            import traceback
            traceback.print_exc()
            return {
                'wall_groups': [],
                'door_window_groups': []
            }

    def _create_legend_panel(self, parent):
        """在右侧框内创建索引图"""
        # 标题
        legend_title = tk.Label(parent, text="索引图",
                               font=('Arial', 9, 'bold'))
        legend_title.pack(anchor='w', padx=5, pady=(2, 0))

        # 创建画布用于显示索引图
        self.legend_frame = tk.Frame(parent)
        self.legend_frame.pack(fill='both', expand=True, padx=5, pady=2)

        # 创建matplotlib图形用于显示索引图
        try:
            import matplotlib.pyplot as plt
            from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
            import matplotlib.font_manager as fm

            # 首先设置matplotlib全局中文字体配置
            plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'SimSun', 'DejaVu Sans']
            plt.rcParams['axes.unicode_minus'] = False

            # 创建小的图形用于索引图（压缩高度）
            self.legend_fig, self.legend_ax = plt.subplots(figsize=(6, 2))
            self.legend_fig.patch.set_facecolor('white')

            # 设置中文字体
            try:
                # 尝试多种中文字体
                chinese_font_names = [
                    'Microsoft YaHei',
                    'SimHei',
                    'SimSun',
                    'KaiTi',
                    'FangSong',
                    'Microsoft JhengHei',
                    'PingFang SC',
                    'Hiragino Sans GB',
                    'Source Han Sans CN',
                    'Noto Sans CJK SC'
                ]

                self.legend_font = None
                for font_name in chinese_font_names:
                    try:
                        # 测试字体是否可用
                        test_font = fm.FontProperties(family=font_name)
                        # 如果能创建成功，使用这个字体
                        self.legend_font = test_font
                        print(f"✅ 使用中文字体: {font_name}")
                        break
                    except:
                        continue

                # 如果没有找到合适的字体，使用系统默认
                if self.legend_font is None:
                    import matplotlib
                    matplotlib.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'DejaVu Sans']
                    matplotlib.rcParams['axes.unicode_minus'] = False
                    self.legend_font = fm.FontProperties()
                    print("✅ 使用系统默认中文字体配置")

            except Exception as e:
                print(f"⚠️ 字体设置失败: {e}")
                # 备用方案：直接设置matplotlib全局字体
                import matplotlib
                matplotlib.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'DejaVu Sans']
                matplotlib.rcParams['axes.unicode_minus'] = False
                self.legend_font = fm.FontProperties()

            # 创建画布
            self.legend_canvas = FigureCanvasTkAgg(self.legend_fig, self.legend_frame)
            self.legend_canvas.get_tk_widget().pack(fill='both', expand=True)

            # 初始化空的索引图
            self._update_legend_display()

            print("✅ 索引图已创建在右侧框内")

        except Exception as e:
            print(f"❌ 创建索引图失败: {e}")
            # 创建错误标签
            error_label = tk.Label(self.legend_frame, text=f"索引图创建失败: {e}")
            error_label.pack(fill='both', expand=True)

    def _update_legend_display(self):
        """更新右侧索引图显示"""
        try:
            if not hasattr(self, 'legend_ax') or not self.legend_ax:
                return

            # 清空现有内容
            self.legend_ax.clear()
            self.legend_ax.set_xlim(0, 10)
            self.legend_ax.set_ylim(0, 10)
            self.legend_ax.axis('off')  # 隐藏坐标轴

            if not self.processor or not hasattr(self.processor, 'all_groups'):
                # 显示空状态
                self.legend_ax.text(5, 5, '暂无数据', ha='center', va='center',
                                   fontproperties=self.legend_font, fontsize=12)
                self.legend_canvas.draw()
                return

            # 获取所有组的信息和统计
            groups_info = self.processor.get_groups_info()
            group_status_stats = {'current': 0, 'labeled': 0, 'auto_labeled': 0, 'unlabeled': 0}

            # 统计各状态的组数
            for i, group_info in enumerate(groups_info):
                status = group_info['status']
                if status == 'labeling':
                    group_status_stats['current'] += group_info['entity_count']
                elif status == 'labeled' or status == 'relabeled':
                    group_status_stats['labeled'] += group_info['entity_count']
                elif status == 'auto_labeled':
                    group_status_stats['auto_labeled'] += group_info['entity_count']
                else:
                    group_status_stats['unlabeled'] += group_info['entity_count']

            # 获取实体类别信息
            existing_categories = set()
            if hasattr(self.processor, 'all_groups') and self.processor.all_groups:
                for group in self.processor.all_groups:
                    if group:
                        for entity in group:
                            label = entity.get('label')
                            if label and label != 'None' and label != 'none':
                                existing_categories.add(label)

            # 创建索引图内容
            self._draw_legend_content(group_status_stats, existing_categories)

            print(f"✅ 右侧索引图已更新")

        except Exception as e:
            print(f"❌ 更新右侧索引图失败: {e}")
            import traceback
            traceback.print_exc()

    def _draw_legend_content(self, group_status_stats, existing_categories):
        """绘制索引图内容"""
        try:
            import matplotlib.pyplot as plt
            import matplotlib.patches as patches

            y_pos = 9.5
            line_height = 0.8

            # 第一部分：组状态图例
            if any(count > 0 for count in group_status_stats.values()):
                # 组状态标题
                self.legend_ax.text(0.5, y_pos, '━━ 组状态 ━━',
                                   fontproperties=self.legend_font, fontsize=10, weight='bold')
                y_pos -= line_height

                # 当前标注组（增加轮廓）
                if group_status_stats.get('current', 0) > 0:
                    highlight_color = getattr(self, 'current_color_scheme', {}).get('highlight', '#FF0000')
                    self.legend_ax.add_patch(plt.Rectangle((0.5, y_pos-0.2), 0.3, 0.4,
                                                          facecolor=highlight_color, alpha=0.8,
                                                          edgecolor='black', linewidth=1))
                    self.legend_ax.text(1.0, y_pos, f'正在标注 ({group_status_stats["current"]}个实体)',
                                       fontproperties=self.legend_font, fontsize=9, va='center')
                    y_pos -= line_height

                # 已手动标注组（增加轮廓）
                if group_status_stats.get('labeled', 0) > 0:
                    self.legend_ax.add_patch(plt.Rectangle((0.5, y_pos-0.2), 0.3, 0.4,
                                                          facecolor='#00AA00', alpha=0.8,
                                                          edgecolor='black', linewidth=1))
                    self.legend_ax.text(1.0, y_pos, f'已标注 ({group_status_stats["labeled"]}个实体)',
                                       fontproperties=self.legend_font, fontsize=9, va='center')
                    y_pos -= line_height

                # 自动标注组（增加轮廓）
                if group_status_stats.get('auto_labeled', 0) > 0:
                    self.legend_ax.add_patch(plt.Rectangle((0.5, y_pos-0.2), 0.3, 0.4,
                                                          facecolor='#0066CC', alpha=0.8,
                                                          edgecolor='black', linewidth=1))
                    self.legend_ax.text(1.0, y_pos, f'自动标注 ({group_status_stats["auto_labeled"]}个实体)',
                                       fontproperties=self.legend_font, fontsize=9, va='center')
                    y_pos -= line_height

                # 未标注组（增加轮廓）
                if group_status_stats.get('unlabeled', 0) > 0:
                    self.legend_ax.add_patch(plt.Rectangle((0.5, y_pos-0.2), 0.3, 0.4,
                                                          facecolor='#D3D3D3', alpha=0.8,
                                                          edgecolor='black', linewidth=1))
                    self.legend_ax.text(1.0, y_pos, f'未标注 ({group_status_stats["unlabeled"]}个实体)',
                                       fontproperties=self.legend_font, fontsize=9, va='center')
                    y_pos -= line_height

            # 第二部分：实体类别图例
            if existing_categories:
                y_pos -= 0.3  # 增加间距

                # 类别标题
                self.legend_ax.text(0.5, y_pos, '━━ 实体类别 ━━',
                                   fontproperties=self.legend_font, fontsize=10, weight='bold')
                y_pos -= line_height

                # 类别颜色映射（使用配色方案）
                if hasattr(self, 'legend_category_colors'):
                    # 使用已更新的配色方案颜色
                    category_colors = self.legend_category_colors
                else:
                    # 使用当前配色方案或默认颜色
                    category_colors = {
                        'wall': getattr(self, 'current_color_scheme', {}).get('wall', '#8B4513'),
                        'door': getattr(self, 'current_color_scheme', {}).get('door_window', '#FFD700'),
                        'window': getattr(self, 'current_color_scheme', {}).get('door_window', '#87CEEB'),
                        'column': getattr(self, 'current_color_scheme', {}).get('column', '#696969'),
                        'beam': getattr(self, 'current_color_scheme', {}).get('other', '#2F4F4F'),
                        'stair': getattr(self, 'current_color_scheme', {}).get('stair', '#9370DB'),
                        'elevator': getattr(self, 'current_color_scheme', {}).get('elevator', '#FF6347'),
                        'other': getattr(self, 'current_color_scheme', {}).get('other', '#808080'),
                        'door_window': getattr(self, 'current_color_scheme', {}).get('door_window', '#FFD700'),
                        'furniture': getattr(self, 'current_color_scheme', {}).get('furniture', '#8B4513')
                    }

                # 类别名称映射（使用中文）
                category_names = {
                    'wall': '墙体',
                    'door': '门',
                    'window': '窗',
                    'column': '柱子',
                    'beam': '梁',
                    'stair': '楼梯',
                    'elevator': '电梯',
                    'other': '其他',
                    'door_window': '门窗',
                    'furniture': '家具'
                }

                # 按字母顺序排序类别
                sorted_categories = sorted(existing_categories)
                for cat in sorted_categories:
                    color = category_colors.get(cat, '#808080')
                    name = category_names.get(cat, cat)  # 如果没有映射，直接使用原名

                    # 为颜色方块增加轮廓，避免颜色与背景颜色一样时不好查看
                    self.legend_ax.add_patch(plt.Rectangle((0.5, y_pos-0.2), 0.3, 0.4,
                                                          facecolor=color, alpha=0.8,
                                                          edgecolor='black', linewidth=1))
                    self.legend_ax.text(1.0, y_pos, f'■ {name}',
                                       fontproperties=self.legend_font, fontsize=9, va='center')
                    y_pos -= line_height

            # 刷新画布
            self.legend_canvas.draw()

        except Exception as e:
            print(f"❌ 绘制索引图内容失败: {e}")
            import traceback
            traceback.print_exc()

    def _create_color_system_area(self, parent):
        """创建区域4：配色系统（恢复原有功能）"""
        # 标题
        title_label = tk.Label(parent, text="4. 配色系统",
                              font=('Arial', 10, 'bold'), bg='lightyellow')
        title_label.pack(fill='x', pady=(0, 2))

        # 创建内容容器
        content_frame = tk.Frame(parent)
        content_frame.pack(fill='both', expand=True, padx=2, pady=2)

        # 恢复原有的配色系统功能
        self._create_original_color_system(content_frame)



    def _create_original_color_system(self, parent):
        """创建原有的配色系统功能"""
        try:
            # 第一行：配色方案选择和应用
            scheme_frame = tk.Frame(parent)
            scheme_frame.pack(fill='x', padx=2, pady=2)

            tk.Label(scheme_frame, text="配色方案:", font=('Arial', 8)).pack(side='left')

            # 配色方案下拉菜单
            self.color_scheme_var = tk.StringVar(value="默认配色")
            self.color_scheme_combo = ttk.Combobox(scheme_frame, textvariable=self.color_scheme_var,
                                                   font=('Arial', 8), width=10, state='readonly')
            self.color_scheme_combo.pack(side='left', padx=(5, 2))
            self.color_scheme_combo.bind('<<ComboboxSelected>>', self.on_color_scheme_selected)

            # 应用配色按钮
            tk.Button(scheme_frame, text="应用配色", command=self.apply_color_scheme_v2,
                   bg='#4CAF50', fg='white', font=('Arial', 8)).pack(side='left', padx=2)

            # 第二行：配色管理按钮 - 与缩放查看按钮等大并平齐
            btn_frame = tk.Frame(parent)
            btn_frame.pack(fill='x', padx=2, pady=10)  # 增加垂直间距与缩放按钮平齐

            # 四个按钮与缩放查看按钮等大
            tk.Button(btn_frame, text="配色设置", command=self.open_color_settings,
                   bg='#9C27B0', fg='white', font=('Arial', 9, 'bold'),
                   width=8, height=3, relief='raised', bd=2).pack(side='left', fill='x', expand=True, padx=(0, 1))

            tk.Button(btn_frame, text="保存配色", command=self.save_color_scheme,
                   bg='#607D8B', fg='white', font=('Arial', 9, 'bold'),
                   width=8, height=3, relief='raised', bd=2).pack(side='left', fill='x', expand=True, padx=1)

            tk.Button(btn_frame, text="导出文件", command=self.export_current_color_scheme,
                   bg='#FF9800', fg='white', font=('Arial', 9, 'bold'),
                   width=8, height=3, relief='raised', bd=2).pack(side='left', fill='x', expand=True, padx=1)

            tk.Button(btn_frame, text="加载配色", command=self.load_color_scheme,
                   bg='#795548', fg='white', font=('Arial', 9, 'bold'),
                   width=8, height=3, relief='raised', bd=2).pack(side='left', fill='x', expand=True, padx=(1, 0))

            # 初始化配色方案下拉菜单
            self.update_color_scheme_combo()



        except Exception as e:
            print(f"创建原有配色系统失败: {e}")
            # 创建简单的错误显示
            error_label = tk.Label(parent, text=f"配色系统加载失败: {e}",
                                 font=('Arial', 8), fg='red')
            error_label.pack(pady=10)

    def apply_color_scheme_v2(self):
        """应用配色方案（V2版本 - 修复配色系统问题和持久化）"""
        try:
            print("🎨 开始应用配色方案...")

            # 🔧 修复1: 更新可视化器的配色方案，确保后续操作都使用新配色
            if hasattr(self, 'visualizer') and self.visualizer:
                print("🔄 更新可视化器配色方案...")
                self.visualizer.update_color_scheme(self.current_color_scheme)

                # 🎨 通知显示控制器配色方案变化
                if hasattr(self, 'display_controller') and self.display_controller:
                    self.display_controller.notify_data_change(
                        DataChangeType.COLOR_SCHEME,
                        self.current_color_scheme.copy(),
                        "EnhancedCADAppV2.apply_color_scheme_v2"
                    )

                # 只更新全图概览（红框内容）
                if hasattr(self.visualizer, 'ax_overview'):
                    self._update_overview_with_color_scheme()

                # 3. 改变配色后，索引图颜色也应随之更改
                self._update_legend_colors_with_scheme()

                # 4. 点击应用配色后应立即更新视图显示
                self._force_refresh_views()

                # 🔧 修复1: 标记配色方案已应用，确保后续操作使用新配色
                self._color_scheme_applied = True
                print("✅ 配色方案已持久化到可视化器")

                print("✅ 配色方案已应用到红框内容和索引图")
                messagebox.showinfo("成功", "配色方案已应用并将持续生效")
            else:
                print("❌ 可视化器不可用")
                messagebox.showwarning("警告", "可视化器不可用，无法应用配色")

        except Exception as e:
            print(f"❌ 应用配色方案失败: {e}")
            import traceback
            traceback.print_exc()
            messagebox.showerror("错误", f"应用配色方案失败: {e}")

    def _force_refresh_views(self):
        """强制刷新视图显示（修复2: 确保立即更新）"""
        try:
            print("🔄 开始强制刷新所有视图...")

            # 刷新全图概览画布 - 使用draw()确保立即更新
            if hasattr(self.visualizer, 'canvas_overview'):
                self.visualizer.canvas_overview.draw()  # 立即绘制，不延迟
                self.visualizer.canvas_overview.flush_events()
                print("  ✅ 全图概览画布已刷新")

            # 刷新索引图画布 - 使用draw()确保立即更新
            if hasattr(self, 'legend_canvas'):
                self.legend_canvas.draw()  # 立即绘制，不延迟
                self.legend_canvas.flush_events()
                print("  ✅ 索引图画布已刷新")

            # 刷新主画布 - 使用draw()确保立即更新
            if hasattr(self, 'canvas'):
                self.canvas.draw()  # 立即绘制，不延迟
                self.canvas.flush_events()
                print("  ✅ 主画布已刷新")

            # 强制更新Tkinter界面
            if hasattr(self, 'root'):
                self.root.update_idletasks()
                self.root.update()
                print("  ✅ Tkinter界面已更新")

            print("✅ 所有视图强制刷新完成")

        except Exception as e:
            print(f"❌ 强制刷新视图失败: {e}")
            import traceback
            traceback.print_exc()

    def _update_overview_with_color_scheme(self):
        """更新全图概览的配色方案（只针对红框内容）"""
        try:
            ax_overview = self.visualizer.ax_overview

            # 设置背景色
            background_color = self.current_color_scheme.get('background', '#FFFFFF')
            ax_overview.set_facecolor(background_color)

            # 如果有数据，重新绘制全图概览
            if (hasattr(self.processor, 'all_groups') and
                self.processor.all_groups and
                hasattr(self.processor, 'current_file_entities') and
                self.processor.current_file_entities):

                # 清除现有内容
                ax_overview.clear()
                ax_overview.set_facecolor(background_color)

                # 🔧 修复1&2: 正确确定当前组索引，避免多组高亮，修复标注完成后颜色问题
                current_group_index = None
                all_groups_labeled = True  # 检查是否所有组都已标注完成

                if hasattr(self.processor, 'current_group_index') and self.processor.current_group_index is not None:
                    # 检查是否还有未标注的组
                    if hasattr(self.processor, 'all_groups'):
                        for group_idx, group in enumerate(self.processor.all_groups):
                            group_labeled = any(entity.get('label') and entity.get('label') not in ['None', 'none']
                                              for entity in group)
                            if not group_labeled:
                                all_groups_labeled = False
                                break

                    # 只有在手动标注模式下且还有未标注组时才高亮当前组
                    if getattr(self.processor, 'manual_grouping_mode', False) and not all_groups_labeled:
                        current_group_index = self.processor.current_group_index
                        print(f"🎯 当前标注组: {current_group_index + 1}")
                    else:
                        if all_groups_labeled:
                            print("🎉 所有组已标注完成，不高亮任何组")
                        else:
                            print("🔄 非手动标注模式，不高亮任何组")

                # 🔧 修复3: 绘制所有实体，正确应用配色方案到实体
                print(f"🎨 开始重新绘制 {len(self.processor.current_file_entities)} 个实体...")

                # 为每个实体分配组信息，确保颜色正确映射
                entity_group_map = {}
                if hasattr(self.processor, 'all_groups'):
                    for group_idx, group in enumerate(self.processor.all_groups):
                        for entity in group:
                            entity_group_map[id(entity)] = group_idx

                for entity in self.processor.current_file_entities:
                    # 获取实体所属组
                    entity_group_idx = entity_group_map.get(id(entity))

                    # 🔧 修复2: 根据实体标签和组状态确定颜色，修复标注完成后颜色问题
                    if entity_group_idx == current_group_index and current_group_index is not None:
                        # 当前标注组使用高亮颜色（只有在有当前组时）
                        color = self.current_color_scheme.get('highlight', '#FF0000')
                        linewidth = 3
                        alpha = 1.0
                    else:
                        # 其他实体使用配色方案中的颜色，根据标签确定
                        color = self._get_entity_color_from_scheme_enhanced(entity)

                        # 🔧 修复2: 根据实体状态确定线宽和透明度
                        entity_label = entity.get('label')
                        if entity_label and entity_label not in ['None', 'none']:
                            # 已标注实体使用正常线宽
                            linewidth = 1.5
                            alpha = 0.9
                        elif entity.get('auto_labeled', False):
                            # 自动标注实体使用稍细线宽
                            linewidth = 1.2
                            alpha = 0.8
                        else:
                            # 未标注实体使用细线宽
                            linewidth = 1.0
                            alpha = 0.7

                    self.visualizer._draw_entity(entity, color, linewidth, alpha, ax_overview)

                # 🔧 修复1: 只绘制一个组的边界框（当前标注组）
                if current_group_index is not None and current_group_index < len(self.processor.all_groups):
                    current_group = self.processor.all_groups[current_group_index]

                    # 计算组边界
                    min_x = min_y = float('inf')
                    max_x = max_y = float('-inf')

                    for entity in current_group:
                        coords = self.visualizer._get_entity_coordinates_fixed(entity)
                        for x, y in coords:
                            min_x, max_x = min(min_x, x), max(max_x, x)
                            min_y, max_y = min(min_y, y), max(max_y, y)

                    if min_x != float('inf'):
                        # 绘制红色边界框
                        from matplotlib.patches import Rectangle
                        rect = Rectangle((min_x, min_y), max_x - min_x, max_y - min_y,
                                       linewidth=3, edgecolor='red', facecolor='none', linestyle='-')
                        ax_overview.add_patch(rect)

                        # 添加组标签
                        center_x = (min_x + max_x) / 2
                        center_y = (min_y + max_y) / 2
                        ax_overview.text(center_x, center_y, f'组{current_group_index + 1}[标注中]',
                                       fontsize=10, color='red', weight='bold',
                                       bbox=dict(facecolor='yellow', alpha=0.8, edgecolor='red'),
                                       ha='center', va='center')

                # 设置标题
                title_color = self.current_color_scheme.get('text', '#000000')
                title_text = f'CAD填充控制（增强版）- {len(self.processor.current_file_entities)}个实体'
                if current_group_index is not None:
                    title_text += f' [当前标注: 组{current_group_index + 1}]'
                ax_overview.set_title(title_text, color=title_color, fontsize=10)

                print("✅ 全图概览配色已更新（实体颜色已正确应用）")
            else:
                # 即使没有数据，也应用背景色
                ax_overview.clear()
                ax_overview.set_facecolor(background_color)
                title_color = self.current_color_scheme.get('text', '#000000')
                ax_overview.set_title('全图概览 - 配色方案已应用', color=title_color)
                print("✅ 全图概览背景色已更新")

        except Exception as e:
            print(f"❌ 更新全图概览配色失败: {e}")
            import traceback
            traceback.print_exc()

    def _get_entity_color_from_scheme_enhanced(self, entity):
        """根据配色方案获取实体颜色（增强版）"""
        try:
            # 优先使用实体的标签
            if entity.get('label'):
                label = entity['label']
                # 根据标签类型映射到配色方案
                label_color_map = {
                    'wall': self.current_color_scheme.get('wall', '#8B4513'),
                    'door': self.current_color_scheme.get('door_window', '#FFD700'),
                    'window': self.current_color_scheme.get('door_window', '#87CEEB'),
                    'door_window': self.current_color_scheme.get('door_window', '#FFD700'),
                    'column': self.current_color_scheme.get('column', '#696969'),
                    'beam': self.current_color_scheme.get('other', '#2F4F4F'),
                    'stair': self.current_color_scheme.get('stair', '#9370DB'),
                    'elevator': self.current_color_scheme.get('elevator', '#FF6347'),
                    'furniture': self.current_color_scheme.get('furniture', '#8B4513'),
                    'other': self.current_color_scheme.get('other', '#808080')
                }
                return label_color_map.get(label, self.current_color_scheme.get('other', '#808080'))

            # 如果没有标签，根据实体类型和图层判断
            entity_type = entity.get('type', 'LINE')
            layer_name = str(entity.get('layer', '')).lower()

            # 根据图层名称判断类型
            if any(keyword in layer_name for keyword in ['wall', '墙', 'wall']):
                return self.current_color_scheme.get('wall', '#8B4513')
            elif any(keyword in layer_name for keyword in ['door', 'window', '门', '窗']):
                return self.current_color_scheme.get('door_window', '#FFD700')
            elif any(keyword in layer_name for keyword in ['column', '柱']):
                return self.current_color_scheme.get('column', '#696969')
            elif any(keyword in layer_name for keyword in ['furniture', '家具']):
                return self.current_color_scheme.get('furniture', '#8B4513')
            else:
                # 根据实体类型使用默认颜色
                type_color_map = {
                    'LINE': self.current_color_scheme.get('wall', '#8B4513'),
                    'LWPOLYLINE': self.current_color_scheme.get('wall', '#8B4513'),
                    'POLYLINE': self.current_color_scheme.get('wall', '#8B4513'),
                    'CIRCLE': self.current_color_scheme.get('other', '#808080'),
                    'ARC': self.current_color_scheme.get('other', '#808080'),
                    'INSERT': self.current_color_scheme.get('furniture', '#8B4513'),
                    'TEXT': self.current_color_scheme.get('text', '#000000'),
                    'MTEXT': self.current_color_scheme.get('text', '#000000')
                }
                return type_color_map.get(entity_type, self.current_color_scheme.get('other', '#808080'))

        except Exception as e:
            print(f"获取实体颜色失败: {e}")
            return self.current_color_scheme.get('other', '#808080')

    def _get_entity_color_from_scheme(self, entity):
        """根据配色方案获取实体颜色"""
        try:
            # 如果实体有标签，使用标签对应的颜色
            if entity.get('label'):
                label = entity['label']
                return self.current_color_scheme.get(label, self.current_color_scheme.get('other', '#C0C0C0'))

            # 如果没有标签，使用默认颜色
            entity_type = entity.get('type', 'LINE')
            type_color_map = {
                'LINE': self.current_color_scheme.get('wall', '#000000'),
                'LWPOLYLINE': self.current_color_scheme.get('wall', '#000000'),
                'POLYLINE': self.current_color_scheme.get('wall', '#000000'),
                'CIRCLE': self.current_color_scheme.get('other', '#C0C0C0'),
                'ARC': self.current_color_scheme.get('other', '#C0C0C0'),
                'INSERT': self.current_color_scheme.get('furniture', '#0000FF'),
                'TEXT': self.current_color_scheme.get('text', '#000000'),
                'MTEXT': self.current_color_scheme.get('text', '#000000')
            }

            return type_color_map.get(entity_type, self.current_color_scheme.get('other', '#C0C0C0'))

        except Exception as e:
            print(f"获取实体颜色失败: {e}")
            return '#808080'  # 默认灰色

    def _update_legend_colors_with_scheme(self):
        """根据配色方案更新索引图颜色"""
        try:
            if not hasattr(self, 'legend_ax') or not self.legend_ax:
                return

            # 更新索引图的类别颜色映射，使用当前配色方案
            self.legend_category_colors = {
                'wall': self.current_color_scheme.get('wall', '#8B4513'),
                'door': self.current_color_scheme.get('door_window', '#FFD700'),
                'window': self.current_color_scheme.get('door_window', '#87CEEB'),
                'column': self.current_color_scheme.get('column', '#696969'),
                'beam': self.current_color_scheme.get('other', '#2F4F4F'),
                'stair': self.current_color_scheme.get('stair', '#9370DB'),
                'elevator': self.current_color_scheme.get('elevator', '#FF6347'),
                'other': self.current_color_scheme.get('other', '#808080'),
                'door_window': self.current_color_scheme.get('door_window', '#FFD700'),
                'furniture': self.current_color_scheme.get('furniture', '#8B4513')
            }

            # 重新绘制索引图
            self._update_legend_display()

            print("✅ 索引图颜色已根据配色方案更新")

        except Exception as e:
            print(f"❌ 更新索引图颜色失败: {e}")
            import traceback
            traceback.print_exc()



    def _create_zoom_button_area(self, parent):
        """创建区域3：图像控制（按红框划分的左右布局）"""
        # 标题
        title_label = tk.Label(parent, text="3. 图像控制",
                              font=('Arial', 10, 'bold'), bg='lightcoral')
        title_label.pack(fill='x', pady=(0, 2))

        # 创建主容器，使用水平布局
        main_container = tk.Frame(parent)
        main_container.pack(fill='both', expand=True, padx=2, pady=2)

        # 配置网格权重
        main_container.grid_rowconfigure(0, weight=1)
        main_container.grid_columnconfigure(0, weight=1)  # 左侧权重1
        main_container.grid_columnconfigure(1, weight=1)  # 右侧权重1

        # 左边红框：图层控制区域
        self.layer_control_container = tk.Frame(main_container, relief='ridge', bd=1)
        self.layer_control_container.grid(row=0, column=0, sticky='nsew', padx=(0, 1))

        # 创建图层控制区域
        self._create_layer_control_area(self.layer_control_container)

        # 右边红框：缩放按钮区域
        self.zoom_buttons_container = tk.Frame(main_container, relief='ridge', bd=1)
        self.zoom_buttons_container.grid(row=0, column=1, sticky='nsew', padx=(1, 0))

        # 创建缩放按钮区域
        self._create_zoom_buttons_area(self.zoom_buttons_container)



    def _create_layer_control_area(self, parent):
        """创建图层控制区域（左边红框）- 新版布局"""
        try:
            # 去掉"图层控制"标题，直接创建图层列表容器
            # 图层列表容器增加高度
            self.layer_list_frame = tk.Frame(parent)
            self.layer_list_frame.pack(fill='both', expand=True, padx=3, pady=5)

            # 初始化图层状态字典和顺序
            self.layer_states = {
                'cad_lines': tk.BooleanVar(value=True),
                'wall_fill': tk.BooleanVar(value=True),
                'furniture_fill': tk.BooleanVar(value=True),
                'room_fill': tk.BooleanVar(value=True)
            }

            # 初始化图层顺序列表
            self.layer_order = ['cad_lines', 'wall_fill', 'furniture_fill', 'room_fill']

            # 图层控制项目
            self.layer_items_data = [
                ('cad_lines', 'CAD线条', '#2196F3', '显示/隐藏CAD原始线条'),
                ('wall_fill', '墙体填充', '#4CAF50', '显示/隐藏墙体填充效果'),
                ('furniture_fill', '家具填充', '#FF9800', '显示/隐藏家具填充效果'),
                ('room_fill', '房间填充', '#9C27B0', '显示/隐藏房间填充效果')
            ]

            # 创建图层控制项
            self._create_layer_items()

            # 在底部添加应用设置按钮（移动到红框位置）
            apply_frame = tk.Frame(parent)
            apply_frame.pack(side='bottom', fill='x', padx=3, pady=5)

            apply_btn = tk.Button(apply_frame, text="⚙️ 应用设置",
                                command=self._apply_layer_settings,
                                bg='#FF5722', fg='white',
                                font=('Arial', 9, 'bold'),
                                height=2,
                                relief='raised', bd=2)
            apply_btn.pack(fill='x')

        except Exception as e:
            print(f"❌ 创建图层控制区域失败: {e}")
            error_label = tk.Label(parent, text=f"图层控制创建失败: {e}")
            error_label.pack(fill='both', expand=True)

    def _create_layer_items(self):
        """创建所有图层控制项"""
        try:
            # 清空现有项目
            for widget in self.layer_list_frame.winfo_children():
                widget.destroy()

            # 按当前顺序创建图层项
            for i, layer_key in enumerate(self.layer_order):
                # 找到对应的图层数据
                layer_data = None
                for data in self.layer_items_data:
                    if data[0] == layer_key:
                        layer_data = data
                        break

                if layer_data:
                    self._create_single_layer_item(i, layer_data)

        except Exception as e:
            print(f"❌ 创建图层项失败: {e}")

    def _create_single_layer_item(self, index, layer_data):
        """创建单个图层控制项（按红框示意调整）"""
        try:
            layer_key, layer_name, color, tooltip = layer_data

            # 图层项容器 - 增加高度，显示更加清晰
            item_frame = tk.Frame(self.layer_list_frame, relief='ridge', bd=1, bg='#FFFFFF')
            item_frame.pack(fill='x', pady=5, padx=3)  # 增加垂直间距

            # 主行：所有控件水平排布在一行 - 增加行高
            main_row = tk.Frame(item_frame, bg='#FFFFFF')
            main_row.pack(fill='x', padx=8, pady=10)  # 增加内边距，提高行高

            # 1. 圆形颜色指示器（不可点击，仅显示颜色）
            color_canvas = tk.Canvas(main_row, width=18, height=18, highlightthickness=0, bg='#FFFFFF')
            color_canvas.pack(side='left', padx=(0, 8))
            color_canvas.create_oval(2, 2, 16, 16, fill=color, outline='#333333', width=1)

            # 2. 图层名称标签 - 增大字体，显示更清晰
            name_label = tk.Label(main_row, text=layer_name, font=('Arial', 10, 'bold'),
                                bg='#FFFFFF', anchor='w', width=10)
            name_label.pack(side='left', padx=(0, 8))

            # 3. 下拉菜单（显示/隐藏）- 取消绿色按钮，改为下拉菜单
            layer_combo = ttk.Combobox(main_row, width=6, font=('Arial', 9),
                                     values=['显示', '隐藏'], state='readonly')
            layer_combo.set('显示' if self.layer_states[layer_key].get() else '隐藏')
            layer_combo.pack(side='left', padx=(0, 8))
            layer_combo.bind('<<ComboboxSelected>>',
                           lambda e: self._on_layer_combo_change(layer_key, layer_combo.get()))

            # 4. 控制按钮组 - 水平排布
            btn_colors = ['#4CAF50', '#2196F3', '#FF9800', '#9C27B0', '#607D8B', '#795548']
            btn_texts = ['设置', '编辑', '复制', '删除', '上移', '下移']

            for i, (btn_text, btn_color) in enumerate(zip(btn_texts, btn_colors)):
                # 根据按钮类型设置状态
                btn_state = 'normal'
                if btn_text == '上移' and index == 0:
                    btn_state = 'disabled'
                elif btn_text == '下移' and index == len(self.layer_order) - 1:
                    btn_state = 'disabled'

                btn = tk.Button(main_row, text=btn_text,
                              font=('Arial', 8), width=4, height=1,
                              bg=btn_color, fg='white', relief='raised', bd=1,
                              state=btn_state,
                              command=lambda t=btn_text, k=layer_key, idx=index: self._on_layer_button_click(k, t, idx))
                btn.pack(side='left', padx=1)

            # 添加工具提示
            def show_tooltip(event):
                print(f"💡 {layer_name}: {tooltip}")

            item_frame.bind("<Enter>", show_tooltip)

        except Exception as e:
            print(f"❌ 创建图层项 {layer_name} 失败: {e}")

    def _create_circular_toggle_button(self, parent, layer_key):
        """创建圆形显示/隐藏按钮"""
        try:
            # 创建画布用于绘制圆形按钮
            canvas = tk.Canvas(parent, width=20, height=20, highlightthickness=0)
            canvas.pack(side='left', padx=(0, 5))

            # 根据当前状态确定颜色
            is_visible = self.layer_states[layer_key].get()
            fill_color = '#4CAF50' if is_visible else '#9E9E9E'  # 绿色或灰色

            # 绘制圆形
            circle = canvas.create_oval(2, 2, 18, 18, fill=fill_color, outline='#333333', width=1)

            # 点击事件处理
            def on_click(event):
                self._toggle_layer_visibility(layer_key, canvas, circle)

            canvas.bind("<Button-1>", on_click)

            # 存储画布和圆形引用，用于后续更新
            if not hasattr(self, 'layer_toggle_buttons'):
                self.layer_toggle_buttons = {}
            self.layer_toggle_buttons[layer_key] = (canvas, circle)

        except Exception as e:
            print(f"❌ 创建圆形按钮失败: {e}")

    def _toggle_layer_visibility(self, layer_key, canvas, circle):
        """切换图层可见性并更新按钮外观"""
        try:
            # 切换状态
            current_state = self.layer_states[layer_key].get()
            new_state = not current_state
            self.layer_states[layer_key].set(new_state)

            # 更新按钮颜色
            fill_color = '#4CAF50' if new_state else '#9E9E9E'  # 绿色或灰色
            canvas.itemconfig(circle, fill=fill_color)

            # 调用原有的图层切换逻辑
            self._on_layer_toggle(layer_key)

        except Exception as e:
            print(f"❌ 切换图层可见性失败: {e}")

    def _move_layer_up(self, index):
        """向上移动图层"""
        try:
            if index > 0:
                # 交换顺序
                self.layer_order[index], self.layer_order[index - 1] = \
                    self.layer_order[index - 1], self.layer_order[index]

                # 重新创建图层项
                self._create_layer_items()

                print(f"📈 图层已向上移动: {self.layer_order}")

        except Exception as e:
            print(f"❌ 向上移动图层失败: {e}")

    def _move_layer_down(self, index):
        """向下移动图层"""
        try:
            if index < len(self.layer_order) - 1:
                # 交换顺序
                self.layer_order[index], self.layer_order[index + 1] = \
                    self.layer_order[index + 1], self.layer_order[index]

                # 重新创建图层项
                self._create_layer_items()

                print(f"📉 图层已向下移动: {self.layer_order}")

        except Exception as e:
            print(f"❌ 向下移动图层失败: {e}")

    def _on_layer_button_click(self, layer_key, button_text, index=None):
        """图层按钮点击事件处理"""
        try:
            print(f"🔘 图层 {layer_key} 的 {button_text} 按钮被点击")

            # 根据按钮类型执行不同的操作
            if button_text == '设置':
                self._open_layer_settings(layer_key)
            elif button_text == '编辑':
                self._edit_layer(layer_key)
            elif button_text == '复制':
                self._copy_layer(layer_key)
            elif button_text == '删除':
                self._delete_layer(layer_key)
            elif button_text == '上移':
                if index is not None:
                    self._move_layer_up(index)
            elif button_text == '下移':
                if index is not None:
                    self._move_layer_down(index)
            elif button_text == '更多':
                self._show_layer_more_options(layer_key)

        except Exception as e:
            print(f"❌ 图层按钮点击处理失败: {e}")

    def _open_layer_settings(self, layer_key):
        """打开图层设置"""
        print(f"⚙️ 打开图层 {layer_key} 的设置")
        # 这里可以添加具体的设置界面

    def _edit_layer(self, layer_key):
        """编辑图层"""
        print(f"✏️ 编辑图层 {layer_key}")
        # 这里可以添加具体的编辑功能

    def _copy_layer(self, layer_key):
        """复制图层"""
        print(f"📋 复制图层 {layer_key}")
        # 这里可以添加具体的复制功能

    def _delete_layer(self, layer_key):
        """删除图层"""
        print(f"🗑️ 删除图层 {layer_key}")
        # 这里可以添加具体的删除功能

    def _show_layer_more_options(self, layer_key):
        """显示图层更多选项"""
        print(f"📋 显示图层 {layer_key} 的更多选项")
        # 这里可以添加更多选项的菜单

    def _create_shadow_controls(self, parent, layer_key, layer_name):
        """创建阴影控制按钮"""
        try:
            # 阴影控制行
            shadow_row = tk.Frame(parent)
            shadow_row.pack(fill='x', padx=5, pady=(0, 3))

            # 阴影功能按钮
            buttons_frame = tk.Frame(shadow_row)
            buttons_frame.pack(side='left', fill='x', expand=True)

            # 添加阴影按钮
            add_shadow_btn = tk.Button(buttons_frame, text="添加阴影", font=('Arial', 7),
                                     command=lambda: self._add_layer_shadow(layer_key),
                                     bg='#4CAF50', fg='white', width=8)
            add_shadow_btn.pack(side='left', padx=(0, 2))

            # 删除阴影按钮
            remove_shadow_btn = tk.Button(buttons_frame, text="删除", font=('Arial', 7),
                                        command=lambda: self._remove_layer_shadow(layer_key),
                                        bg='#F44336', fg='white', width=6)
            remove_shadow_btn.pack(side='left', padx=(0, 2))

            # 隐藏阴影按钮
            hide_shadow_btn = tk.Button(buttons_frame, text="隐藏", font=('Arial', 7),
                                      command=lambda: self._hide_layer_shadow(layer_key),
                                      bg='#FF9800', fg='white', width=6)
            hide_shadow_btn.pack(side='left', padx=(0, 2))

            # 重新识别按钮
            reidentify_btn = tk.Button(buttons_frame, text="重新识别", font=('Arial', 7),
                                     command=lambda: self._reidentify_layer_shadow(layer_key),
                                     bg='#2196F3', fg='white', width=8)
            reidentify_btn.pack(side='left', padx=(0, 2))

            # 阴影参数控制
            params_frame = tk.Frame(shadow_row)
            params_frame.pack(side='right')

            # 阴影方向
            direction_frame = tk.Frame(params_frame)
            direction_frame.pack(side='left', padx=(0, 5))

            tk.Label(direction_frame, text="方向:", font=('Arial', 7)).pack(side='left')
            direction_var = tk.StringVar(value="315")
            direction_entry = tk.Entry(direction_frame, textvariable=direction_var,
                                     width=4, font=('Arial', 7))
            direction_entry.pack(side='left', padx=(2, 0))
            direction_entry.bind('<Return>', lambda e: self._update_shadow_direction(layer_key, direction_var.get()))

            # 阴影强度
            intensity_frame = tk.Frame(params_frame)
            intensity_frame.pack(side='left', padx=(0, 5))

            tk.Label(intensity_frame, text="强度:", font=('Arial', 7)).pack(side='left')
            intensity_var = tk.StringVar(value="0.3")
            intensity_entry = tk.Entry(intensity_frame, textvariable=intensity_var,
                                     width=4, font=('Arial', 7))
            intensity_entry.pack(side='left', padx=(2, 0))
            intensity_entry.bind('<Return>', lambda e: self._update_shadow_intensity(layer_key, intensity_var.get()))

            # 阴影长度
            length_frame = tk.Frame(params_frame)
            length_frame.pack(side='left')

            tk.Label(length_frame, text="长度:", font=('Arial', 7)).pack(side='left')
            length_var = tk.StringVar(value="10")
            length_entry = tk.Entry(length_frame, textvariable=length_var,
                                   width=4, font=('Arial', 7))
            length_entry.pack(side='left', padx=(2, 0))
            length_entry.bind('<Return>', lambda e: self._update_shadow_length(layer_key, length_var.get()))

            # 存储控件引用以便后续更新
            if not hasattr(self, 'shadow_controls'):
                self.shadow_controls = {}

            self.shadow_controls[layer_key] = {
                'add_btn': add_shadow_btn,
                'remove_btn': remove_shadow_btn,
                'hide_btn': hide_shadow_btn,
                'reidentify_btn': reidentify_btn,
                'direction_var': direction_var,
                'intensity_var': intensity_var,
                'length_var': length_var
            }

        except Exception as e:
            print(f"❌ 创建阴影控制失败: {e}")

    def _create_compact_shadow_controls(self, parent, layer_key):
        """创建紧凑的阴影控制按钮（水平布局）"""
        try:
            # 阴影功能按钮 - 紧凑版本
            add_btn = tk.Button(parent, text="阴影", font=('Arial', 6),
                              command=lambda: self._add_layer_shadow(layer_key),
                              bg='#4CAF50', fg='white', width=4, height=1)
            add_btn.pack(side='left', padx=(0, 1))

            remove_btn = tk.Button(parent, text="删", font=('Arial', 6),
                                 command=lambda: self._remove_layer_shadow(layer_key),
                                 bg='#F44336', fg='white', width=2, height=1)
            remove_btn.pack(side='left', padx=(0, 1))

            hide_btn = tk.Button(parent, text="隐", font=('Arial', 6),
                               command=lambda: self._hide_layer_shadow(layer_key),
                               bg='#FF9800', fg='white', width=2, height=1)
            hide_btn.pack(side='left', padx=(0, 1))

            reidentify_btn = tk.Button(parent, text="识", font=('Arial', 6),
                                     command=lambda: self._reidentify_layer_shadow(layer_key),
                                     bg='#2196F3', fg='white', width=2, height=1)
            reidentify_btn.pack(side='left', padx=(0, 3))

            # 阴影参数控制 - 紧凑版本
            # 方向
            tk.Label(parent, text="方:", font=('Arial', 6)).pack(side='left')
            direction_var = tk.StringVar(value="315")
            direction_entry = tk.Entry(parent, textvariable=direction_var,
                                     width=3, font=('Arial', 6))
            direction_entry.pack(side='left', padx=(1, 2))
            direction_entry.bind('<Return>', lambda e: self._update_shadow_direction(layer_key, direction_var.get()))

            # 强度
            tk.Label(parent, text="强:", font=('Arial', 6)).pack(side='left')
            intensity_var = tk.StringVar(value="0.3")
            intensity_entry = tk.Entry(parent, textvariable=intensity_var,
                                     width=3, font=('Arial', 6))
            intensity_entry.pack(side='left', padx=(1, 2))
            intensity_entry.bind('<Return>', lambda e: self._update_shadow_intensity(layer_key, intensity_var.get()))

            # 长度
            tk.Label(parent, text="长:", font=('Arial', 6)).pack(side='left')
            length_var = tk.StringVar(value="10")
            length_entry = tk.Entry(parent, textvariable=length_var,
                                   width=3, font=('Arial', 6))
            length_entry.pack(side='left', padx=(1, 0))
            length_entry.bind('<Return>', lambda e: self._update_shadow_length(layer_key, length_var.get()))

            # 存储控件引用
            if not hasattr(self, 'shadow_controls'):
                self.shadow_controls = {}

            self.shadow_controls[layer_key] = {
                'add_btn': add_btn,
                'remove_btn': remove_btn,
                'hide_btn': hide_btn,
                'reidentify_btn': reidentify_btn,
                'direction_var': direction_var,
                'intensity_var': intensity_var,
                'length_var': length_var
            }

        except Exception as e:
            print(f"❌ 创建紧凑阴影控制失败: {e}")

    def _add_layer_shadow(self, layer_key):
        """为图层添加阴影"""
        try:
            if not SHADOW_AVAILABLE or layer_key not in self.layer_shadows:
                print(f"⚠️ 阴影系统不可用或图层不存在: {layer_key}")
                return

            shadow_config = self.layer_shadows[layer_key]

            # 创建阴影生成器
            if shadow_config['type'] == 'vector':
                from shadow_generator import DirectionalShadowGenerator
                generator = DirectionalShadowGenerator(
                    light_angle=shadow_config['direction'],
                    max_offset=shadow_config['length'],
                    alpha=shadow_config['intensity']
                )
            elif shadow_config['type'] == 'raster':
                from shadow_generator import RasterShadowGenerator
                generator = RasterShadowGenerator(
                    offset_x=shadow_config['length'],
                    offset_y=shadow_config['length'],
                    blur_radius=int(shadow_config['length'] * 1.5)
                )
            else:
                from shadow_generator import VectorShadowGenerator
                generator = VectorShadowGenerator(
                    offset_x=shadow_config['length'],
                    offset_y=shadow_config['length'],
                    alpha=shadow_config['intensity']
                )

            shadow_config['generator'] = generator
            shadow_config['enabled'] = True

            print(f"✅ 已为图层 {layer_key} 添加阴影")

            # 刷新显示
            self._refresh_layer_display(layer_key)

        except Exception as e:
            print(f"❌ 添加图层阴影失败: {e}")

    def _remove_layer_shadow(self, layer_key):
        """删除图层阴影"""
        try:
            if layer_key in self.layer_shadows:
                self.layer_shadows[layer_key]['enabled'] = False
                self.layer_shadows[layer_key]['generator'] = None
                print(f"🗑️ 已删除图层 {layer_key} 的阴影")

                # 刷新显示
                self._refresh_layer_display(layer_key)

        except Exception as e:
            print(f"❌ 删除图层阴影失败: {e}")

    def _hide_layer_shadow(self, layer_key):
        """隐藏图层阴影"""
        try:
            if layer_key in self.layer_shadows:
                shadow_config = self.layer_shadows[layer_key]
                shadow_config['enabled'] = not shadow_config['enabled']

                status = "隐藏" if not shadow_config['enabled'] else "显示"
                print(f"👁️ 图层 {layer_key} 阴影已{status}")

                # 刷新显示
                self._refresh_layer_display(layer_key)

        except Exception as e:
            print(f"❌ 切换图层阴影可见性失败: {e}")

    def _reidentify_layer_shadow(self, layer_key):
        """重新识别图层阴影"""
        try:
            print(f"🔄 重新识别图层 {layer_key} 的阴影...")

            # 重新分析图层数据
            # 这里可以添加智能阴影识别逻辑

            # 暂时重新应用当前设置
            if layer_key in self.layer_shadows:
                self._add_layer_shadow(layer_key)

        except Exception as e:
            print(f"❌ 重新识别图层阴影失败: {e}")

    def _update_shadow_direction(self, layer_key, direction_str):
        """更新阴影方向"""
        try:
            direction = float(direction_str)
            if 0 <= direction <= 360:
                if layer_key in self.layer_shadows:
                    self.layer_shadows[layer_key]['direction'] = direction
                    print(f"🧭 图层 {layer_key} 阴影方向已更新: {direction}°")

                    # 重新创建阴影生成器
                    if self.layer_shadows[layer_key]['enabled']:
                        self._add_layer_shadow(layer_key)
            else:
                print(f"⚠️ 阴影方向必须在0-360度之间: {direction}")

        except ValueError:
            print(f"⚠️ 无效的阴影方向值: {direction_str}")
        except Exception as e:
            print(f"❌ 更新阴影方向失败: {e}")

    def _update_shadow_intensity(self, layer_key, intensity_str):
        """更新阴影强度"""
        try:
            intensity = float(intensity_str)
            if 0.0 <= intensity <= 1.0:
                if layer_key in self.layer_shadows:
                    self.layer_shadows[layer_key]['intensity'] = intensity
                    print(f"💪 图层 {layer_key} 阴影强度已更新: {intensity}")

                    # 重新创建阴影生成器
                    if self.layer_shadows[layer_key]['enabled']:
                        self._add_layer_shadow(layer_key)
            else:
                print(f"⚠️ 阴影强度必须在0.0-1.0之间: {intensity}")

        except ValueError:
            print(f"⚠️ 无效的阴影强度值: {intensity_str}")
        except Exception as e:
            print(f"❌ 更新阴影强度失败: {e}")

    def _update_shadow_length(self, layer_key, length_str):
        """更新阴影长度"""
        try:
            length = float(length_str)
            if length >= 0:
                if layer_key in self.layer_shadows:
                    self.layer_shadows[layer_key]['length'] = length
                    print(f"📏 图层 {layer_key} 阴影长度已更新: {length}")

                    # 重新创建阴影生成器
                    if self.layer_shadows[layer_key]['enabled']:
                        self._add_layer_shadow(layer_key)
            else:
                print(f"⚠️ 阴影长度必须大于等于0: {length}")

        except ValueError:
            print(f"⚠️ 无效的阴影长度值: {length_str}")
        except Exception as e:
            print(f"❌ 更新阴影长度失败: {e}")

    def _refresh_layer_display(self, layer_key):
        """刷新指定图层的显示"""
        try:
            # 这里可以添加具体的图层刷新逻辑
            print(f"🔄 刷新图层显示: {layer_key}")

            # 如果有可视化器，更新显示
            if hasattr(self, 'visualizer') and self.visualizer:
                # 重新绘制图形
                if hasattr(self, 'canvas') and self.canvas:
                    self.visualizer.update_canvas(self.canvas)

        except Exception as e:
            print(f"❌ 刷新图层显示失败: {e}")

    def _create_zoom_buttons_area(self, parent):
        """创建缩放按钮区域（右边红框）"""
        try:
            # 标题
            buttons_title = tk.Label(parent, text="视图控制",
                                   font=('Arial', 9, 'bold'), bg='#FFF8DC')
            buttons_title.pack(fill='x', pady=(2, 5))

            # 创建按钮容器，水平并列布置
            buttons_container = tk.Frame(parent)
            buttons_container.pack(expand=True, fill='both')

            # 居中放置按钮
            center_frame = tk.Frame(buttons_container)
            center_frame.place(relx=0.5, rely=0.5, anchor='center')

            # 三个按钮水平并列
            button_frame = tk.Frame(center_frame)
            button_frame.pack()

            # 缩放查看按钮
            zoom_btn = tk.Button(button_frame, text="🔍\n缩放查看",
                               command=self._open_zoom_window,
                               font=('Arial', 9, 'bold'),
                               bg='#FF9800', fg='white',
                               width=8, height=3,
                               relief='raised', bd=2)
            zoom_btn.pack(side='left', padx=2)

            # 适应窗口按钮
            fit_btn = tk.Button(button_frame, text="📐\n适应窗口",
                              command=self._fit_to_window,
                              font=('Arial', 9, 'bold'),
                              bg='#4CAF50', fg='white',
                              width=8, height=3,
                              relief='raised', bd=2)
            fit_btn.pack(side='left', padx=2)

            # 重置视图按钮
            reset_btn = tk.Button(button_frame, text="🔄\n重置视图",
                                command=self._reset_view,
                                font=('Arial', 9, 'bold'),
                                bg='#2196F3', fg='white',
                                width=8, height=3,
                                relief='raised', bd=2)
            reset_btn.pack(side='left', padx=2)



            # 底部提示
            tip_frame = tk.Frame(parent)
            tip_frame.pack(side='bottom', fill='x', pady=2)

            tip_label = tk.Label(tip_frame, text="点击按钮控制视图和图层设置",
                               font=('Arial', 7), fg='gray')
            tip_label.pack()



        except Exception as e:
            print(f"❌ 创建缩放按钮区域失败: {e}")
            error_label = tk.Label(parent, text=f"按钮区域创建失败: {e}")
            error_label.pack(fill='both', expand=True)

    def _on_layer_toggle(self, layer_key):
        """图层显示切换事件处理"""
        try:
            if not hasattr(self, 'layer_states'):
                return

            is_visible = self.layer_states[layer_key].get()
            layer_names = {
                'cad_lines': 'CAD线条',
                'wall_fill': '墙体填充',
                'furniture_fill': '家具填充',
                'room_fill': '房间填充'
            }

            layer_name = layer_names.get(layer_key, layer_key)
            status = "显示" if is_visible else "隐藏"

            print(f"🔄 {layer_name} 已{status}")

            # 应用图层变化
            self._apply_layer_visibility(layer_key, is_visible)

        except Exception as e:
            print(f"❌ 图层切换失败: {e}")

    def _on_layer_order_change(self, event):
        """图层顺序变化事件处理"""
        try:
            if not hasattr(self, 'layer_order_var'):
                return

            new_order = self.layer_order_var.get()
            print(f"📋 图层顺序已更改为: {new_order}")

            # 应用新的图层顺序
            self._apply_layer_order(new_order)

        except Exception as e:
            print(f"❌ 图层顺序更改失败: {e}")

    def _apply_layer_settings(self):
        """应用图层设置（新版 - 基于图层顺序列表）"""
        try:
            print("🔧 应用图层设置...")

            # 应用所有图层的可见性设置
            if hasattr(self, 'layer_states'):
                for layer_key, state_var in self.layer_states.items():
                    is_visible = state_var.get()
                    self._apply_layer_visibility(layer_key, is_visible)

            # 应用图层顺序（使用新的顺序列表）
            if hasattr(self, 'layer_order'):
                self._apply_layer_order_from_list(self.layer_order)

            # 刷新显示
            self._refresh_all_views()

            messagebox.showinfo("成功", "图层设置已应用")

        except Exception as e:
            print(f"❌ 应用图层设置失败: {e}")
            messagebox.showerror("错误", f"应用图层设置失败: {e}")

    def _apply_layer_order_from_list(self, layer_order):
        """根据图层顺序列表应用图层顺序"""
        try:
            # 将图层键转换为显示名称
            layer_names = []
            for layer_key in layer_order:
                for data in self.layer_items_data:
                    if data[0] == layer_key:
                        layer_names.append(data[1])
                        break

            order_text = ' → '.join(layer_names)
            print(f"📊 应用图层顺序: {order_text}")

            # 这里可以根据具体的绘图逻辑来实现图层顺序
            # 例如：控制matplotlib中不同图层的z-order

        except Exception as e:
            print(f"❌ 应用图层顺序失败: {e}")

    def _apply_layer_visibility(self, layer_key, is_visible):
        """应用图层可见性设置"""
        try:
            # 根据图层类型应用不同的可见性逻辑
            if layer_key == 'cad_lines':
                self._toggle_cad_lines_visibility(is_visible)
            elif layer_key == 'wall_fill':
                self._toggle_wall_fill_visibility(is_visible)
            elif layer_key == 'furniture_fill':
                self._toggle_furniture_fill_visibility(is_visible)
            elif layer_key == 'room_fill':
                self._toggle_room_fill_visibility(is_visible)

        except Exception as e:
            print(f"❌ 应用图层可见性失败 {layer_key}: {e}")

    def _apply_layer_order(self, order_text):
        """应用图层顺序设置"""
        try:
            # 解析顺序文本
            if "CAD线条 → 墙体 → 家具 → 房间" in order_text:
                order = ['cad_lines', 'wall_fill', 'furniture_fill', 'room_fill']
            elif "CAD线条 → 房间 → 墙体 → 家具" in order_text:
                order = ['cad_lines', 'room_fill', 'wall_fill', 'furniture_fill']
            elif "房间 → 墙体 → 家具 → CAD线条" in order_text:
                order = ['room_fill', 'wall_fill', 'furniture_fill', 'cad_lines']
            elif "墙体 → 房间 → 家具 → CAD线条" in order_text:
                order = ['wall_fill', 'room_fill', 'furniture_fill', 'cad_lines']
            else:
                order = ['cad_lines', 'wall_fill', 'furniture_fill', 'room_fill']  # 默认顺序

            # 应用图层顺序（这里可以根据具体的绘图逻辑来实现）
            print(f"📊 应用图层顺序: {' → '.join(order)}")

            # 存储当前顺序供绘图时使用
            self.current_layer_order = order

        except Exception as e:
            print(f"❌ 应用图层顺序失败: {e}")

    def _toggle_cad_lines_visibility(self, is_visible):
        """切换CAD线条可见性"""
        try:
            # 这里实现CAD线条的显示/隐藏逻辑
            print(f"🔧 CAD线条可见性: {'显示' if is_visible else '隐藏'}")
            # 可以通过修改matplotlib的线条对象的visible属性来实现

        except Exception as e:
            print(f"❌ 切换CAD线条可见性失败: {e}")

    def _toggle_wall_fill_visibility(self, is_visible):
        """切换墙体填充可见性"""
        try:
            print(f"🏠 墙体填充可见性: {'显示' if is_visible else '隐藏'}")
            # 这里可以控制墙体填充的显示/隐藏

        except Exception as e:
            print(f"❌ 切换墙体填充可见性失败: {e}")

    def _toggle_furniture_fill_visibility(self, is_visible):
        """切换家具填充可见性"""
        try:
            # 这里可以控制家具填充的显示/隐藏
            pass

        except Exception as e:
            print(f"❌ 切换家具填充可见性失败: {e}")

    def _toggle_room_fill_visibility(self, is_visible):
        """切换房间填充可见性"""
        try:
            print(f"🏡 房间填充可见性: {'显示' if is_visible else '隐藏'}")
            # 这里可以控制房间填充的显示/隐藏

        except Exception as e:
            print(f"❌ 切换房间填充可见性失败: {e}")

    def _refresh_all_views(self):
        """刷新所有视图"""
        try:
            # 刷新详细视图
            if hasattr(self, 'detail_canvas') and self.detail_canvas:
                self.detail_canvas.draw()

            # 刷新概览视图
            if hasattr(self, 'overview_canvas') and self.overview_canvas:
                self.overview_canvas.draw()

            # 刷新房间布局视图
            if hasattr(self, 'room_layout_canvas') and self.room_layout_canvas:
                self.room_layout_canvas.draw()

            print("🔄 所有视图已刷新")

        except Exception as e:
            print(f"❌ 刷新视图失败: {e}")

    def _create_original_zoom_buttons(self, parent):
        """创建原有的缩放按钮功能"""
        try:
            # 主缩放按钮（保持原有样式）
            main_zoom_frame = tk.Frame(parent)
            main_zoom_frame.pack(expand=True, fill='both')

            # 居中放置主按钮
            center_frame = tk.Frame(main_zoom_frame)
            center_frame.place(relx=0.5, rely=0.4, anchor='center')

            # 主缩放按钮
            main_zoom_btn = tk.Button(center_frame, text="🔍\n缩放查看",
                                     command=self._open_zoom_window,
                                     font=('Arial', 11, 'bold'),
                                     bg='#FF9800', fg='white',
                                     width=8, height=2,
                                     relief='raised', bd=2)
            main_zoom_btn.pack(pady=2)

            # 辅助按钮行
            aux_frame = tk.Frame(center_frame)
            aux_frame.pack(pady=3)

            # 适应窗口按钮
            fit_btn = tk.Button(aux_frame, text="适应窗口",
                               command=self._fit_to_window,
                               font=('Arial', 8), bg='#4CAF50', fg='white',
                               width=7, height=1)
            fit_btn.pack(side='left', padx=1)

            # 重置视图按钮
            reset_btn = tk.Button(aux_frame, text="重置视图",
                                 command=self._reset_view,
                                 font=('Arial', 8), bg='#2196F3', fg='white',
                                 width=7, height=1)
            reset_btn.pack(side='left', padx=1)

            # 底部提示
            tip_frame = tk.Frame(parent)
            tip_frame.pack(side='bottom', fill='x', pady=2)

            tip_label = tk.Label(tip_frame, text="点击缩放查看大图",
                               font=('Arial', 7), fg='gray')
            tip_label.pack()



        except Exception as e:
            print(f"创建原有缩放按钮失败: {e}")
            # 创建简单的错误显示
            error_label = tk.Label(parent, text=f"缩放功能加载失败: {e}",
                                 font=('Arial', 8), fg='red')
            error_label.pack(pady=10)

    def _fit_to_window(self):
        """适应窗口"""
        try:
            if hasattr(self, 'detail_ax') and self.detail_ax:
                self.detail_ax.relim()
                self.detail_ax.autoscale_view()
                self.detail_canvas.draw()

            if hasattr(self, 'overview_ax') and self.overview_ax:
                self.overview_ax.relim()
                self.overview_ax.autoscale_view()
                self.overview_canvas.draw()

            print("✅ 视图已适应窗口")
        except Exception as e:
            print(f"适应窗口失败: {e}")

    def _reset_view(self):
        """重置视图"""
        try:
            if hasattr(self, 'detail_ax') and self.detail_ax:
                self.detail_ax.clear()
                self.detail_ax.set_title('当前组详细视图', fontsize=10)
                self.detail_ax.set_aspect('equal', adjustable='box')
                self.detail_canvas.draw()

            if hasattr(self, 'overview_ax') and self.overview_ax:
                self.overview_ax.clear()
                self.overview_ax.set_title('全图概览视图', fontsize=10)
                self.overview_ax.set_aspect('equal', adjustable='box')
                self.overview_canvas.draw()

            print("✅ 视图已重置")
        except Exception as e:
            print(f"重置视图失败: {e}")

    def _setup_visualizer_compatibility(self):
        """设置可视化器兼容性（恢复原有的单一画布模式）"""
        try:
            # 检查可视化器是否已经在detail_view中创建
            if hasattr(self, 'visualizer') and self.visualizer:


                # 确保轴对象引用正确
                if hasattr(self.visualizer, 'ax_detail'):
                    self.detail_ax = self.visualizer.ax_detail
                if hasattr(self.visualizer, 'ax_overview'):
                    self.overview_ax = self.visualizer.ax_overview


            else:
                print("⚠️ 可视化器未创建，将在detail_view中创建")

        except Exception as e:
            print(f"设置可视化器兼容性失败: {e}")
            import traceback
            traceback.print_exc()

    def _update_visualization_display(self):
        """更新可视化显示（恢复原有的单一画布模式）"""
        try:
            # 使用原有的单一画布刷新方式
            if hasattr(self, 'canvas') and self.canvas:
                self.canvas.draw()
                print("✅ 可视化显示更新完成（原有模式）")
            else:
                print("⚠️ 画布未初始化")

        except Exception as e:
            print(f"更新可视化显示失败: {e}")

    def _create_color_system_with_zoom(self, parent):
        """创建配色系统和缩放按钮（问题3实现）"""
        # 创建水平布局框架
        control_frame = tk.Frame(parent)
        control_frame.pack(fill='x')

        # 左侧：配色系统（调用父类方法）
        color_left_frame = tk.Frame(control_frame)
        color_left_frame.pack(side='left', fill='x', expand=True)

        try:
            # 调用父类的配色系统创建方法
            super()._create_color_system(color_left_frame)
        except:
            # 如果父类方法不存在，创建简单的配色显示
            tk.Label(color_left_frame, text="配色系统", font=('Arial', 10)).pack()

        # 右侧：缩放按钮
        zoom_frame = tk.Frame(control_frame)
        zoom_frame.pack(side='right', padx=(10, 0))

        # 缩放按钮
        zoom_btn = tk.Button(zoom_frame, text="🔍 缩放",
                            command=self._open_zoom_window,
                            bg='#FF9800', fg='white',
                            font=('Arial', 9, 'bold'),
                            width=8, height=1)
        zoom_btn.pack()

        # 添加提示标签
        tip_label = tk.Label(zoom_frame, text="点击查看大图",
                           font=('Arial', 8), fg='gray')
        tip_label.pack()

    def _open_zoom_window(self):
        """打开缩放窗口（问题3核心功能）"""
        try:
            if not hasattr(self, 'visualizer') or not self.visualizer:
                messagebox.showwarning("警告", "可视化器未初始化")
                return

            # 创建缩放窗口（调整为700x700）
            zoom_window = tk.Toplevel(self.root)
            zoom_window.title("CAD全图缩放视图")
            zoom_window.geometry("800x800")  # 稍大一些以容纳工具栏
            zoom_window.resizable(True, True)

            # 创建缩放窗口的内容
            self._create_zoom_window_content(zoom_window)

        except Exception as e:
            print(f"打开缩放窗口失败: {e}")
            messagebox.showerror("错误", f"打开缩放窗口失败: {e}")

    def _create_zoom_window_content(self, window):
        """创建缩放窗口内容"""
        try:
            import matplotlib.pyplot as plt
            from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
            import numpy as np

            # 创建主框架
            main_frame = tk.Frame(window)
            main_frame.pack(fill='both', expand=True, padx=10, pady=10)

            # 标题
            title_label = tk.Label(main_frame, text="CAD全图缩放视图 - 可放大缩小",
                                 font=('Arial', 14, 'bold'))
            title_label.pack(pady=(0, 10))

            # 创建matplotlib图形（调整为正方形）
            fig, ax = plt.subplots(figsize=(10, 10))  # 正方形图形
            fig.patch.set_facecolor('white')

            # 复制当前全图视图的内容（去除颜色索引）
            self._copy_overview_to_zoom_figure(ax, fig)

            # 创建画布
            canvas = FigureCanvasTkAgg(fig, main_frame)
            canvas_widget = canvas.get_tk_widget()
            canvas_widget.pack(fill='both', expand=True)

            # 添加导航工具栏（支持缩放、平移等）
            toolbar_frame = tk.Frame(main_frame)
            toolbar_frame.pack(fill='x', pady=(5, 0))

            toolbar = NavigationToolbar2Tk(canvas, toolbar_frame)
            toolbar.update()

            # 添加控制按钮
            control_frame = tk.Frame(main_frame)
            control_frame.pack(fill='x', pady=(5, 0))

            # 重置视图按钮
            reset_btn = tk.Button(control_frame, text="重置视图",
                                command=lambda: self._reset_zoom_view(ax, canvas),
                                bg='#4CAF50', fg='white', font=('Arial', 9))
            reset_btn.pack(side='left', padx=(0, 5))

            # 适应窗口按钮
            fit_btn = tk.Button(control_frame, text="适应窗口",
                              command=lambda: self._fit_zoom_view(ax, canvas),
                              bg='#2196F3', fg='white', font=('Arial', 9))
            fit_btn.pack(side='left', padx=(5, 0))

            # 关闭按钮
            close_btn = tk.Button(control_frame, text="关闭",
                                command=window.destroy,
                                bg='#f44336', fg='white', font=('Arial', 9))
            close_btn.pack(side='right')

            # 显示提示信息
            tip_label = tk.Label(main_frame,
                               text="使用工具栏进行缩放、平移操作，或使用鼠标滚轮缩放",
                               font=('Arial', 9), fg='gray')
            tip_label.pack(pady=(5, 0))

            print("✅ 缩放窗口创建成功")

        except Exception as e:
            print(f"创建缩放窗口内容失败: {e}")
            error_label = tk.Label(window, text=f"创建缩放窗口失败: {e}")
            error_label.pack(fill='both', expand=True)

    def _copy_overview_to_zoom_figure(self, ax, fig=None):
        """复制全图概览到缩放图形（去除颜色索引）"""
        try:
            if not hasattr(self, 'visualizer') or not self.visualizer:
                ax.text(0.5, 0.5, '无可视化数据', ha='center', va='center', fontsize=16)
                return

            # 获取原始全图视图的数据
            overview_ax = self.visualizer.ax_overview

            # 复制所有线条和图形元素
            for line in overview_ax.get_lines():
                # 复制线条，但使用统一的颜色（去除颜色索引）
                xdata = line.get_xdata()
                ydata = line.get_ydata()
                ax.plot(xdata, ydata, color='black', linewidth=1.0, alpha=0.8)

            # 复制所有补丁（patches）
            for patch in overview_ax.patches:
                # 复制补丁，但使用统一的颜色
                import matplotlib.patches as mpatches
                if hasattr(patch, 'get_path'):
                    new_patch = mpatches.PathPatch(patch.get_path(),
                                                 facecolor='lightgray',
                                                 edgecolor='black',
                                                 alpha=0.6)
                    ax.add_patch(new_patch)

            # 设置相同的坐标范围并确保居中显示
            ax.set_xlim(overview_ax.get_xlim())
            ax.set_ylim(overview_ax.get_ylim())
            ax.set_aspect('equal', adjustable='box')  # 确保纵横比相等并居中
            ax.grid(True, alpha=0.3)
            ax.set_title('CAD全图视图 - 无颜色索引', fontsize=14, fontweight='bold')

            # 调整布局确保图形居中（如果提供了fig参数）
            if fig:
                fig.tight_layout(pad=2.0)

        except Exception as e:
            print(f"复制全图视图失败: {e}")
            ax.text(0.5, 0.5, f'复制视图失败: {e}', ha='center', va='center', fontsize=12)

    def _reset_zoom_view(self, ax, canvas):
        """重置缩放视图"""
        try:
            ax.autoscale()
            canvas.draw()
        except Exception as e:
            print(f"重置视图失败: {e}")

    def _fit_zoom_view(self, ax, canvas):
        """适应窗口视图"""
        try:
            ax.relim()
            ax.autoscale_view()
            canvas.draw()
        except Exception as e:
            print(f"适应窗口失败: {e}")

    def on_group_click(self, event):
        """处理组列表单击事件（重写以改进填充按钮逻辑）"""
        try:
            # 获取点击的位置
            region = self.group_tree.identify_region(event.x, event.y)
            if region == "cell":
                # 获取点击的列
                column = self.group_tree.identify_column(event.x)
                if column == '#4':  # 填充列
                    # 获取选中的项目
                    item = self.group_tree.identify_row(event.y)
                    if item:
                        # 获取组索引
                        group_text = self.group_tree.item(item, 'text')
                        if group_text.startswith('组'):
                            group_index = int(group_text[1:]) - 1

                            # 检查填充状态，决定是否响应点击
                            fill_status = self._get_group_fill_status(group_index)

                            if "▢" in fill_status:  # 不可填充状态
                                # 获取组状态信息
                                if (hasattr(self.processor, 'groups_info') and
                                    self.processor.groups_info and
                                    group_index < len(self.processor.groups_info)):

                                    group_info = self.processor.groups_info[group_index]
                                    status = group_info.get('status', 'unlabeled')

                                    status_text = {
                                        'unlabeled': '未标注',
                                        'labeling': '标注中',
                                        'pending': '待处理'
                                    }.get(status, '未知状态')

                                    messagebox.showinfo("提示",
                                        f"组 {group_index + 1} 当前状态为: {status_text}\n"
                                        f"只有已标注的组才能进行填充")
                                else:
                                    messagebox.showinfo("提示", "该组当前不可填充")
                            else:
                                # 可填充或已填充状态，执行填充操作
                                self.fill_group(group_index)
        except Exception as e:
            print(f"处理组点击事件失败: {e}")
            import traceback
            traceback.print_exc()

    def _get_fill_tag(self, group_index, fill_status):
        """获取填充状态的标签（重写以改进状态标识）"""
        if "●" in fill_status:
            return "filled"
        elif "□" in fill_status:
            return "fillable"
        else:
            return "unfillable"

    def _set_fill_column_color(self, item_id, group_index, fill_status):
        """为填充列设置颜色（重写以改进视觉效果）"""
        try:
            # 根据填充状态设置不同的显示样式
            if "●" in fill_status:
                # 已填充：绿色背景
                self.group_tree.set(item_id, '填充', fill_status)
                self.group_tree.tag_configure(f'filled_{group_index}', background='lightgreen')

                # 获取当前标签并添加填充标签
                current_tags = list(self.group_tree.item(item_id, 'tags'))
                current_tags.append(f'filled_{group_index}')
                self.group_tree.item(item_id, tags=current_tags)

            elif "□" in fill_status:
                # 可填充：浅蓝色背景
                self.group_tree.set(item_id, '填充', fill_status)
                self.group_tree.tag_configure(f'fillable_{group_index}', background='lightblue')

                # 获取当前标签并添加可填充标签
                current_tags = list(self.group_tree.item(item_id, 'tags'))
                current_tags.append(f'fillable_{group_index}')
                self.group_tree.item(item_id, tags=current_tags)

            else:
                # 不可填充：灰色背景
                self.group_tree.set(item_id, '填充', fill_status)
                self.group_tree.tag_configure(f'unfillable_{group_index}', background='lightgray', foreground='gray')

                # 获取当前标签并添加不可填充标签
                current_tags = list(self.group_tree.item(item_id, 'tags'))
                current_tags.append(f'unfillable_{group_index}')
                self.group_tree.item(item_id, tags=current_tags)

        except Exception as e:
            print(f"设置填充列颜色失败: {e}")







    def visualize_overview(self, all_entities, current_group_entities=None, labeled_entities=None,
                          processor=None, current_group_index=None, wall_fills=None, wall_fill_processor=None):
        """可视化全图概览（增强版：改进坐标系处理）"""
        try:
            # 调用父类方法
            super().visualize_overview(all_entities, current_group_entities, labeled_entities,
                                     processor, current_group_index, wall_fills, wall_fill_processor)

            # 增强坐标范围计算
            self._enhance_coordinate_range(all_entities)

        except Exception as e:
            print(f"可视化全图概览失败: {e}")
            import traceback
            traceback.print_exc()

    def _enhance_coordinate_range(self, entities):
        """增强坐标范围计算"""
        try:
            if not entities:
                return

            # 收集所有坐标点
            all_x = []
            all_y = []

            for entity in entities:
                coords = self._extract_entity_coordinates(entity)
                if coords:
                    for x, y in coords:
                        if isinstance(x, (int, float)) and isinstance(y, (int, float)):
                            all_x.append(x)
                            all_y.append(y)

            if all_x and all_y:
                # 计算坐标范围
                min_x, max_x = min(all_x), max(all_x)
                min_y, max_y = min(all_y), max(all_y)

                # 添加边距
                margin_x = (max_x - min_x) * 0.1 if max_x != min_x else 10
                margin_y = (max_y - min_y) * 0.1 if max_y != min_y else 10

                # 设置坐标范围
                if hasattr(self, 'visualizer') and hasattr(self.visualizer, 'ax_overview'):
                    self.visualizer.ax_overview.set_xlim(min_x - margin_x, max_x + margin_x)
                    self.visualizer.ax_overview.set_ylim(min_y - margin_y, max_y + margin_y)

                    print(f"坐标范围: X({min_x:.2f}, {max_x:.2f}), Y({min_y:.2f}, {max_y:.2f})")

        except Exception as e:
            print(f"增强坐标范围计算失败: {e}")

    def _extract_entity_coordinates(self, entity):
        """提取实体的坐标点"""
        try:
            coords = []

            if entity['type'] in ['LINE', 'LWPOLYLINE', 'POLYLINE']:
                if 'points' in entity:
                    coords.extend(entity['points'])
            elif entity['type'] == 'CIRCLE':
                if 'center' in entity and 'radius' in entity:
                    center = entity['center']
                    radius = entity['radius']
                    # 添加圆的边界点
                    coords.extend([
                        (center[0] - radius, center[1] - radius),
                        (center[0] + radius, center[1] + radius)
                    ])
            elif entity['type'] == 'ARC':
                if 'center' in entity and 'radius' in entity:
                    center = entity['center']
                    radius = entity['radius']
                    # 添加圆弧的边界点
                    coords.extend([
                        (center[0] - radius, center[1] - radius),
                        (center[0] + radius, center[1] + radius)
                    ])
            elif entity['type'] == 'ELLIPSE':
                if 'center' in entity and 'major_axis' in entity and 'ratio' in entity:
                    center = entity['center']
                    major_axis = entity['major_axis']
                    ratio = entity['ratio']
                    # 简化处理：使用长轴作为边界
                    major_length = (major_axis[0]**2 + major_axis[1]**2)**0.5
                    minor_length = major_length * ratio
                    max_radius = max(major_length, minor_length)
                    coords.extend([
                        (center[0] - max_radius, center[1] - max_radius),
                        (center[0] + max_radius, center[1] + max_radius)
                    ])

            return coords

        except Exception as e:
            print(f"提取实体坐标失败: {e}")
            return []



    def _ensure_processor_initialized(self):
        """确保处理器在初始化时就存在"""
        try:
            if not hasattr(self, 'processor') or not self.processor:
                print("🔧 初始化时创建处理器")
                from main_enhanced import EnhancedCADProcessor
                self.processor = EnhancedCADProcessor(self.visualizer, self.canvas)
                
                # 设置回调
                if hasattr(self, 'on_status_update') and hasattr(self, 'on_progress_update'):
                    self.processor.set_callbacks(self.on_status_update, self.on_progress_update)
                
                # 初始化类别映射
                if not hasattr(self.processor, 'category_mapping') or not self.processor.category_mapping:
                    self.processor.category_mapping = {
                        'wall': '墙体',
                        'door_window': '门窗', 
                        'other': '其他'
                    }
                
                print("✅ 处理器初始化完成")
            else:
                print("✅ 处理器已存在")
                
        except Exception as e:
            print(f"❌ 处理器初始化失败: {e}")

    def _clean_group_data(self, group):
        """清理组数据，确保只包含有效的实体字典"""
        print(f"🔧 [DEBUG] _clean_group_data 调用:")
        print(f"  输入类型: {type(group)}")
        print(f"  输入长度: {len(group) if hasattr(group, '__len__') else 'N/A'}")

        if isinstance(group, dict):
            entities = group.get('entities', [])
            print(f"  从字典提取entities: {len(entities)} 个")
        elif isinstance(group, list):
            entities = group
            print(f"  直接使用列表: {len(entities)} 个")
        else:
            print(f"  无效输入类型，返回空列表")
            return []

        # 过滤并清理实体
        cleaned_entities = []
        invalid_count = 0
        for i, entity in enumerate(entities):
            if isinstance(entity, dict) and entity.get('type') and entity.get('layer'):
                cleaned_entities.append(entity)
                if i < 3:  # 只显示前3个有效实体的详情
                    print(f"    ✅ 有效实体[{i}]: {entity.get('type')} - {entity.get('layer')}")
            else:
                invalid_count += 1
                if invalid_count <= 3:  # 只显示前3个无效实体的详情
                    print(f"    ⚠️ 跳过无效实体[{i}]: {type(entity)} - {str(entity)[:50]}")

        if invalid_count > 3:
            print(f"    ⚠️ ... 还有 {invalid_count - 3} 个无效实体被跳过")

        print(f"  清理结果: {len(cleaned_entities)} 个有效实体")
        return cleaned_entities

    def _ensure_processor_exists(self, context="未知"):
        """确保处理器存在，如果不存在则尝试恢复或创建"""
        if not self.processor:
            print(f"🔧 处理器不存在 ({context})，尝试恢复...")
            
            # 尝试从缓存恢复
            if hasattr(self, 'current_file') and self.current_file:
                success = self._restore_processor_from_current_file()
                if success:
                    print(f"  ✅ 从缓存恢复处理器成功")
                    return True
            
            # 创建新处理器
            print(f"  🔄 创建新处理器")
            from main_enhanced import EnhancedCADProcessor
            self.processor = EnhancedCADProcessor(self.visualizer, self.canvas)
            
            # 设置回调
            if hasattr(self, 'on_status_update') and hasattr(self, 'on_progress_update'):
                self.processor.set_callbacks(self.on_status_update, self.on_progress_update)
            
            # 初始化类别映射
            if not hasattr(self.processor, 'category_mapping') or not self.processor.category_mapping:
                self.processor.category_mapping = {
                    'wall': '墙体',
                    'door_window': '门窗', 
                    'other': '其他'
                }
            
            return True
        
        return True

    def start_relabel_mode(self, group_index):
        """开始重新分类模式"""
        if not self.processor:
            messagebox.showwarning("警告", "请先开始处理文件")
            return

        # 检查是否有可重新分类的组
        if not self.processor.can_relabel_group(group_index):
            messagebox.showwarning("警告", "该组无法重新分类")
            return

        # 进入重新分类模式
        success = self.processor.start_relabel_group(group_index)
        if success:
            # 更新状态显示
            self.status_var.set(f"正在重新分类第 {group_index} 组")

            # 创建分类选择对话框
            self.show_relabel_dialog(group_index)
        else:
            messagebox.showerror("错误", "无法进入重新分类模式")

    def show_relabel_dialog(self, group_index):
        """显示重新分类对话框"""
        dialog = tk.Toplevel(self.root)
        dialog.title(f"重新分类 - 组{group_index}")
        dialog.geometry("400x500")
        dialog.transient(self.root)
        dialog.grab_set()

        # 居中显示
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
        y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
        dialog.geometry(f"+{x}+{y}")

        # 获取组信息
        group_info = self.processor.get_group_info(group_index)

        # 标题
        title_label = Label(dialog, text=f"重新分类 - 组{group_index}",
                           font=('Arial', 12, 'bold'))
        title_label.pack(pady=10)

        # 组信息
        info_frame = Frame(dialog)
        info_frame.pack(fill='x', padx=20, pady=5)

        Label(info_frame, text=f"实体数量: {group_info['entity_count']}",
              font=('Arial', 10)).pack(anchor='w')
        Label(info_frame, text=f"当前分类: {group_info.get('current_type', '未知')}",
              font=('Arial', 10)).pack(anchor='w')

        # 分类选择
        selection_frame = Frame(dialog)
        selection_frame.pack(fill='both', expand=True, padx=20, pady=10)

        Label(selection_frame, text="选择新的分类:", font=('Arial', 11, 'bold')).pack(anchor='w', pady=(0, 10))

        # 分类选项
        selected_label = tk.StringVar()
        categories = [
            ('wall', '墙体', '#FF6B6B'),
            ('door_window', '门窗', '#4ECDC4'),
            ('other', '其他', '#45B7D1')
        ]

        for label, name, color in categories:
            frame = Frame(selection_frame)
            frame.pack(fill='x', pady=2)

            radio = Radiobutton(frame, text=name, variable=selected_label, value=label,
                               font=('Arial', 10), bg=color, fg='white',
                               selectcolor=color, activebackground=color)
            radio.pack(side='left', fill='x', expand=True)

        # 按钮
        btn_frame = Frame(dialog)
        btn_frame.pack(fill='x', padx=20, pady=10)

        Button(btn_frame, text="确定",
               command=lambda: self.apply_relabel(group_index, selected_label.get(), dialog),
               bg='#4CAF50', fg='white', font=('Arial', 9)).pack(side='right')

        Button(btn_frame, text="取消", command=dialog.destroy,
               bg='#9E9E9E', fg='white', font=('Arial', 9)).pack(side='right', padx=(5, 0))

    def apply_relabel(self, group_index, new_label, dialog):
        """应用重新分类"""
        if not self.processor:
            return

        if not new_label:
            messagebox.showwarning("警告", "请选择一个分类")
            return

        # 执行重新分类
        success = self.processor.relabel_group(group_index, new_label)

        if success:
            # 安全获取类型映射
            if hasattr(self.processor, 'category_mapping') and self.processor.category_mapping:
                category_name = self.processor.category_mapping.get(new_label, new_label)
            else:
                category_name = new_label
            self.status_var.set(f"组{group_index} 已重新分类为: {category_name}")

            # 关闭对话框
            dialog.destroy()

            # 检查是否还有未完成的分类
            self.check_and_continue_labeling()

            # 更新组列表
            self.update_group_list()

            # 更新可视化显示
            if self.processor and self.processor.visualizer and self.processor.canvas:
                try:
                    self.processor.visualizer.visualize_overview(
                        self.processor.current_file_entities,
                        [],  # 没有当前处理组
                        self.processor.auto_labeled_entities + self.processor.labeled_entities
                    )
                    self.processor.visualizer.update_canvas(self.processor.canvas)
                except Exception as e:
                    print(f"重新分类后可视化更新失败: {e}")
        else:
            messagebox.showerror("错误", "重新分类失败")

def main_v2():
    """完整V2版本主程序入口（仅外轮廓填充）"""
    try:
        # 设置matplotlib后端
        import matplotlib
        matplotlib.use('TkAgg')
        
        root = tk.Tk()
        app = EnhancedCADAppV2(root)
        
        root.mainloop()
        
    except Exception as e:
        print(f"完整V2程序启动失败: {e}")

if __name__ == "__main__":
    main_v2() 